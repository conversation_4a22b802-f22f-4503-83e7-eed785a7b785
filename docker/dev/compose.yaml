services:
  dev:
    hostname: agenda-diagnostic-dev
    build: ./api/
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_started
      adminer:
        condition: service_started
      mailcatcher:
        condition: service_started
    ports:
      - "8000:8000"
      - "3000:3000"
    environment:
      - APP_ENV=dev
      - FRONT_URL=http://127.0.0.1:3000
      - DEFAULT_URI=http://127.0.0.1:8000/
      - DATABASE_NAME=agenda-diagnostic
      - DATABASE_HOST=agenda-diagnostic-dev-mysql
      - DATABASE_PORT=3306
      - DATABASE_USER=root
      - DATABASE_PASSWORD=toor
      - DATABASE_URL=mysql://root:toor@agenda-diagnostic-dev-mysql:3306/agenda-diagnostic?serverVersion=8.1.0&charset=utf8mb4
      - MAILER_DSN=smtp://agenda-diagnostic-dev-mailcatcher:1025
    volumes:
      - ../../:/var/www/

  mysql:
    hostname: agenda-diagnostic-dev-mysql
    image: mysql:8.1.0
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "mysqladmin", "ping", "-h", "127.0.0.1", "-P", "3306" ]
      interval: 10s
      timeout: 5s
      retries: 3
    environment:
      - MYSQL_ROOT_PASSWORD=toor
      - MYSQL_USER=api
      - MYSQL_PASSWORD=toor
      - MYSQL_DATABASE=agenda-diagnostic
      - MYSQL_PORT=3306
      - TZ=Europe/Paris
    volumes:
      - mysql_data:/var/lib/mysql

  adminer:
    hostname: agenda-diagnostic-dev-adminer
    image: adminer:latest
    restart: unless-stopped
    ports:
      - "8001:8080"
    environment:
      ADMINER_DEFAULT_SERVER: agenda-diagnostic-dev-mysql

  mailcatcher:
    hostname: agenda-diagnostic-dev-mailcatcher
    image: sj26/mailcatcher:latest
    restart: unless-stopped
    ports:
      - "1080:1080"

volumes:
  mysql_data:

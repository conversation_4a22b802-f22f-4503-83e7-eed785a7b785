FROM ubuntu:22.04

RUN apt-get update -yq \
    && apt-get upgrade -yq \
    && DEBIAN_FRONTEND=noninteractive apt-get install --no-install-recommends -yq \
        software-properties-common gpg-agent make \
        nano supervisor git ssh curl zip unzip \
        mysql-client ghostscript imagemagick \
    && apt-get autoremove \
    && apt-get autoclean \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

RUN add-apt-repository -yn ppa:ondrej/php \
    && apt-get update -yq \
    && apt-get upgrade -yq \
    && curl -1sLf 'https://dl.cloudsmith.io/public/symfony/stable/setup.deb.sh' | bash \
    && DEBIAN_FRONTEND=noninteractive apt-get install --no-install-recommends -yq \
        php8.3 php8.3-cli \
        php8.3-dom php8.3-xml \
        php8.3-bz2 php8.3-mbstring php8.3-intl \
        php8.3-curl php8.3-mysql php8.3-tidy \
        php8.3-apcu php8.3-zip php8.3-pdo php8.3-ssh2 \
        php8.3-soap php8.3-bcmath php8.3-gd php8.3-excimer php8.3-imap\
        symfony-cli=5.10.2 \
    && apt-get autoremove \
    && apt-get autoclean \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

COPY --from=composer:latest /usr/bin/composer /usr/local/bin/composer

COPY docker /

WORKDIR /var/www

RUN phpenmod -v ALL -s ALL api \
    && chmod +x /usr/bin/image_*

ENV NVM_DIR /usr/local/nvm
ENV NODE_VERSION v20.13.1

RUN mkdir -p $NVM_DIR \
    && curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash \
    && . $NVM_DIR/nvm.sh \
    && nvm install $NODE_VERSION \
    && nvm alias default $NODE_VERSION \
    && nvm use default \
    && chmod +x /usr/bin/image_*

ENV NODE_PATH $NVM_DIR/$NODE_VERSION/lib/node_modules
ENV PATH $NVM_INSTALL_PATH/bin:$NVM_DIR/versions/node/$NODE_VERSION/bin:$PATH

WORKDIR /var/www

EXPOSE 8000
EXPOSE 3000

ENTRYPOINT [ "image_start" ]

# HEALTHCHECK --timeout=60s CMD curl --silent --fail "http://127.0.0.1:8000/api/ping"

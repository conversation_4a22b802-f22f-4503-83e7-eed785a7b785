export const mapStore = (obj) => {
  return {
    ...(obj['@id'] ? { '@id': obj['@id']} : {}),
    ...(obj.legacyId ? { legacyId: parseInt(obj.legacyId) } : {}),
    name: obj.name,
    freeCallId: "BC1E",
    upload: obj.image?.length > 0 ? obj.image : null,
    meta: {
      title: obj.metaTitle?.length > 0 ? obj.metaTitle : null,
      description: obj.metaDescription?.length > 0 ? obj.metaDescription : null,
    },
    linkToOpen: obj.linkToOpen,
    location: {
      address1: obj.address,
      address2: null,
      city: obj.city,
      postcode: obj.zip,
      latitude: obj.latitude,
      longitude: obj.longitude,
    },
    contact: {
      name: obj.contactName,
      phone: obj.contactPhone,
      email: obj.contactEmail,
      upload: obj.contactImage?.length > 0 ? obj.contactImage : null,
    },
    certifications: obj.certifications,
    advantage: {
      code: null,
      percent: null,
    },
    description: obj.description?.length > 0 ? obj.description : null,
    ratingWidget: {
      html: obj.html?.length > 0 ? obj.html : null,
      js: obj.js?.length > 0 ? obj.js : null,
    },
    lyraSeller: obj.lyraSeller?.length > 0 ? obj.lyraSeller : null,
    locationAreas: obj.areas?.length > 0 ? obj.areas.split(",") : [],
    displayAppointment: "on" === obj.displayAppointment,
    enablePaymentThreeTimeNoFee: "on" === obj.paymentEnabled,
    displayCallIfAvailableNow: "on" === obj.displayCallIfAvailableNow,
    facebookLink: obj?.facebookLink,
    linkedinLink: obj?.linkedinLink,
    schedule: {
      monday: {
        morning: {
          openAt: obj.startMondayMorning?.length > 0 ? obj.startMondayMorning : null,
          closeAt: obj.endMondayMorning?.length > 0 ? obj.endMondayMorning : null,
        },
        afternoon: {
          openAt: obj.startMondayAfternoon?.length > 0 ? obj.startMondayAfternoon : null,
          closeAt: obj.endMondayAfternoon?.length > 0 ? obj.endMondayAfternoon : null,
        },
      },
      tuesday: {
        morning: {
          openAt: obj.startTuesdayMorning?.length > 0 ? obj.startTuesdayMorning : null,
          closeAt: obj.endTuesdayMorning?.length > 0 ? obj.endTuesdayMorning : null,
        },
        afternoon: {
          openAt: obj.startTuesdayAfternoon?.length > 0 ? obj.startTuesdayAfternoon : null,
          closeAt: obj.endTuesdayAfternoon?.length > 0 ? obj.endTuesdayAfternoon : null,
        },
      },
      wednesday: {
        morning: {
          openAt: obj.startWednesdayMorning?.length > 0 ? obj.startWednesdayMorning : null,
          closeAt: obj.endWednesdayMorning?.length > 0 ? obj.endWednesdayMorning : null,
        },
        afternoon: {
          openAt: obj.startWednesdayAfternoon?.length > 0 ? obj.startWednesdayAfternoon : null,
          closeAt: obj.endWednesdayAfternoon?.length > 0 ? obj.endWednesdayAfternoon : null,
        },
      },
      thursday: {
        morning: {
          openAt: obj.startThursdayMorning?.length > 0 ? obj.startThursdayMorning : null,
          closeAt: obj.endThursdayMorning?.length > 0 ? obj.endThursdayMorning : null,
        },
        afternoon: {
          openAt: obj.startThursdayAfternoon?.length > 0 ? obj.startThursdayAfternoon : null,
          closeAt: obj.endThursdayAfternoon?.length > 0 ? obj.endThursdayAfternoon : null,
        },
      },
      friday: {
        morning: {
          openAt: obj.startFridayMorning?.length > 0 ? obj.startFridayMorning : null,
          closeAt: obj.endFridayMorning?.length > 0 ? obj.endFridayMorning : null,
        },
        afternoon: {
          openAt: obj.startFridayAfternoon?.length > 0 ? obj.startFridayAfternoon : null,
          closeAt: obj.endFridayAfternoon?.length > 0 ? obj.endFridayAfternoon : null,
        },
      },
      saturday: {
        morning: {
          openAt: obj.startSaturdayMorning?.length > 0 ? obj.startSaturdayMorning : null,
          closeAt: obj.endSaturdayMorning?.length > 0 ? obj.endSaturdayMorning : null,
        },
        afternoon: {
          openAt: obj.startSaturdayAfternoon?.length > 0 ? obj.startSaturdayAfternoon : null,
          closeAt: obj.endSaturdayAfternoon?.length > 0 ? obj.endSaturdayAfternoon : null,
        },
      },
      sunday: {
        morning: {
          openAt: obj.startSundayMorning?.length > 0 ? obj.startSundayMorning : null,
          closeAt: obj.endSundayMorning?.length > 0 ? obj.endSundayMorning : null,
        },
        afternoon: {
          openAt: obj.startSundayAfternoon?.length > 0 ? obj.startSundayAfternoon : null,
          closeAt: obj.endSundayAfternoon?.length > 0 ? obj.endSundayAfternoon : null,
        },
      },
    },
  };
};

import LayoutContainer from "@/components/LayoutContainer";
import Navbar from "@/components/public/navbar/Navbar";
import Footer from "@/components/public/footer/Footer";
import { fetchApi } from "@/utils/fetch.utils";
import NotFound404 from "@/components/NotFound404";

export default async function NotFound() {
  const headerData = await fetchApi("header");
  const footerData = await fetchApi("footer");
  return (
    <>
      <Navbar data={headerData} />
      <LayoutContainer>
        <NotFound404 />
      </LayoutContainer>
      <Footer data={footerData} />
    </>
  );
}

"use client";

import Input from "@/components/inputs/Input";
import theme from "@/lib/theme";
import ROUTES from "@/enums/ROUTES";
import { <PERSON><PERSON>, FormHelperText, Stack, Typography } from "@mui/material";
import logo from "@public/assets/images/logo-agenda.svg";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { useApi } from "@/context/ApiProvider";
import { useEffect, useState } from "react";
import { useSnack } from "@/context/SnackProvider";

export default function Page({ params }) {
  const { get, post } = useApi();
  const { add } = useSnack();
  const router = useRouter();
  const [token, setToken] = useState(null);
  const searchParams = useSearchParams();
  const welcome = searchParams.get("welcome");

  const fetchToken = async () => {
    try {
      const token = await get(`/password-reset-tokens/${params.token}`);
      setToken(token?.token);
    } catch (error) {
      console.log(error);
      add("error", error.description);
      router.push(ROUTES.LOGOUT);
    }
  };

  useEffect(() => {
    void fetchToken();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    const obj = {
      password: e.target.password.value,
      confirmPassword: e.target.confirmPassword.value,
    };
    try {
      if (obj.password !== obj.confirmPassword) {
        alert("Les mots de passe ne correspondent pas");
        return;
      }
      await post("/password-resets", {
        token: token,
        password: obj.password,
      });
      add("success", "Mot de passe réinitialisé avec succès");
      router.push(ROUTES.LOGIN);
    } catch (error) {
      console.log(error);
      add("error", error.description);
    }
  };

  return (
    <Stack border={`1px solid ${theme.palette.grey.main}`} m="auto" my={10} width={400} p={5} gap={4}>
      <Image src={logo} alt="logo" width={100} style={{ margin: "auto" }} />
      <Typography variant="h4" textAlign="center">
        {welcome ? "Créez votre mot de passe" : "Réinitialisez votre mot de passe"}
      </Typography>
      <Stack component="form" gap={2} onSubmit={handleSubmit}>
        <Input required label="Mot de passe" type="password" fullWidth name="password" />
        <Input required label="Confirmer le mot de passe" type="password" fullWidth name="confirmPassword" />
        <FormHelperText>Le mot de passe doit contenir au moins 8 caractères, 1 chiffre, 1 lettre minuscule, 1 majuscule et 1 caractère spécial</FormHelperText>
        <Button type="submit">Valider</Button>
      </Stack>
    </Stack>
  );
}

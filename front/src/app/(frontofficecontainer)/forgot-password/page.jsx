"use client";

import Input from "@/components/inputs/Input";
import theme from "@/lib/theme";
import ROUTES from "@/enums/ROUTES";
import { Button, Stack, Typography } from "@mui/material";
import Link from "next/link";
import logo from "@public/assets/images/logo-agenda.svg";
import Image from "next/image";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import { useRouter } from "next/navigation";

export default function Page() {
  const { post } = useApi();
  const { add } = useSnack();
  const router = useRouter();

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await post("/password-reset-starts", {
        email: e.target.email.value,
      });
      e.target.reset();
      add("success", "L'email a bien été envoyé !");
      router.push(ROUTES.LOGIN);
    } catch (error) {
      console.error("Password reset failed:", error);
      add("error", error.description);
    }
  };

  return (
    <Stack border={`1px solid ${theme.palette.grey.main}`} m="auto" my={10} width={400} p={5} gap={4}>
      <Image src={logo} alt="logo" width={100} style={{ margin: "auto" }} />
      <Typography variant="h4" textAlign="center">
        Mot de passe oublié
      </Typography>
      <Stack component="form" spacing={2} onSubmit={handleSubmit}>
        <Input required label="Email" type="email" name="email" fullWidth />
        <Button type="submit">Envoyer</Button>
        <Button color="secondary" component={Link} href={ROUTES.LOGIN}>
          Annuler
        </Button>
      </Stack>
    </Stack>
  );
}

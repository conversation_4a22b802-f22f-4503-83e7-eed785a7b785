"use client";

import Input from "@/components/inputs/Input";
import theme from "@/lib/theme";
import ROUTES from "@/enums/ROUTES";
import { Button, Stack, Typography } from "@mui/material";
import Link from "next/link";
import logo from "@public/assets/images/logo-agenda.svg";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useUser } from "@/context/UserProvider";
import { useSnack } from "@/context/SnackProvider";

export default function Page() {
  const router = useRouter();
  const { login } = useUser();
  const { add } = useSnack();

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const data = await login({
        email: e.target.email.value,
        password: e.target.password.value,
      });
      router.push(ROUTES.ACCOUNT);
    } catch (e) {
      add("error", "E-mail ou mot de passe incorrect");
      console.log(e);
    }
  };

  return (
    <Stack border={`1px solid ${theme.palette.grey.main}`} m="auto" my={10} width={400} p={5} gap={4}>
      <Image src={logo} alt="logo" width={100} style={{ margin: "auto" }} />
      <Typography variant="h4" textAlign="center">
        Connexion
      </Typography>
      <Stack component="form" spacing={2} onSubmit={handleSubmit}>
        <Input required label="E-mail" type="email" fullWidth name="email" />
        <Input required label="Mot de passe" type="password" fullWidth name="password" />
        <Typography
          alignSelf="flex-end"
          variant="body2"
          sx={{
            textDecoration: "underline",
          }}
        >
          <Link href={ROUTES.FORGOT_PASSWORD}>Mot de passe oublié ?</Link>
        </Typography>
        <Button type="submit">Se connecter</Button>
      </Stack>
    </Stack>
  );
}

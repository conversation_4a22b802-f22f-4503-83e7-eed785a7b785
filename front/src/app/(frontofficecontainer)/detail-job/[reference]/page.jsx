import { headers } from "next/headers";
import DetailJob from "@/components/DetailJob";
import { fetchApi } from "@/utils/fetch.utils";

export async function generateMetadata({ params }) {
  const headersList = headers();
  const pathname = headersList.get("x-pathname");

  const job = await fetchApi(`job-offers/${params.reference}`);

  return {
    title: `${job.title} | ${job.agency?.name}`,
    description: `${job.title} | ${job.location} | ${job.agency?.name}`,
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_API_URL}${pathname}`,
    },
  };
}

export default async function DetailJobPage({ params }) {
  const job = await fetchApi(`job-offers/${params.reference}`);

  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "JobPosting",
    name: job.jobName,
    title: job.title,
    description: `${job.title} | ${job.location} | ${job.agency?.name}`,
    datePosted: job.publicationDate,
    identifier: {
      "@type": "PropertyValue",
      name: job.agency?.name ?? 'Agenda Diagnostics',
      value: job.uuid,
    },
    applicantLocationRequirements: {
      "@type": "Country",
      sameAs: "https://www.wikidata.org/wiki/Q142",
      name: "FR",
    },
    hiringOrganization: {
      "@type": "Organization",
      name: job.agency?.name ?? 'Agenda Diagnostics',
    },
  };

  return (
    <section>
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }} />
      <DetailJob job={job} />
    </section>
  );
}

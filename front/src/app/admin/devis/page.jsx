import ExportButton from "@/components/admin/ExportButton";
import ButtonSendMail from "@/components/ButtonSendMail";
import Pagination from "@/components/Pagination";
import QSearch from "@/components/QSearch";
import TableHeadSort from "@/components/tables/TableHeadSort";
import DIRECTIONS from "@/enums/DIRECTIONS";
import { BUILD_YEAR_TRANSLATED, ESTIMATE_TYPE_TRANSLATED, PROPERTY_TYPE_TRANSLATED } from "@/enums/ESTIMATES";
import { fetchApi } from "@/utils/fetch.utils";
import FormatUtils from "@/utils/format.utils";
import { Button, Paper, Stack, Table, TableBody, TableCell, TableHead, TableRow, Typography } from "@mui/material";

export default async function DevisPage({ searchParams }) {
  const user = await fetchApi("user");
  const role = user.role;

  let data;
  if (role === 'ROLE_SUPER_ADMIN') {
    data = await fetchApi("estimates", {
      "order[createdAt]": DIRECTIONS.DESC,
      ...searchParams,
    });
  } else {
    data = await fetchApi("my-estimates", {
      "order[createdAt]": DIRECTIONS.DESC,
      ...searchParams,
    });
  }

  const array = [
    { code: "execution", label: "Execution", variant: "black" },
    { code: "sendEmail", label: "", variant: "white" },
    { code: "lastName", label: "Nom" },
    { code: "firstName", label: "Prénom" },
    { code: "address", label: "Adresse" },
    { code: ["city", "name"], label: "Ville" },
    { code: ["city", "zip"], label: "Code postal" },
    { code: "customerEmail", isSortable: true, label: "Email" },
    { code: "customerPhone", isSortable: true, label: "Téléphone" },
    { code: "comment", label: "Commentaire" },
    { code: "createdAt", label: "Date", isSortable: true, render: (value) => FormatUtils.formatDate(value) + "-" + FormatUtils.formatTime(value) },
    { code: "type", label: "Type de devis", render: (value) => ESTIMATE_TYPE_TRANSLATED[value] },
    { code: "propertyType", label: "Type de bien", render: (value) => PROPERTY_TYPE_TRANSLATED[value] },
    { code: "roomNumber", label: "Nombre de pièces" },
    { code: "buildYear", label: "Année de construction", render: (value) => BUILD_YEAR_TRANSLATED[value] },
    { code: "gazOlderThan15Years", label: "Installation gaz" },
    { code: "electricityOlderThan15Years", label: "Installation electricité" },
    { code: ["opportunityWanted", "amiante"], label: "Amiante" },
    { code: ["opportunityWanted", "audit"], label: "Audit" },
    { code: ["opportunityWanted", "carrez"], label: "Loi carrez" },
    { code: ["opportunityWanted", "decent"], label: "Logement décent" },
    { code: ["opportunityWanted", "dpe"], label: "DPE" },
    { code: ["opportunityWanted", "electricite"], label: "Electricité" },
    { code: ["opportunityWanted", "erp"], label: "ERP" },
    { code: ["opportunityWanted", "etatLieux"], label: "État des lieux" },
    { code: ["opportunityWanted", "gaz"], label: "Gaz" },
    { code: ["opportunityWanted", "mesurage"], label: "Mesurage" },
    { code: ["opportunityWanted", "plomb"], label: "Plomb" },
    { code: ["opportunityWanted", "termites"], label: "Termites" },
    { code: "price", label: "Prix" },
    { code: "discountPrice", label: "Prix réduction" },
    { code: "appointmentDate", label: "Date RDV", render: (value) => value ? (FormatUtils.formatDate(value) + "-" + FormatUtils.formatTime(value)) : 'N/A' },
    { code: "frMeetingDate", label: "Date FR" },
    { code: "discountCode", label: "Code avantage" },
  ];

  const renderDynamicContent = (header, devis) => {
    const value = Array.isArray(header.code) ? devis[header.code[0]]?.[header.code[1]] : devis[header.code];

    if (typeof value === "boolean") {
      return value ? "Oui" : "Non";
    }

    if (typeof value === "string") {
      return value;
    }

    return "N/A";
  };

  const renderHeaderContent = (header, devis) => {
    if (header.render) {
      return header.render(devis[header.code]);
    }

    if (header.variant === "white") {
      return <ButtonSendMail devis={devis} />;
    }

    if (header.variant === "black") {
      return devis[header.code];
    }

    return renderDynamicContent(header, devis);
  };

  return (
    <Stack height="100%" overflow="hidden">
      <Stack component={Paper} p={{ xs: 1, md: 4 }} gap={2}>
        <Stack direction="row" gap={2} alignItems="center">
          <Typography variant="h2">Mes devis</Typography>
          <ExportButton data={data.member.map((item) => item.uuid)} />
        </Stack>
        <QSearch />

        <Stack borderRadius="10px" overflow="auto">
          <Table>
            <TableHead>
              <TableRow sx={{ bgcolor: "red", whiteSpace: "nowrap" }}>
                {array.map((header, index) =>
                  header.isSortable ? (
                    <TableHeadSort key={index} code={header.code} searchParams={searchParams}>
                      {header.label}
                    </TableHeadSort>
                  ) : (
                    <TableCell key={index} color="white">
                      {header.label}
                    </TableCell>
                  )
                )}
              </TableRow>
            </TableHead>

            <TableBody>
              {data.member?.map((devis) => (
                <TableRow key={devis.uuid}>
                  {array.map((header, index) => (
                    <TableCell key={index}>{renderHeaderContent(header, devis)}</TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Stack>
        <Pagination data={data} />
      </Stack>
    </Stack>
  );
}

import Agencies from "@/components/admin/agencies/Agencies";
import DIRECTIONS from "@/enums/DIRECTIONS";
import { fetchApi } from "@/utils/fetch.utils";
import { Suspense } from "react";

export default async function Page({ searchParams }) {
  const user = await fetchApi("user");
  const role = user.role;

  let data;
  if (role === 'ROLE_SUPER_ADMIN') {
    data = await fetchApi("agencies", {
      ...searchParams,
      "order[name]": DIRECTIONS.ASC,
    });
  } else {
    data = await fetchApi("my-agencies", {
      ...searchParams,
      "order[name]": DIRECTIONS.ASC,
    });
  }


  return (
    <Suspense>
      <Agencies data={data} searchParams={searchParams} />
    </Suspense>
  );
}

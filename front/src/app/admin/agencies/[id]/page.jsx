import StoreActu from "@/components/admin/StoreActu";
import FormAgencies from "@/components/admin/agencies/form/FormAgencies";
import { fetchApi } from "@/utils/fetch.utils";
import { Stack } from "@mui/material";

export default async function Page({ params }) {
  const agency = await fetchApi(`agencies/${params.id}`);

  return (
    <Stack gap={4}>
      <FormAgencies agency={agency} />
      <StoreActu agency={agency} />
    </Stack>
  );
}

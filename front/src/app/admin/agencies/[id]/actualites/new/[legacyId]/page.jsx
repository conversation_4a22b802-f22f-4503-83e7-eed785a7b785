"use client";

import BackButton from "@/components/buttons/BackButton";
import ButtonSave from "@/components/forms/agencies/ButtonSave";
import InputMedias from "@/components/inputs/InputMedias";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import Input from "@/components/inputs/Input";
import { Paper, Stack, Typography } from "@mui/material";
import { useRouter } from "next/navigation";
import TranslationUtils from "@/utils/translation.utils";
import InputWysiwyg from "@/components/inputs/wysiwyg/InputWysiwyg";
import { useEffect, useState } from "react";

export default function Page({ params }) {
  const { post } = useApi();
  const { add } = useSnack();
  const router = useRouter();

  const [content, setContent] = useState(null);

  const submit = async (e) => {
    try {
      e.preventDefault();
      const formData = new FormData(e.target);
      const obj = {
        ...Object.fromEntries(formData.entries()),
        content: content,
        upload: e.target.upload.value === "" ? null : e.target.upload.value,
        agency: `/api/agencies/${params.legacyId}`,
      };

      await post("/agency-posts", obj);
      add("success", "Actualité enregistrée avec succès");
      router.push(`/admin/agencies/${params.id}`);
      router.refresh();
    } catch (error) {
      add("error", "Une erreur est survenue");
    }
  };

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <Stack component={Paper} p={3} gap={2}>
      <BackButton url={`/admin/agencies/${params.id}`}>{TranslationUtils.get("agencies.posts._back")}</BackButton>
      <Typography variant="h2">{TranslationUtils.get("agencies.posts.add")}</Typography>
      <Stack gap={2} component="form" onSubmit={submit}>
        <Input name="title" label={TranslationUtils.get("agencies.posts.title")} fullWidth required />
        <InputWysiwyg label="Contenu" name={TranslationUtils.get("global.content")} fullWidth onChange={setContent} required />
        <Input name="url" label={TranslationUtils.get("agencies.posts.url")} fullWidth required />
        <InputMedias defaultValue={null} />
        <ButtonSave />
      </Stack>
    </Stack>
  );
}

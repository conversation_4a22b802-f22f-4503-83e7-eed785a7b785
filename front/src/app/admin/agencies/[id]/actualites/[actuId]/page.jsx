"use client";

import BackButton from "@/components/buttons/BackButton";
import ButtonSave from "@/components/forms/agencies/ButtonSave";
import InputMedias from "@/components/inputs/InputMedias";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import Input from "@/components/inputs/Input";
import { Paper, Stack, Typography } from "@mui/material";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import TranslationUtils from "@/utils/translation.utils";
import InputWysiwyg from "@/components/inputs/wysiwyg/InputWysiwyg";
import Loader from "@/components/Loader";

export default function Page({ params }) {
  const { get, put } = useApi();
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const { add } = useSnack();
  const router = useRouter();

  const submit = async (e) => {
    try {
      e.preventDefault();
      const obj = {
        ...data,
        title: e.target.title.value,
        content: data.content,
        url: e.target.url.value,
        upload: e.target.upload.value === "" ? null : e.target.upload.value,
        agency: data.agency["@id"],
      };

      await put(`/agency-posts/${params.actuId}`, obj);
      add("success", "Actualité enregistrée avec succès");
      router.push(`/admin/agencies/${params.id}`);
      router.refresh();
    } catch (error) {
      add("error", "Une erreur est survenue");
      console.log(error);
    }
  };

  const getData = async () => {
    try {
      const response = await get(`/agency-posts/${params.actuId}`);
      setData(response);
    } catch (error) {
      console.error(error);
    } finally {
      if (loading) {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    window.scrollTo(0, 0);
    void getData();
  }, []);

  if (loading) {
    return <Loader />;
  }

  return (
    <Stack component={Paper} p={3} gap={2}>
      <BackButton url={`/admin/agencies/${params.id}`}>{TranslationUtils.get("agencies.posts._back")}</BackButton>
      <Typography variant="h2">{TranslationUtils.get("agencies.posts.edit")}</Typography>
      <Stack gap={2} component="form" onSubmit={submit}>
        <Input name="title" label={TranslationUtils.get("agencies.posts.title")} defaultValue={data?.title} fullWidth required />
        <InputWysiwyg
          name="contenu"
          label="Contenu"
          fullWidth
          defaultValue={data?.content}
          onChange={(value) => setData({ ...data, content: value })}
          required
        />
        <Input
          name="url"
          label={TranslationUtils.get("agencies.posts.url")}
          defaultValue={data?.url}
          fullWidth
          helper="Lien ou slug (exemple : /lien-actualite)"
          required
        />
        <InputMedias defaultValue={data?.upload} />
        <ButtonSave />
      </Stack>
    </Stack>
  );
}

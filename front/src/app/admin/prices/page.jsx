"use client";

import { Stack, Grid2 as <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Button, Typography } from "@mui/material";
import PricesCard from "@/components/admin/PricesCard";
import { useApi } from "@/context/ApiProvider";
import { useEffect, useState } from "react";
import Loader from "@/components/Loader";
import Input from "@/components/inputs/Input";
import TranslationUtils from "@/utils/translation.utils";
import { useUser } from "@/context/UserProvider";

export default function Page() {
  const { get } = useApi();
  const user = useUser()
  const [page, setPage] = useState(1);
  const [agencies, setAgencies] = useState(null);

  const getAgencies = async (search = null) => {
    const params = { page, "order[location.postcode]": "asc" };
    if (search && search.length > 0) {
      params.q = search;
    }
    let data;

    if (user.isSuperAdmin()) {
      data = await get("/agencies", params);
    } else  {
      data = await get("/my-agencies", params);
    }
    setAgencies(data);
  };

  useEffect(() => {
    getAgencies();
  }, [page]);

  if (!agencies) {
    return <Loader />;
  }

  return (
    <Stack height="100%" gap={4}>
      <Stack
        component="form"
        direction="row"
        gap={1}
        onSubmit={(e) => {
          e.preventDefault();
          getAgencies(e.target.search.value);
        }}
        width="50%"
        alignItems="center"
      >
        <Input name="search" placeholder="Rechercher un cabinet" fullWidth />
        <Button type="submit">{TranslationUtils.get("global.search")}</Button>
      </Stack>
      <Stack>
        <Typography mb={2} color="error">*Grille active en rouge</Typography>
        <Grid container spacing={2} justifyContent="space-between">
          {agencies.member.map((item) => (
            <Grid size={{ xs: 12, md: 6 }} key={item?.uuid}>
              <PricesCard agency={item} onSuccess={() => getAgencies()} />
            </Grid>
          ))}
        </Grid>
      </Stack>
      <Stack justifyContent="center" alignItems="center">
        <Pagination
          showFirstButton
          showLastButton
          count={Math.ceil(agencies.totalItems / 30)}
          page={Number(page)}
          onChange={(_, page) => setPage(page)}
        />
      </Stack>
    </Stack>
  );
}

"use client";

import Loader from "@/components/Loader";
import { useApi } from "@/context/ApiProvider";
import { createElement, useEffect, useState } from "react";
import Type1 from "@/components/admin/prices/Type1";
import Type2 from "@/components/admin/prices/Type2";
import Type3 from "@/components/admin/prices/Type3";

const COMPONENT_TYPE = {
  [1]: Type1,
  [2]: Type2,
  [3]: Type3,
};

export default function Page({ params }) {
  const { get } = useApi();
  const [agency, setAgency] = useState(null);
  const [price, setPrice] = useState(null);

  const getData = async () => {
    const a = await get(`/agencies/${params.agency}`);
    const data = await get(`/price-grids/${params.uuid}`);
    setAgency(a);
    setPrice(data);
  };

  useEffect(() => {
    getData();
  }, []);

  if (!agency) {
    return <Loader />;
  }

  return createElement(COMPONENT_TYPE[price.type], { params, agency, price, onSuccess: getData });
}

"use client";

import { <PERSON>, <PERSON><PERSON>, Paper, Stack, Typography } from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import { useEffect, useState } from "react";
import { useApi } from "@/context/ApiProvider";
import Loader from "@/components/Loader";
import FormatUtils from "@/utils/format.utils";
import ButtonAddArea from "@/components/admin/prices/ButtonAddArea";
import DeleteModal from "@/components/DeleteModal";
import BackButton from "@/components/buttons/BackButton";
import { DEFAULT_PRICE_DATA } from "@/enums/PRICE_TYPE";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import ButtonDuplicatePrice from "@/components/admin/prices/ButtonDuplicatePrice";
import DIRECTIONS from "@/enums/DIRECTIONS";
import TranslationUtils from "@/utils/translation.utils";
import { useSnack } from "@/context/SnackProvider";

export default function Page({ params }) {
  const { get, post } = useApi();
  const [agency, setAgency] = useState(null);
  const [prices, setPrices] = useState(null);
  const [deleteItem, setDeleteItem] = useState(null);
  const { add } = useSnack();

  const getData = async () => {
    const a = await get(`/agencies/${params.agency}`);
    const data = await get(`/price-grids`, { agency: params.agency, type: params.type, pagination: false, "order[area.name]": DIRECTIONS.ASC });
    setAgency(a);
    setPrices(data.member);
  };

  const onSuccess = async () => {
    await getData();
    add("success", "Canton ajouté");
  };

  useEffect(() => {
    getData();
  }, []);

  if (!agency) {
    return <Loader />;
  }

  const createGlobalPrice = async () => {
    const price = DEFAULT_PRICE_DATA[params.type](agency["@id"]);
    await post(`/price-grids`, price);
    await getData();
  };

  return (
    <Stack component={Paper} p={{ xs: 1, md: 4 }}>
      <BackButton>{TranslationUtils.get("agencies._back")}</BackButton>
      <Typography variant="h2">{TranslationUtils.get("prices.modify")}</Typography>
      <Box gap={8} display="flex" py={2}>
        <Stack>
          <Typography variant="body2">{TranslationUtils.get("agencies._")}:</Typography>
          <Typography variant="h6" color="secondary">
            {agency.name}
          </Typography>
        </Stack>
        <Stack>
          <Typography variant="body2">Grille:</Typography>
          <Typography variant="h6" color="secondary">
            {params.type}
          </Typography>
        </Stack>
      </Box>
      <Stack mt={4} gap={2}>
        <Stack display="grid" gridTemplateColumns="1fr max-content 1fr">
          <Typography variant="bold">Tarif global</Typography>
          <Typography variant="bold">Mise à jour le</Typography>
          <Stack />
        </Stack>
        {prices
          .filter((e) => !e.area)
          .map((item, index) => (
            <Stack
              key={index}
              display="grid"
              gridTemplateColumns="1fr max-content 1fr"
              border="1px solid #e0e0e0"
              p={3}
              borderRadius={2}
              alignItems="center"
            >
              <Typography variant="bold">{item.name}</Typography>
              <Stack direction="row" gap={1}>
                <Typography variant="bold">{FormatUtils.formatDate(item.updatedAt || item.createdAt)}</Typography>
                <span>à</span>
                <Typography variant="bold">{FormatUtils.formatTime(item.updatedAt || item.createdAt)} </Typography>
              </Stack>
              <Stack direction="row" gap={1} alignItems="center" justifyContent="flex-end">
                <ButtonDuplicatePrice item={item} agency={params.agency} type={params.type} prices={prices} onSuccess={getData} />
                <Button
                  startIcon={<EditIcon />}
                  variant="contained"
                  color="secondary"
                  size="small"
                  href={`/admin/prices/${params.agency}/${params.type}/${item.uuid}`}
                >
                  Éditer
                </Button>
              </Stack>
            </Stack>
          ))}
        {!prices.filter((e) => !e.area).length ? (
          <Stack border="1px solid #e0e0e0" p={2} borderRadius={2} alignItems="center" justifyContent="center">
            <Button variant="text" color="text" onClick={createGlobalPrice}>
              <Typography variant="bold">+ Ajouter un tarif global</Typography>
            </Button>
          </Stack>
        ) : null}
      </Stack>
      <Stack mt={4} gap={2}>
        <Stack display="grid" gridTemplateColumns="1fr max-content 1fr">
          <Typography variant="bold">Tarifs spécifiques</Typography>
          <Typography variant="bold">Mise à jour le</Typography>
          <Stack />
        </Stack>
        {prices
          .filter((e) => e.area)
          .map((item, index) => (
            <Stack
              key={index}
              display="grid"
              gridTemplateColumns="1fr max-content 1fr"
              border="1px solid #e0e0e0"
              p={3}
              borderRadius={2}
              alignItems="center"
            >
              <Typography variant="bold">{item.area.name}</Typography>
              <Stack direction="row" gap={1}>
                <Typography variant="bold">{FormatUtils.formatDate(item.updatedAt || item.createdAt)}</Typography>
                <span>à</span>
                <Typography variant="bold">{FormatUtils.formatTime(item.updatedAt || item.createdAt)} </Typography>
              </Stack>
              <Stack direction="row" gap={1} alignItems="center" justifyContent="flex-end">
                <ButtonDuplicatePrice item={item} agency={params.agency} type={params.type} prices={prices} onSuccess={getData} />
                <Button
                  startIcon={<EditIcon />}
                  variant="contained"
                  color="secondary"
                  size="small"
                  href={`/admin/prices/${params.agency}/${params.type}/${item.uuid}`}
                >
                  Éditer
                </Button>
                <Button variant="contained" color="secondary" size="small" onClick={() => setDeleteItem(item)}>
                  <DeleteIcon />
                </Button>
              </Stack>
            </Stack>
          ))}
        <ButtonAddArea agency={agency} type={params.type} prices={prices} onSuccess={onSuccess} />
        <DeleteModal item={deleteItem} onRefresh={getData} onClose={() => setDeleteItem(null)} />
      </Stack>
    </Stack>
  );
}

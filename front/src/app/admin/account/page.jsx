"use client";

import { useUser } from "@/context/UserProvider";
import { <PERSON>ton, Paper, Stack, Typography } from "@mui/material";
import Input from "@/components/inputs/Input";
import { useApi } from "@/context/ApiProvider";
import Loader from "@/components/Loader";
import { useSnack } from "@/context/SnackProvider";
import TranslationUtils from "@/utils/translation.utils";

export default function Page() {
  const { user } = useUser();
  const { put } = useApi();
  const { add } = useSnack();

  const submit = async (e) => {
    e.preventDefault();
    try {
      const data = Object.fromEntries(new FormData(e.target));
      const obj = { ...user, ...data };
      await put(`/users/${user.uuid}`, obj);
      add("success", TranslationUtils.get("account.update"));
    } catch (error) {
      add("error", TranslationUtils.get("global.error"));
    }
  };

  if (!user) {
    return <Loader />;
  }

  return (
    <Stack component={Paper} p={4} height="fit-content">
      <Typography variant="h1">{TranslationUtils.get("account.title")}</Typography>
      <Stack component="form" gap={2} onSubmit={submit} mt={2}>
        <Stack direction="row" gap={2} width="100%">
          <Input name="lastname" label={TranslationUtils.get("account.lastname")} defaultValue={user.lastname} fullWidth />
          <Input name="firstname" label={TranslationUtils.get("account.firstname")} defaultValue={user.firstname} fullWidth />
        </Stack>
        <Input name="email" label={TranslationUtils.get("account.email")} defaultValue={user.email} disabled fullWidth />
        <Stack direction="row" justifyContent="flex-end">
          <Button type="submit">{TranslationUtils.get("global.save")}</Button>
        </Stack>
      </Stack>
    </Stack>
  );
}

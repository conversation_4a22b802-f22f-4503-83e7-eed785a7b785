import SectionTitle from "@/components/admin/SectionTitle";
import LoggedLayoutContainer from "@/components/admin/layout/LoggedLayoutContainer";
import Menu from "@/components/admin/layout/Menu";
import Navbar from "@/components/public/navbar/Navbar";
import Footer from "@/components/public/footer/Footer";
import { fetchApi } from "@/utils/fetch.utils";

export const metadata = {
  title: "Administration",
  description: "Administration",
};

export default async function layout({ children }) {
  const headerData = await fetchApi("header");
  const footerData = await fetchApi("footer");
  return (
    <>
      <Navbar data={headerData} />
      <SectionTitle />
      <LoggedLayoutContainer>
        <Menu />
        {children}
      </LoggedLayoutContainer>
      <Footer data={footerData} />
    </>
  );
}

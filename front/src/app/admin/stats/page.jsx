"use client";

import { useCallback, useEffect, useState } from "react";
import { <PERSON>, <PERSON>ack, <PERSON><PERSON><PERSON>, But<PERSON>, debounce } from "@mui/material";
import { <PERSON><PERSON><PERSON>, BarLabel } from "@mui/x-charts";
import Input from "@/components/inputs/Input";
import { useApi } from "@/context/ApiProvider";
import Loader from "@/components/Loader";

const statsKeys = {
  estimate: "Devis",
  appointment: "Rendez-vous",
  estimateOld: "Devis (période précédente)",
  appointmentOld: "Rendez-vous (période précédente)",
};

export default function Page() {
  const { get } = useApi();
  const [startDate, setStartDate] = useState(new Date(new Date().setDate(new Date().getDate() - 7)).toISOString().split("T")[0]);
  const [endDate, setEndDate] = useState(new Date().toISOString().split("T")[0]);
  const [data, setData] = useState([]);
  const [xAxisLabels, setXAxisLabels] = useState([]);

  const getDatesInRange = () => {
    const dates = [];
    const currentDate = new Date(startDate);
    const end = new Date(endDate);

    while (currentDate <= end) {
      dates.push(new Date(currentDate).toISOString().split("T")[0]);
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return dates;
  };

  const getData = async () => {
    const data = await get(`/agency-stats`, {
      startAt: startDate,
      endAt: endDate,
    });

    const array = [];
    const dates = getDatesInRange();

    const members = data.member;

    const middleIndex = Math.ceil(members.length / 2);

    const oldStats = members.slice(0, middleIndex);
    const secondHalf = members.slice(middleIndex);

    let oldEstimate = {
      label: statsKeys.estimateOld,
      data: []
    }

    let oldAppointment = {
      label: statsKeys.appointmentOld,
      data: []
    }

    oldStats.forEach((s, index) => {
      oldEstimate.data.push(s.estimate);
      oldAppointment.data.push(s.appointment)
    })
    array.push(oldEstimate);
    array.push(oldAppointment);


    let estimate = {
      label: statsKeys.estimate,
      data: []
    }

    let appointment = {
      label: statsKeys.appointment,
      data: []
    }

    secondHalf.forEach((s, index) => {
      estimate.data.push(s.estimate);
      appointment.data.push(s.appointment)
    })
    array.push(estimate);
    array.push(appointment);

    setData(array);
  };

  useEffect(() => {
    setXAxisLabels(getDatesInRange());
    getData();
  }, []);

  const submit = (e) => {
    e.preventDefault();
    setXAxisLabels(getDatesInRange());
    getData();
  };

  const chartSettings = {
    series: data,
    colors: ["#0094d3", "#004d70", "#707070", "#9E2B50"],
    xAxis: [{ data: xAxisLabels.map((date) => {
        let d = new Date(date)
        let p = new Date(date)
        p.setDate(d.getDate() - getDatesInRange().length);

        return `${d.toLocaleDateString("fr-FR")} (${p.toLocaleDateString("fr-FR")})`;
      }), scaleType: "band" }],
    height: 500,
    barLabel:"value",
    slotProps: {
      legend: {
        direction: "row",
        position: { vertical: "bottom", horizontal: "middle" },
        padding: -10,
      },
      barLabel: {
        style: {
          fill: "white",
        },
      }
    },
  };

  const handleChange = useCallback(
    debounce((f) => {
      f();
    }, 100),
    []
  );

  return (
    <Stack component={Paper} width="100%" p={5} gap={3}>
      <Typography variant="h2">Statistiques</Typography>
      <Stack direction="row" gap={2} alignItems="center" component="form" onSubmit={submit}>
        <Typography variant="h6">Du</Typography>
        <Input
          type="date"
          name="startDate"
          slotProps={{
            htmlInput: {
              max: endDate,
            },
          }}
          onChange={(e) => handleChange(() => setStartDate(e.target.value))}
          defaultValue={startDate}
        />
        <Typography variant="h6">au</Typography>
        <Input
          type="date"
          name="endDate"
          slotProps={{
            htmlInput: {
              min: startDate,
            },
          }}
          onChange={(e) => handleChange(() => setEndDate(e.target.value))}
          defaultValue={endDate}
        />
        <Button color="secondary" type="submit">
          Rechercher
        </Button>
      </Stack>

      {chartSettings.series.length > 0 ? <BarChart {...chartSettings} sx={{ width: "100%", color: "white !important" }} /> : <Loader />}
    </Stack>
  );
}

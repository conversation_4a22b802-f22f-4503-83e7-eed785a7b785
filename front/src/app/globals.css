html,
body {
    max-width: 100vw;
    overflow-x: hidden;
    scroll-padding-top: 150px;
}
body {
    font-family: var(--font-montserrat-regular);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-image: url("/assets/background.jpg");
    background-size: cover;
    background-attachment: fixed;
    background-repeat: no-repeat;
    background-position: center;
    --primary-color: #0094d3;
}
* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

ul,
ol {
    padding: revert;
}
a {
    color: inherit;
    text-decoration: none;
}
.ProseMirror-focused {
    outline: none;
}
.ProseMirror {
    min-height: inherit;
}
/* .ProseMirror * {
    padding: revert;
    margin: revert;
} */
.ProseMirror a {
    color: revert;
    text-decoration: revert;
}
.tree-content div {
    padding: 5px;
}

.prev,
.next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
}

.prev {
    left: -30px;
}

.next {
    right: -30px;
}

.mySwiper {
    position: relative;
    width: 100%;
}

.swiper {
    width: 100%;
    height: 100%;
}

.mySwiper .swiper-slide {
    max-width: 200px;
}

.number-without-arrows {
    text-align: center;
}

.number-without-arrows::-webkit-inner-spin-button,
.number-without-arrows::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.input-without-arrows::-webkit-inner-spin-button,
.input-without-arrows::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

"use client";

import { Suspense, useEffect, useState } from "react";
import { Stack } from "@mui/material";
import Toolbar from "@/components/superadmin/Toolbar";
import Tree from "@/components/superadmin/Tree";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import FormLogo from "@/components/superadmin/FormLogo";

export default function Page() {
  const [logo, setLogo] = useState({});
  const [items, setItems] = useState([]);
  const [sublinks, setSublinks] = useState([]);
  const [socialLinks, setSocialLinks] = useState([]);

  const { get, put } = useApi();
  const { add } = useSnack();

  const getData = async () => {
    try {
      const data = await get("/footer");
      setLogo(data.logo);
      setItems(data.navs);
      setSublinks(data.links);
      setSocialLinks(data.socials);
    } catch (error) {
      add("error", "Erreur lors de la récupération des données");
    }

    return Promise.resolve();
  };

  useEffect(() => {
    getData();
  }, []);

  const mapNavs = (navs) => {
    if (!navs) return [];
    return navs.map((nav) => ({
      ...nav,
      link: {
        ...nav.link,
        content: nav.link?.content ? nav.link?.content?.["@id"] || nav.link?.content : null,
        label: nav.link?.content ? null : nav.link?.label,
        url: nav.link?.content ? null : nav.link?.url,
      },
      children: mapNavs(nav?.children),
    }));
  };

  const submit = async () => {
    try {
      const obj = {
        logo: {
          ...logo,
          upload: logo.upload?.["@id"] || logo.upload,
        },
        navs: mapNavs(items),
        links: mapNavs(sublinks),
        socials: socialLinks.map((item) => ({
          ...item,
          link: {
            ...item.link,
            upload: item.link.upload?.["@id"] || item.link.upload,
          },
        })),
      };
      await put("/footer", obj);
      add("success", "Footer modifié");
    } catch (error) {
      console.log(error);
      add("error", error.description);
    }
  };

  return (
    <Suspense>
      <Stack gap={3} overflow="hidden">
        <Toolbar title="Footer" onSave={submit} />
        <Stack overflow="auto" height="100%" gap={2}>
          <FormLogo logo={logo} onChange={setLogo} />
          <Tree label="Menu" items={items} onChange={setItems} />
          <Tree label="Sous menu" items={sublinks} onChange={setSublinks} />
          <Tree label="Réseaux sociaux" items={socialLinks} onChange={setSocialLinks} withImage disallowContents />
        </Stack>
      </Stack>
    </Suspense>
  );
}

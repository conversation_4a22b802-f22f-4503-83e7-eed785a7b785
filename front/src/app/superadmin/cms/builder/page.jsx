"use client";

import { <PERSON><PERSON>, Paper, Stack } from "@mui/material";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { Suspense, useEffect, useRef, useState } from "react";
import Toolbar from "@/components/superadmin/Toolbar";
import PagesTree from "@/components/superadmin/PagesTree";
import PagesForm from "@/components/superadmin/PagesForm";
import BlockContainer from "@/components/superadmin/BlockContainer";
import BlocRender from "@/components/superadmin/BlocRender";
import DrawerBlocs from "@/components/superadmin/DrawerBlocs";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import DeleteModal from "@/components/DeleteModal";
import theme from "@/lib/theme";
import TranslationUtils from "@/utils/translation.utils";
import { BLOC_LABEL } from "@/enums/BLOC_LABEL";
import { useContent } from "@/context/ContentProvider";
import LeavingDialog from "@/components/LeavingModal";
import { formatContent, removeId } from "@/utils/builder.utils";

function SuspensedPage() {
  const [currentPage, setCurrentPage] = useState(null);
  const [openBlocksContainer, setOpenBlocksContainer] = useState(false);
  const searchParams = useSearchParams();
  const currentPageId = searchParams.get("page");
  const [stack, setStack] = useState([]);
  const [currentBloc, setCurrentBloc] = useState(null);
  const [contentInfos, setContentInfos] = useState({});
  const [itemDelete, setItemDelete] = useState(null);
  const [isDirty, setIsDirty] = useState(false);
  const { get, put, post } = useApi();
  const { add } = useSnack();
  const pathname = usePathname();
  const router = useRouter();
  const { fetchContents, contents } = useContent();
  const ref = useRef(null);

  const isEqual = () => {
    if (currentPage) {
      const defaultData = {
        name: currentPage.name,
        slug: currentPage.slug,
        seo_title: currentPage.seo.title,
        seo_description: currentPage.seo.description,
        status: currentPage.status,
      };
      const infos = {
        name: contentInfos.name,
        slug: contentInfos.slug,
        seo_title: contentInfos.seo_title,
        seo_description: contentInfos.seo_description,
        status: contentInfos.status,
      };
      return JSON.stringify(defaultData) === JSON.stringify(infos) && JSON.stringify(stack) === JSON.stringify(currentPage?.page?.blocks);
    }
    return true;
  };

  useEffect(() => {
    if (currentBloc && !isDirty) {
      setIsDirty(true);
    }
    if (!isEqual() && !isDirty) {
      setIsDirty(true);
    } else if (isEqual() && isDirty) {
      setIsDirty(false);
    }
  }, [currentBloc, contentInfos, currentPage, stack]);

  useEffect(() => {
    setIsDirty(false);
  }, [currentPageId]);

  const addStack = (block) => {
    const initialValue = stack.find((b) => b.uuid === block.uuid);
    if (initialValue) {
      setStack((prev) => prev.map((b) => (b.uuid === block.uuid ? block : b)));
      return;
    }
    setStack((prev) => [...prev, block]);
  };

  const removeBlock = (uuid) => {
    setStack((prev) => prev.filter((block) => block.uuid !== uuid));
  };

  const moveBlock = (uuid, direction) => {
    const newStack = [...stack];
    const currentIndex = newStack.findIndex((block) => block.uuid === uuid);

    if (direction === "up" && currentIndex > 0) {
      // Move block up
      const temp = newStack[currentIndex];
      newStack[currentIndex] = newStack[currentIndex - 1];
      newStack[currentIndex - 1] = temp;
    } else if (direction === "down" && currentIndex < newStack.length - 1) {
      // Move block down
      const temp = newStack[currentIndex];
      newStack[currentIndex] = newStack[currentIndex + 1];
      newStack[currentIndex + 1] = temp;
    }

    setStack(newStack);
  };

  const getData = async () => {
    try {
      const response = await get(`/contents/${currentPageId}`);
      setCurrentPage(response);
      setStack(response?.page?.blocks || []);
      setContentInfos({
        name: response?.name,
        slug: response?.slug,
        seo_title: response?.seo?.title,
        seo_description: response?.seo?.description,
        status: response?.status,
      });
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (currentPageId) {
      void getData();
      ref?.current?.scrollTo({ top: 0, behavior: "smooth" });
    } else {
      setCurrentPage(null);
    }
  }, [currentPageId]);

  useEffect(() => {
    if (!openBlocksContainer) {
      setCurrentBloc(null);
    }
  }, [openBlocksContainer]);

  const handleContentInfos = (key, value) => {
    setContentInfos((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const validate = async () => {
    try {
      const data = formatContent(currentPage, contentInfos, stack);
      await put(`/contents/${currentPageId}`, data);
      setIsDirty(false);
      add("success", "Page modifiée");
      void fetchContents();
    } catch (error) {
      add("error", error.description);
      console.log(error);
    }
  };

  const duplicate = async () => {
    try {
      const data = formatContent(currentPage, contentInfos, stack);
      const page = await post(`/contents`, removeId(data, contents.length + 1));
      const current = new URLSearchParams(Array.from(searchParams.entries()));
      current.set("page", page.uuid);
      router.push(`${pathname}?${current.toString()}`);
      void fetchContents();
      add("success", "Page dupliquée");
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <Stack gap={2} height="100dvh" overflow="hidden">
      <LeavingDialog isDirty={isDirty} save={validate} />
      <Toolbar
        title={TranslationUtils.get("contents._plural")}
        duplicate={duplicate}
        defaultActivate={contentInfos.status}
        onValidate={() => handleContentInfos("status", !contentInfos.status)}
        onSave={validate}
        onDelete={() => setItemDelete(currentPage)}
        onPreview={() => window.open(`/${currentPage?.slug}`, "_blank")}
        disabled={!currentPage}
        disabledSave={!isDirty}
      />
      <Stack width="100%" direction="row" gap={2} display="grid" gridTemplateColumns="300px 1fr" overflow="hidden">
        <Stack component={Paper} height="100%" p={3} overflow="auto">
          <PagesTree />
        </Stack>
        {currentPage ? (
          <>
            <Stack gap={2} overflow="auto" ref={ref}>
              {currentPageId ? <PagesForm content={contentInfos} onChange={handleContentInfos} /> : null}
              <Stack px="1px">
                {stack.map((block, index) => (
                  <BlockContainer
                    key={block.uuid}
                    index={index}
                    blocks={stack}
                    block={block}
                    name={BLOC_LABEL[block.type]}
                    onClick={() => {
                      setOpenBlocksContainer(true);
                      setCurrentBloc(block);
                    }}
                    onDelete={removeBlock}
                    onMove={moveBlock}
                  >
                    <BlocRender block={block} key={block.uuid} isBuilder />
                  </BlockContainer>
                ))}
              </Stack>
              <Stack alignItems="center" border={`1px dashed ${theme.palette.grey[400]}`} py={1}>
                <Button onClick={() => setOpenBlocksContainer(true)}>+</Button>
              </Stack>
            </Stack>
            <DrawerBlocs open={openBlocksContainer} onClose={() => setOpenBlocksContainer(false)} addStack={addStack} defaultSelected={currentBloc} />
          </>
        ) : null}
      </Stack>
      <DeleteModal
        item={itemDelete}
        onClose={() => setItemDelete(null)}
        onRefresh={() => {
          setCurrentPage(null);
          void fetchContents();
          router.push(pathname);
        }}
      />
    </Stack>
  );
}

export default function Page() {
  return (
    <Suspense>
      <SuspensedPage />
    </Suspense>
  );
}

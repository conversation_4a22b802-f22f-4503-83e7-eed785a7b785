"use client";

import { Suspense, useEffect, useState } from "react";
import Toolbar from "@/components/superadmin/Toolbar";
import { Stack } from "@mui/material";
import Tree from "@/components/superadmin/Tree";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import FormLogo from "@/components/superadmin/FormLogo";

export default function Page() {
  const [navs, setNavs] = useState([]);
  const [links, setLinks] = useState([]);
  const [logo, setLogo] = useState({});

  const { get, put } = useApi();
  const { add } = useSnack();

  const getData = async () => {
    try {
      const data = await get("/header");
      setNavs(data.navs || []);
      setLinks(data.links || []);
      setLogo(data.logo || {});
    } catch (error) {
      add("error", "Erreur lors de la récupération des données");
    }
  };

  useEffect(() => {
    getData();
  }, []);

  const mapNavs = (navs) => {
    if (!navs) {
      return [];
    }
    return navs?.map((nav) => ({
      ...nav,
      link: {
        ...nav.link,
        content: nav.link?.content ? nav.link?.content?.["@id"] || nav.link?.content : null,
        label: nav.link?.content ? null : nav.link?.label,
        url: nav.link?.content ? null : nav.link?.url,
        upload: nav.link?.upload ? nav.link?.upload?.["@id"] || nav.link?.upload : null,
      },
      children: mapNavs(nav?.children),
    }));
  };

  const submit = async () => {
    try {
      const obj = {
        navs: mapNavs(navs),
        links: mapNavs(links),
        logo: {
          ...logo,
          link: {
            ...logo.link,
            content: logo.link?.content ? logo.link?.content?.["@id"] || logo.link?.content : null,
          },
          upload: logo.upload?.["@id"] || logo.upload,
        },
      };
      await put("/header", obj);
      add("success", "Header modifié");
    } catch (error) {
      console.log(error);
      add("error", error.description);
    }
  };

  return (
    <Suspense>
      <Stack gap={3} overflow="hidden">
        <Toolbar title="Header" onSave={submit} />
        <Stack overflow="auto" height="100%" gap={2}>
          <FormLogo logo={logo} onChange={setLogo} />
          <Tree label="Menu" items={navs} onChange={setNavs} />
          <Tree label="Buttons" items={links} onChange={setLinks} withImage max={3} canHaveChildren={false} />
        </Stack>
      </Stack>
    </Suspense>
  );
}

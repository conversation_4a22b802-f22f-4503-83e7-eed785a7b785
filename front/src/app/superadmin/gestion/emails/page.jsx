import Table from "@/components/tables/Table";
import LIST_OPTIONS from "@/enums/LIST_OPTIONS";
import { fetchApi } from "@/utils/fetch.utils";
import { Suspense } from "react";

export default async function Page({ searchParams }) {
  const data = await fetchApi("mailer-types");
  return (
    <Suspense>
      <Table title="Emails" data={data} page={searchParams.page ? searchParams.page : 1} searchParams={searchParams} options={LIST_OPTIONS.EMAILS} />
    </Suspense>
  );
}

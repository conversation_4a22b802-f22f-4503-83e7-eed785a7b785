import Loader from "@/components/Loader";
import FormFranchise from "@/components/forms/superadmin/FormFranchise";
import { fetchApi } from "@/utils/fetch.utils";
export default async function Page({ params: { uuid } }) {
  const data = await fetchApi(`users/${uuid}`);
  const agencies = await fetchApi("agency-users", { user: `/api/users/${uuid}` });

  const item = { ...data, agencies: agencies.member };

  if (!item) return <Loader />;

  return <FormFranchise item={item} />;
}

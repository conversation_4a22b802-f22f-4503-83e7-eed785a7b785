import { Suspense } from "react";
import Table from "@/components/tables/Table";
import LIST_OPTIONS from "@/enums/LIST_OPTIONS";
import TranslationUtils from "@/utils/translation.utils";
import { fetchApi } from "@/utils/fetch.utils";
import DIRECTIONS from "@/enums/DIRECTIONS";
import USER_ROLES from "@/enums/USER_ROLES";

export default async function PageUsers({ searchParams }) {
  const data = await fetchApi("users", {
    "order[lastname]": DIRECTIONS.ASC,
    "role": USER_ROLES.ADMIN,
    ...searchParams,
  });

  return (
    <Suspense>
      <Table
        title={TranslationUtils.get("franchises._plural")}
        data={data}
        page={searchParams.page ? searchParams.page : 1}
        searchParams={searchParams}
        options={LIST_OPTIONS.USERS}
      />
    </Suspense>
  );
}

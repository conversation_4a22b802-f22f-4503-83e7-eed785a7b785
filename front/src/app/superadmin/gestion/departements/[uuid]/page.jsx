"use client";

import { <PERSON><PERSON>, <PERSON>, Stack, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import Input from "@/components/inputs/Input";
import ROUTES from "@/enums/ROUTES";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import { useRouter } from "next/navigation";
import FormatUtils from "@/utils/format.utils";
import Loader from "@/components/Loader";

export default function Page({ params, searchParams }) {
	const { uuid } = params;
	const { put, get } = useApi();
	const { add } = useSnack();
	const [item, setItem] = useState(null);
	const router = useRouter();

	const getData = async (e) => {
		try {
			const data = await get(`/location-departments/${uuid}`);
			setItem(data);
		} catch (error) {
			console.log(error);
		}
	};

	useEffect(() => {
    void getData();
	}, []);

	const handleSubmit = async (e) => {
		try {
			e.preventDefault();
			const data = Object.fromEntries(new FormData(e.target));
			await put(`/location-departments/${uuid}`, data);
			add("success", "Le département a bien été modifié !");
			router.push(
				ROUTES.DEPARTEMENTS + FormatUtils.formatUrl(searchParams)
			);
			router.refresh();
		} catch (error) {
			add("error", error?.description);
			console.log(error);
		}
	};

	if (!item) {
		return <Loader />;
	}
	return (
		<Stack component={Paper} p={5} gap={3}>
			<Typography variant="h4">Modifier le département</Typography>
			<Stack gap={2} component="form" onSubmit={handleSubmit}>
				<Stack direction="row" gap={2}>
					<Input
						defaultValue={item?.name}
						label="Nom"
						name="name"
						fullWidth
						required
					/>
					<Input
						defaultValue={item?.code}
						label="Code postal"
						name="code"
						fullWidth
						required
					/>
				</Stack>
				<Stack direction="row" justifyContent="flex-end" gap={2}>
					<Button
						color="secondary"
						onClick={() =>
							router.push(
								ROUTES.DEPARTEMENTS +
									FormatUtils.formatUrl(searchParams)
							)
						}
					>
						Retour
					</Button>
					<Button type="submit">Enregistrer</Button>
				</Stack>
			</Stack>
		</Stack>
	);
}

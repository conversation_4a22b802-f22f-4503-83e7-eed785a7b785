"use client";

import { <PERSON><PERSON>, Paper, Stack, Typography } from "@mui/material";
import Input from "@/components/inputs/Input";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import { useRouter } from "next/navigation";
import ROUTES from "@/enums/ROUTES";
import FormatUtils from "@/utils/format.utils";

export default function Page({ searchParams }) {
	const { post } = useApi();
	const { add } = useSnack();
	const router = useRouter();

	const handleSubmit = async (e) => {
		try {
			e.preventDefault();
			const data = Object.fromEntries(new FormData(e.target));
			await post("/location-departments", data);
			add("success", "Le département a bien été créé !");
			router.push(
				ROUTES.DEPARTEMENTS + FormatUtils.formatUrl(searchParams)
			);
			router.refresh();
		} catch (error) {
			add("error", error?.description);
			console.log(error);
		}
	};
	return (
		<Stack component={Paper} p={5} gap={3}>
			<Typography variant="h4">Ajouter un département</Typography>
			<Stack gap={2} component="form" onSubmit={handleSubmit}>
				<Stack direction="row" gap={2}>
					<Input
						label="Nom"
						name="name"
						fullWidth
						autoFocus
						required
					/>
					<Input label="Code postal" name="code" fullWidth required />
				</Stack>
				<Stack direction="row" justifyContent="flex-end" gap={2}>
					<Button
						color="secondary"
						onClick={() =>
							router.push(
								ROUTES.DEPARTEMENTS +
									FormatUtils.formatUrl(searchParams)
							)
						}
					>
						Retour
					</Button>
					<Button type="submit">Ajouter</Button>
				</Stack>
			</Stack>
		</Stack>
	);
}

"use client";
import Dropzone from "@/components/Dropzone";
import Loader from "@/components/Loader";
import Table from "@/components/tables/Table";
import { useApi } from "@/context/ApiProvider";
import LIST_OPTIONS from "@/enums/LIST_OPTIONS";
import useMedia from "@/hooks/useMedia";
import { useEffect, useRef, useState } from "react";

export default function Medias({ searchParams }) {
  const { get } = useApi();
  const { uploadPdf } = useMedia();

  const [data, setData] = useState([]);

  const inputRef = useRef();

  const getData = async () => {
    const data = await get("/uploads", {
      isPublic: true,
      ...searchParams,
    });
    setData(data);
  };
  
  const handleUpload = async (files) => {
    Promise.all(
      files.map(async (file) => {
        await uploadPdf(file, true);
      })
    ).then(() => {
      getData();
    });
  };

  useEffect(() => {
    getData();
  }, []);

  if (!data.member) {
    return <Loader />;
  }

  return (
    <section>
      <Dropzone onDrop={handleUpload}>
        <Table
          title="Medias"
          data={data}
          page={searchParams.page ? searchParams.page : 1}
          searchParams={searchParams}
          options={LIST_OPTIONS.MEDIAS}
          canDelete
          canUpdate={false}
          onRefresh={getData}
          onClickAdd={() => {
            inputRef.current.click();
          }}
        />
        <input
          ref={inputRef}
          hidden
          type="file"
          multiple
          onChange={(e) => {
            handleUpload(Array.from(e.target.files));
          }}
        />
      </Dropzone>
    </section>
  );
}

import { fetchApi } from "@/utils/fetch.utils";
import BackButton from "@/components/buttons/BackButton";
import FormatUtils from "@/utils/format.utils";
import TranslationUtils from "@/utils/translation.utils";
import { Paper, Stack, Typography } from "@mui/material";

export default async function Page({ params }) {
  const data = await fetchApi(`job-applications/${params.uuid}`);
  return (
    <Stack component={Paper} p={4} gap={1}>
      <BackButton>Retour</BackButton>
      <Typography variant="h4" mb={3}>
        Detail candidature spontanée
      </Typography>
      <Typography variant="body1">Poste souhaité : {TranslationUtils.get(`wantedJobs.${data.wantedJob}`)}</Typography>
      <Typography variant="body1">
        Créé le : {FormatUtils.formatDate(data.createdAt)} à {FormatUtils.formatTime(data.createdAt)}
      </Typography>
      <Typography variant="body1">Civilité : {TranslationUtils.get(`civility.${data.civility}`)}</Typography>
      <Typography variant="body1">Nom : {data.lastname}</Typography>
      <Typography variant="body1">Prénom : {data.firstname}</Typography>
      <Typography variant="body1">Adresse : {data.address}</Typography>
      <Typography variant="body1">Code postal : {data.zipCode}</Typography>
      <Typography variant="body1">Ville : {data.city}</Typography>
      <Typography variant="body1">Téléphone : {data.phone}</Typography>
      <Typography variant="body1">Email : {data.email}</Typography>
      <Typography variant="body1">Date de naissance : {FormatUtils.formatDate(data.birthdate)}</Typography>
      <Typography variant="body1">Vos formations et certifications : {data.training}</Typography>
      <Typography variant="body1">Expériences professionnelles : {data.professionalExperience}</Typography>
      <Typography variant="body1">Niveau de connaissance informatique : {data.computerLevel}</Typography>
      <Typography variant="body1">Situation actuelle : {TranslationUtils.get(`situation.${data.situation}`)}</Typography>
      <Typography variant="body1">Lieu de travail souhaité : {data.workplace}</Typography>
      <Typography variant="body1">Disponibilité : {FormatUtils.formatDate(data.availability)}</Typography>
      <Typography variant="body1">Salaire souhaité : {data.salary}</Typography>
      <Typography variant="body1">
        Lettre de motivation :{" "}
        <Typography component="a" target="_blank" href={FormatUtils.binary(data.coverLetter)} sx={{ textDecoration: "underline" }}>
          Ouvrir
        </Typography>{" "}
        ou{" "}
        <Typography component="a" href={FormatUtils.download(data.coverLetter)} sx={{ textDecoration: "underline" }}>
          Télécharger
        </Typography>
      </Typography>
      <Typography variant="body1">
        CV :{" "}
        <Typography component="a" target="_blank" href={FormatUtils.binary(data.cv)} sx={{ textDecoration: "underline" }}>
          Ouvrir
        </Typography>{" "}
        ou{" "}
        <Typography component="a" href={FormatUtils.download(data.cv)} sx={{ textDecoration: "underline" }}>
          Télécharger
        </Typography>
      </Typography>
    </Stack>
  );
}

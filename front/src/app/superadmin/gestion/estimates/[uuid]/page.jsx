import BackButton from "@/components/buttons/BackButton";
import { BUILD_YEAR_TRANSLATED, ESTIMATE_TYPE_TRANSLATED, PROPERTY_TYPE_TRANSLATED } from "@/enums/ESTIMATES";
import { fetchApi } from "@/utils/fetch.utils";
import FormatUtils from "@/utils/format.utils";
import { Paper, Stack, Typography } from "@mui/material";

export default async function Page({ params }) {
  const data = await fetchApi(`estimates/${params.uuid}`);
  return (
    <Stack component={Paper} p={4} gap={1}>
      <BackButton>Retour</BackButton>
      <Typography variant="h4" mb={3}>
        Detail devis particulier
      </Typography>
      <Typography variant="body1">
        Créé le : {FormatUtils.formatDate(data.createdAt)} à {FormatUtils.formatTime(data.createdAt)}
      </Typography>
      <Typography variant="body1">Nom : {data.customerLastname}</Typography>
      <Typography variant="body1">Prénom : {data.customerFirstname}</Typography>
      <Typography variant="body1">Email : {data.customerEmail}</Typography>
      <Typography variant="body1">Téléphone : {data.customerPhone}</Typography>
      <Typography variant="body1">Agence : {data.agency.name}</Typography>
      <Typography variant="body1">
        Ville : {data.city.zip} {data.city.name}
      </Typography>
      <Typography variant="body1">Type de devis : {ESTIMATE_TYPE_TRANSLATED[data.type]}</Typography>
      <Typography variant="body1">Type de bien : {PROPERTY_TYPE_TRANSLATED[data.propertyType]}</Typography>
      <Typography variant="body1">Nombre de pièces : {data.roomNumber}</Typography>
      <Typography variant="body1">Année de construction : {BUILD_YEAR_TRANSLATED[data.buildYear]}</Typography>
      <Typography variant="body1">Installation gaz + de 15 ans : {data.gazOlderThan15Years ? "Oui" : "Non"}</Typography>
      <Typography variant="body1">Installation élec + de 15 ans : {data.electricityOlderThan15Years ? "Oui" : "Non"}</Typography>
    </Stack>
  );
}

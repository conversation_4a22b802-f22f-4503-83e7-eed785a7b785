import { Suspense } from "react";
import Table from "@/components/tables/Table";
import LIST_OPTIONS from "@/enums/LIST_OPTIONS";
import { fetchApi } from "@/utils/fetch.utils";

export default async function Page({ searchParams }) {
  const data = await fetchApi("posts", searchParams);

  return (
    <Suspense>
      <Table
        title="Actualités"
        data={data}
        page={searchParams.page ? searchParams.page : 1}
        searchParams={searchParams}
        options={LIST_OPTIONS.ACTUS}
      />
    </Suspense>
  );
}

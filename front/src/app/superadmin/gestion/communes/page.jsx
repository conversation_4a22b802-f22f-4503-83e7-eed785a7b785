import Table from "@/components/tables/Table";
import FormatUtils from "@/utils/format.utils";
import LIST_OPTIONS from "@/enums/LIST_OPTIONS";
import { Suspense } from "react";
import DIRECTIONS from "@/enums/DIRECTIONS";
import { fetchApi } from "@/utils/fetch.utils";

export default async function Page({ searchParams }) {
  const data = await fetchApi("location-cities", {
    "order[name]": DIRECTIONS.ASC,
    ...searchParams,
  });

  return (
    <Suspense>
      <Table
        title="Communes"
        data={data}
        page={searchParams.page ? searchParams.page : 1}
        searchParams={searchParams}
        options={LIST_OPTIONS.COMMUNES}
      />
    </Suspense>
  );
}

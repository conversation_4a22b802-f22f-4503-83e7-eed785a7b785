import { Suspense } from "react";
import Table from "@/components/tables/Table";
import LIST_OPTIONS from "@/enums/LIST_OPTIONS";
import { fetchApi } from "@/utils/fetch.utils";
import DIRECTIONS from "@/enums/DIRECTIONS";

export default async function Page({ searchParams }) {
  const data = await fetchApi("location-areas", {
    "order[name]": DIRECTIONS.ASC,
    ...searchParams,
  });

  return (
    <Suspense>
      <Table
        title="Cantons"
        data={data}
        page={searchParams.page ? searchParams.page : 1}
        searchParams={searchParams}
        options={LIST_OPTIONS.CANTONS}
      />
    </Suspense>
  );
}

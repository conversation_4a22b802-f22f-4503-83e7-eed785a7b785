import Table from "@/components/tables/Table";
import DIRECTIONS from "@/enums/DIRECTIONS";
import LIST_OPTIONS from "@/enums/LIST_OPTIONS";
import { fetchApi } from "@/utils/fetch.utils";
import TranslationUtils from "@/utils/translation.utils";
import React, { Suspense } from "react";

export default async function Page({ searchParams }) {
  const data = await fetchApi("pro-estimates", {
    "order[createdAt]": DIRECTIONS.DESC,
    ...searchParams,
  });

  return (
    <Suspense>
      <Table
        title={TranslationUtils.get("pro_estimates._plural")}
        data={data}
        page={searchParams.page ? searchParams.page : 1}
        searchParams={searchParams}
        options={LIST_OPTIONS.PRO_ESTIMATES}
        readOnly
      />
    </Suspense>
  );
}

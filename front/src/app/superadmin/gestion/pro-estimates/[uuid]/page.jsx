import BackButton from "@/components/buttons/BackButton";
import { QUOTE_MISSIONS } from "@/enums/QUOTE_MISSIONS";
import { fetchApi } from "@/utils/fetch.utils";
import FormatUtils from "@/utils/format.utils";
import TranslationUtils from "@/utils/translation.utils";
import { Paper, Stack, Typography } from "@mui/material";
import React from "react";

export default async function Page({ params }) {
  const data = await fetchApi(`pro-estimates/${params.uuid}`);
  return (
    <Stack component={Paper} p={4} gap={1}>
      <BackButton>Retour</BackButton>
      <Typography variant="h4" mb={3}>
        Detail devis pro
      </Typography>
      <Typography variant="body1">
        Créé le : {FormatUtils.formatDate(data.createdAt)} à {FormatUtils.formatTime(data.createdAt)}
      </Typography>
      <Typography variant="body1">Civilité : {TranslationUtils.get(`civility.${data.civility}`)}</Typography>
      <Typography variant="body1">Nom : {data.lastname}</Typography>
      <Typography variant="body1">Prénom : {data.firstname}</Typography>
      <Typography variant="body1">Email : {data.email}</Typography>
      <Typography variant="body1">Société : {data.company}</Typography>
      <Typography variant="body1">
        Ville : {data.city.zip} {data.city.name}
      </Typography>
      <Typography variant="body1">
        Type de missions à réaliser :{" "}
        {Object.keys(QUOTE_MISSIONS)
          .filter((key) => data[QUOTE_MISSIONS[key]])
          .map((key) => TranslationUtils.get(`quote_missions.${key}`))
          .join(", ")}
      </Typography>
      {data.customMissions && <Typography variant="body1">Mission spécifique : {data.customMissions}</Typography>}
    </Stack>
  );
}

import { Suspense } from "react";
import Table from "@/components/tables/Table";
import FormatUtils from "@/utils/format.utils";
import LIST_OPTIONS from "@/enums/LIST_OPTIONS";
import TranslationUtils from "@/utils/translation.utils";
import { fetchApi } from "@/utils/fetch.utils";
import DIRECTIONS from "@/enums/DIRECTIONS";

export default async function PagePartners({ searchParams }) {
  const data = await fetchApi("partners", {
    "order[name]": DIRECTIONS.ASC,
    ...searchParams,
  });

  return (
    <Suspense>
      <Table
        title={TranslationUtils.get("partners._plural")}
        data={data}
        page={searchParams.page ? searchParams.page : 1}
        searchParams={searchParams}
        options={LIST_OPTIONS.PARTNERS}
      />
    </Suspense>
  );
}

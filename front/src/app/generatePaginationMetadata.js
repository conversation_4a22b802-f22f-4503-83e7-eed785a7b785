// This file is here because it uses the headers from the server, which needs to be in the app directory
// Check actions next js docs for more info

import { headers } from "next/headers";

export default function generatePaginationMetadata(data, searchParams) {
  const currentPage = Number.parseInt(searchParams.page || "1", 10);
  const pages = Math.ceil(data.totalItems / 5);

  const headersList = headers();
  const pathname = headersList.get("x-pathname") || "/";

  let nextUrl = null;
  let prevUrl = null;

  if (currentPage < pages) {
    nextUrl = `${process.env.NEXT_PUBLIC_API_URL}${pathname}?page=${currentPage + 1}`;
  }

  if (currentPage > 1) {
    prevUrl = `${process.env.NEXT_PUBLIC_API_URL}${pathname}?page=${currentPage - 1}`;
  }

  const array = [];

  if (nextUrl) {
    array.push({ rel: "next", url: nextUrl });
  }

  if (prevUrl) {
    array.push({ rel: "prev", url: prevUrl });
  }

  return array;
}

import { notFound } from "next/navigation";
import Navbar from "@/components/public/navbar/Navbar";
import Footer from "@/components/public/footer/Footer";
import { fetchApi } from "@/utils/fetch.utils";
import PageRender from "@/components/superadmin/PageRender";

async function getData(url) {
  const headerData = await fetchApi("header");
  const footerData = await fetchApi("footer");
  const data = await fetchApi(`content/slug?slug=${url}`);
  if (data.status === 404) {
    return notFound();
  }
  return { header: headerData, footer: footerData, page: data };
}

export async function generateMetadata({ params }) {
  const url = params.hasOwnProperty("path") ? "/" + params?.path?.join("/") : "//";
  const { page } = await getData(url);
  return {
    title: page?.seo?.title,
    description: page?.seo?.description,
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_API_URL}/`,
    },
  };
}

export default async function Home({ params }) {
  const url = params.hasOwnProperty("path") ? "/" + params?.path?.join("/") : "//";
  const { page, header, footer } = await getData(url);

  return (
    <>
      <Navbar data={header} />
      <PageRender blocks={page?.page?.blocks} />
      <Footer data={footer} />
    </>
  );
}

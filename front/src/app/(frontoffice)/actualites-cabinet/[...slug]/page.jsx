import SectionTitle from "@/components/admin/SectionTitle";
import BackButton from "@/components/buttons/BackButton";
import LayoutContainer from "@/components/LayoutContainer";
import NotFound404 from "@/components/NotFound404";
import { fetchApi } from "@/utils/fetch.utils";
import FormatUtils from "@/utils/format.utils";
import { Stack, Typography } from "@mui/material";

const getData = async (url) => {
  try {
    const data = await fetchApi(`agency-posts?url=${url}`);
    return data.member[0];
  } catch (e) {
    console.log(e);
  }
};

export async function generateMetadata({ params }) {
  const { slug } = params;
  const post = await getData(slug?.[0]);

  return {
    title: post?.seoTitle,
    description: post?.seoDescription,
  };
}

export default async function Page({ params }) {
  const { slug } = params;
  const post = await getData(slug?.[0]);

  if (!post || post?.status === 404) {
    return <NotFound404 />;
  }

  return (
    <Stack>
      <SectionTitle text="Nos actualités" color="primary.main" />
      <LayoutContainer bgcolor="white" py={4} px={3} gap={2}>
        <Stack direction={{ xs: "column", sm: "row" }} justifyContent={{ xs: "flex-start", sm: "space-between" }}>
          <BackButton>Retour</BackButton>
          <Typography variant="subtitle1">
            {FormatUtils.date(post.createdAt)} - {post.title}
          </Typography>
        </Stack>
        <Typography variant="h1" textAlign="center">
          {post.longTitle}
        </Typography>
        <Typography component="div" dangerouslySetInnerHTML={{ __html: post.content }} variant="body1" />
      </LayoutContainer>
    </Stack>
  );
}

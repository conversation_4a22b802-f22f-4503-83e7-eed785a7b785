"use client";

import CabinetSection from "@/components/CabinetSection";
import LayoutContainer from "@/components/LayoutContainer";
import Input from "@/components/inputs/Input";
import SelectCommune from "@/components/inputs/SelectCommune";
import { useApi } from "@/context/ApiProvider";
import { BUILD_YEAR_TRANSLATED, ESTIMATE_TYPE_TRANSLATED, OPPORTUNITY_WANTED_TRANSLATED, PROPERTY_TYPE_TRANSLATED } from "@/enums/ESTIMATES";
import { Box, Button, Paper, Stack, Typography, useTheme } from "@mui/material";
import { useEffect, useState } from "react";
import PhoneIcon from "@mui/icons-material/Phone";
import Link from "next/link";
import { slugify } from "@/utils/string.utils";

export default function ConfirmDevisPage({ params }) {
  const { uuid } = params;

  const formatter = new Intl.DateTimeFormat("fr-FR", {
    weekday: "long",
    day: "numeric",
    month: "long",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    timeZone: "UTC",
  });

  const theme = useTheme();
  const { get, post, put } = useApi();
  const [estimate, setEstimate] = useState(null);
  const [zip, setZip] = useState("");
  const [city, setCity] = useState("");
  const [informations, setInformations] = useState(null);
  const [isPaying, setIsPaying] = useState(false);

  const fetchEstimate = async () => {
    try {
      const response = await get(`/estimates/${uuid}`);
      setEstimate(response);

      if (response.city.zip) {
        setZip(response.city.zip);
      }

      if (response?.customerEmail && response?.customerZip && response.customerCity && response?.customerPhone) {
        setInformations(response);
      }

      return response;
    } catch (error) {
      console.error("Error fetching estimate:", error);
      return null;
    }
  };

  useEffect(() => {
    fetchEstimate();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    const data = { ...estimate, ...Object.fromEntries(new FormData(e.target)) };
    const obj = { ...data, postCode: zip, customerCity: city?.value };
    setInformations({ ...data, postCode: zip, city: city?.label });

    await put(`/estimates/${uuid}`, obj);
  };

  if (!estimate) {
    return (
      <LayoutContainer>
        <Paper sx={{ p: 2, my: 2 }}>
          <Stack>
            <Typography>Chargement...</Typography>
          </Stack>
        </Paper>
      </LayoutContainer>
    );
  }

  const pay = async (isMultiple = false) => {
    setIsPaying(true);
    if (!estimate) {
      alert("Impossible de payer, devis non trouvé");
    }

    try {
      var response = await post("/lyra-orders", {
        estimate: estimate["@id"],
        isMultiple: isMultiple,
      });

      if (response.url) {
        window.open(response.url, "_blank").focus();
      }
    } catch (e) {
      alert("Impossible de payer en ligne, merci de contacter votre diagnostiqueur");
    }
    setIsPaying(false);
  };

  if (false === estimate.appointment) {
    return (
      <LayoutContainer>
        <Stack
          width="100%"
          justifyContent="center"
          alignItems="center"
          gap={1}
          sx={{ py: 3, px: 4, backgroundColor: theme.palette.secondary.main, color: "white" }}
        >
          <Typography variant="h3">{estimate?.agency?.name}</Typography>
          <Typography>{estimate?.agency?.contact.name}</Typography>
          <Typography>{estimate?.agency?.location.city}</Typography>
          <Stack direction="row" alignItems="center" gap={1}>
            <PhoneIcon fontSize="small" />
            <Typography variant="bold">{estimate?.agency?.contact?.phone?.replace(/(\d{2})(?=\d)/g, "$1 ")}</Typography>
          </Stack>
          {estimate?.agency ? (
            <Button
              component={Link}
              href={`/detail-cabinet/${slugify(estimate?.agency?.name)}/${estimate?.agency?.location?.postcode?.slice(0, 2)}/${slugify(
                estimate?.agency?.location?.city
              )}/${estimate?.agency?.uuid}`}
            >
              Accéder à la fiche franchisé
            </Button>
          ) : (
            <Button component={Link} href={`${settings?.quoteLink || "/"}`}>
              Prendre un rendez-vous
            </Button>
          )}
        </Stack>
        <Paper sx={{ p: 2, my: 2 }}>
          <Stack>
            <Typography variant="h3" textAlign="center" textTransform="uppercase">
              Vous allez recevoir votre devis par email ! <br />
              Merci de votre confiance
            </Typography>
          </Stack>
        </Paper>
      </LayoutContainer>
    );
  }

  return (
    <LayoutContainer gap={1} my={2}>
      <CabinetSection agency={estimate?.agency} />
      {informations && estimate.isPaid === false ? (
        <Paper sx={{ py: 3, px: 4, backgroundColor: theme.palette.green.main, color: "white" }}>
          <Stack>
            <Typography variant="h3" textAlign="center" color="white" textTransform="uppercase">
              Vous allez recevoir votre rendez-vous par email.
            </Typography>
          </Stack>
        </Paper>
      ) : null}
      {estimate.isPaid ? (
        <Paper sx={{ py: 3, px: 4, backgroundColor: theme.palette.green.main, color: "white" }}>
          <Stack>
            <Typography variant="h3" textAlign="center" color="white" textTransform="uppercase">
              Commande déjà payé !
            </Typography>
          </Stack>
        </Paper>
      ) : null}
      <Paper sx={{ py: 3, px: 4 }}>
        <Stack gap={2}>
          <Typography variant="h6" textTransform="uppercase" textAlign="center">
            Nous vous confirmons votre rendez-vous :
          </Typography>
          <Stack direction="row" gap={2}>
            <Stack gap={0} width="100%">
              <Typography variant="bold" textAlign="center">
                Date du rendez-vous
              </Typography>
              <Box bgcolor={theme.palette.secondary.main} p={2} borderRadius={1}>
                <Typography variant="h3" color="white" textAlign="center">
                  {estimate?.appointmentDate ? formatter.format(new Date(estimate.appointmentDate)) : "Aucune"}
                </Typography>
              </Box>
            </Stack>
            <Stack gap={0} width="100%">
              <Typography variant="bold" textAlign="center">
                Prix du diagnostic
              </Typography>
              <Box bgcolor={theme.palette.primary.main} p={2} borderRadius={1}>
                <Typography variant="h3" color="white" textAlign="center">
                  {
                    estimate?.appointment ?
                      `${Number(estimate.appointmentPrice).toFixed(2)}€`
                      : estimate?.price ? `${Number(estimate.price).toFixed(2)}€` : "N/A"
                  }
                </Typography>
              </Box>
            </Stack>
          </Stack>
          {informations === null ? <>Avant de procéder au paiement, merci de saisir vos informations personelles.</> : <></>}
          {estimate.isPaid ? (
            <>À très vite pour votre diagnostic !</>
          ) : (
            <Stack direction="row" gap={2}>
              <Button variant="outlined" color="secondary" fullWidth size="large" disabled={
                informations === null
                || isPaying
                || estimate.agency?.lyraSeller === null
              } onClick={() => pay()}>
                Payer par CB
              </Button>
              {estimate?.agency?.enablePaymentThreeTimeNoFee ? (
                <Button
                  variant="outlined"
                  color="secondary"
                  fullWidth
                  size="large"
                  disabled={informations === null || isPaying}
                  onClick={() => pay(true)}
                >
                  Payer par CB 3X sans frais
                </Button>
              ) : null}
            </Stack>
          )}
        </Stack>
      </Paper>
      {informations ? null : (
        <Paper sx={{ py: 3, px: 4 }}>
          <Stack component="form" gap={2} onSubmit={handleSubmit}>
            <Stack display="grid" gridTemplateColumns="1fr 1fr" gap={2}>
              <Input label="Nom" name="customerLastname" fullWidth required />
              <Input label="Prénom" name="customerFirstname" fullWidth required />
              <Input label="Email" name="customerEmail" fullWidth type="email" required defaultValue={estimate.customerEmail} />
              <Input label="Adresse" name="customerAddress" fullWidth required />
              <Input label="Code Postal" name="customerZip" value={zip} onChange={(e) => setZip(e.target.value)} fullWidth required />
              {zip?.length !== 5 ? (
                <Input required placeholder="Commune*" disabled fullWidth label={"Commune"} />
              ) : (
                <SelectCommune name="customerCity" zip={zip} defaultValue={city} onChange={(e) => setCity(e)} required />
              )}
              <Input label="Téléphone" name="customerPhone" fullWidth required defaultValue={estimate.customerPhone} />
              <Box gridColumn={"1 / span 2"}>
                <Input label="Commentaire" name="customerCommentary" fullWidth rows={3} multiline />
              </Box>
            </Stack>
            <Button type="submit" variant="outlined" color="secondary" size="large" fullWidth>
              Confirmer et recevoir ce rendez-vous par mail
            </Button>
          </Stack>
        </Paper>
      )}

      <Paper sx={{ py: 3, px: 4 }}>
        <Stack gap={2}>
          <Typography>Ce prix a été étudié en fonction de vos critères.</Typography>
          <Stack gap={1} py={3} px={4} borderRadius={2} border={`1px solid ${theme.palette.secondary.main}`}>
            <Typography variant="bold" sx={{ color: theme.palette.secondary.main, textTransform: "uppercase" }}>
              Votre bien situé dans le code postal : {estimate.city.zip}
            </Typography>
            <ul style={{ fontFamily: "var(--font-montserrat-regular)", fontSize: "14px" }}>
              <li>{ESTIMATE_TYPE_TRANSLATED[estimate.type]}</li>
              <li>{PROPERTY_TYPE_TRANSLATED[estimate.propertyType]}</li>
              <li>{estimate.roomNumber} pièce(s)</li>
              <li>{BUILD_YEAR_TRANSLATED[estimate.buildYear]}</li>
              <li>Installation gaz de plus de 15 ans: {estimate.gazOlderThan15Years ? "Oui" : "Non"}</li>
              <li>Installation électrique de plus de 15 ans: {estimate.electricityOlderThan15Years ? "Oui" : "Non"}</li>
              <li>{estimate.city.name}</li>
            </ul>
            {informations ? (
              <>
                <Typography variant="bold" sx={{ color: theme.palette.secondary.main, textTransform: "uppercase", pt: 2 }}>
                  Vos informations
                </Typography>
                <ul style={{ fontFamily: "var(--font-montserrat-regular)", fontSize: "14px" }}>
                  <li>Nom : {informations.customerLastname}</li>
                  <li>Prénom : {informations.customerFirstname}</li>
                  <li>Email : {informations.customerEmail}</li>
                  <li>Adresse : {informations.customerAddress}</li>
                  <li>Code postal : {informations.customerZip}</li>
                  <li>Commune : {informations.customerCity}</li>
                  <li>Téléphone : {informations.customerPhone}</li>
                  <li>Commentaires : {informations.customerComment}</li>
                </ul>
              </>
            ) : null}
          </Stack>
          <Stack gap={1} py={3} px={4} borderRadius={2} border={`1px solid ${theme.palette.secondary.main}`}>
            <Typography variant="bold" sx={{ color: theme.palette.secondary.main, textTransform: "uppercase" }}>
              Les diagnostics
            </Typography>
            <Stack display="grid" gridTemplateColumns="repeat(2, 1fr)" gap={2}>
              {Object.keys(estimate.opportunityWanted).map((opportunity) => {
                return estimate.opportunityWanted?.[opportunity] === true ? (
                  <Button
                    key={opportunity}
                    variant={estimate.opportunityWanted?.[opportunity] ? "contained" : "outlined"}
                    fullWidth
                    color="secondary"
                    size="large"
                  >
                    {OPPORTUNITY_WANTED_TRANSLATED[opportunity]}
                  </Button>
                ) : null;
              })}
            </Stack>
          </Stack>
        </Stack>
      </Paper>
    </LayoutContainer>
  );
}

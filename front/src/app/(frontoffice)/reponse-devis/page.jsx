"use client";

import { useEffect, useState } from "react";
import { <PERSON>ton, Divider, <PERSON>, Stack, Typography, useTheme } from "@mui/material";
import LayoutContainer from "@/components/LayoutContainer";
import { useApi } from "@/context/ApiProvider";
import {
  BUILD_YEAR_TRANSLATED,
  ESTIMATE_TYPE,
  ESTIMATE_TYPE_TRANSLATED,
  OPPORTUNITY_WANTED,
  OPPORTUNITY_WANTED_AUDIT,
  OPPORTUNITY_WANTED_SELL,
  OPPORTUNITY_WANTED_TRANSLATED,
  PROPERTY_TYPE_TRANSLATED,
} from "@/enums/ESTIMATES";
import Input from "@/components/inputs/Input";
import CreditCardIcon from "@mui/icons-material/CreditCard";
import CabinetSection from "@/components/CabinetSection";
import PhoneIcon from "@mui/icons-material/Phone";
import Link from "next/link";
import { slugify } from "@/utils/string.utils";
import { useRouter } from "next/navigation";
import FormatUtils from "@/utils/format.utils";
import Loader from "@/components/Loader";
import CardCabinetDevis from "@/components/cards/CardCabinetDevis";
import { CheckBox, CheckBoxOutlineBlank } from "@mui/icons-material";
import { useSettings } from "@/context/SettingsProvider";

export default function ReponseDevisPage() {
  const theme = useTheme();
  const router = useRouter();
  const { post } = useApi();
  const { settings } = useSettings();

  const [estimate, setEstimate] = useState(null);
  const [calculated, setCalculated] = useState(null);
  const [opportunityWanted, setOpportunityWanted] = useState(null);
  const [datesRdv, setDatesRdv] = useState(null);
  const [selectedDate, setSelectedDate] = useState(null);

  const formatter = new Intl.DateTimeFormat("fr-FR", {
    weekday: "long",
    day: "numeric",
    month: "long",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    timeZone: "UTC",
  });

  const fetchCalculate = async (data) => {
    try {
      const response = await post("/estimates/calculate", data);
      setCalculated(response);
      defineDatesRdv(response);
      setOpportunityWanted(response.opportunityWanted);
    } catch (error) {
      console.log(error);
    }
  };

  const defineDatesRdv = (response) => {
    const proposal = response.proposal;
    setDatesRdv({
      [`${proposal.firstDate}`]: {
        datetime: formatter.format(new Date(proposal.firstDate)).replace(/(\d+)/, "$1").replace(/:/g, "h"),
        price: proposal.firstPrice,
      },
      [`${proposal.secondDate}`]: {
        datetime: formatter.format(new Date(proposal.secondDate)).replace(/(\d+)/, "$1").replace(/:/g, "h"),
        price: proposal.secondPrice,
      },
      null: {
        datetime: "C'est urgent, une autre date ?",
        price: 0,
      },
    });
  };

  const submitCalculate = async (e) => {
    e?.preventDefault();
    const calculateData = {
      ...calculated,
      appointmentDate: selectedDate === "null" ? null : selectedDate,
      opportunityWanted: opportunityWanted,
      discountCode: e?.target.discountCode?.value === "" ? null : e?.target.discountCode?.value,
      city: calculated?.city?.["@id"],
      agency: calculated?.agency?.["@id"],
    };

    if (calculateData) {
      delete calculateData["@id"];
    }

    const response = await post("/estimates/recalculate", calculateData);
    defineDatesRdv(response);
    setEstimate(response);
    setCalculated(response);
    setOpportunityWanted(response.opportunityWanted);
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  const submitRecalculate = async (e, date = "null") => {
    e?.preventDefault();
    const recalculateData = {
      ...calculated,
      appointmentDate: date !== "null" ? date : selectedDate === "null" ? null : selectedDate,
      opportunityWanted,
      discountCode: e?.target?.discountCode?.value === "" ? null : e?.target?.discountCode?.value,
      city: calculated?.city?.["@id"],
      agency: calculated?.agency?.["@id"],
    };

    if (recalculateData) {
      delete recalculateData["@id"];
    }

    const response = await post("/estimates", recalculateData);
    router.push(`/reponse-devis/${response.uuid}`);
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  useEffect(() => {
    const storedEstimate = localStorage.getItem("estimate");
    if (storedEstimate) {
      const parsedEstimate = JSON.parse(storedEstimate);
      setEstimate(parsedEstimate);
      fetchCalculate(parsedEstimate);
    }
  }, []);

  if (!estimate || !calculated) {
    return <Loader />;
  }

  const agency = calculated?.agency;

  if (!agency) {
    return (
      <LayoutContainer>
        <Paper sx={{ p: 4, my: 2, backgroundColor: theme.palette.secondary.main, color: "white" }}>
          <Stack gap={2}>
            <Stack width="100%" justifyContent="center" alignItems="center" gap={1}>
              <Typography variant="h3" textAlign="center">
                Nous n&apos;avons pas encore de diagnostiqueur disponible sur ce code postal.
              </Typography>
              <Typography variant="h6" textAlign="center">
                Vous pouvez consultez notre annuaire pour voir s&apos;il existe un diagnostiqueur proche de chez vous que vous pouvez contacter
                directement.
              </Typography>
              <Typography>
                Avec plus de 156 diagnostiqueurs AGENDA, nous avons encore quelques secteurs vides, mais nous faisons tout notre possible pour étendre
                notre réseau !
              </Typography>
            </Stack>
            <Button component={Link} href="/trouvez-votre-diagnostiqueur" sx={{ mt: 1 }}>
              Recherchez un autre diagnostiqueur
            </Button>
          </Stack>
        </Paper>
      </LayoutContainer>
    );
  }

  if (!calculated?.price) {
    return (
      <LayoutContainer>
        <Paper sx={{ p: 4, my: 2, backgroundColor: theme.palette.secondary.main, color: "white" }}>
          <Stack gap={2}>
            <Stack width="100%" justifyContent="center" alignItems="center" gap={1}>
              <Typography>
                Le calcul du devis n’a pas pu aboutir, mais un diagnostiqueur est bien disponible dans votre secteur.
                <br />
                Contactez directement votre diagnostiqueur pour obtenir un devis personnalisé et adapté à votre besoin. :
              </Typography>
              <Typography variant="h6">{agency?.location.city}</Typography>
              <Typography>{agency?.contact.name}</Typography>
              <Stack direction="row" alignItems="center" gap={1}>
                <PhoneIcon fontSize="small" />
                <Typography variant="bold">{agency?.contact?.phone?.replace(/(\d{2})(?=\d)/g, "$1 ")}</Typography>
              </Stack>
              {agency ? (
                <Button
                  component={Link}
                  href={`/detail-cabinet/${slugify(agency?.name)}/${agency?.location?.postcode?.slice(0, 2)}/${slugify(agency?.location?.city)}/${
                    agency?.uuid
                  }`}
                >
                  Accéder à la fiche franchisé
                </Button>
              ) : (
                <Button component={Link} href={`${settings?.quoteLink || "/"}`}>
                  Prendre un rendez-vous
                </Button>
              )}
              <Button component={Link} href="/trouvez-votre-diagnostiqueur" sx={{ mt: 1 }}>
                Recherchez un autre diagnostiqueur
              </Button>
            </Stack>
          </Stack>
        </Paper>
      </LayoutContainer>
    );
  }

  if (agency?.displayCallIfAvailableNow && agency?.isAvailable) {
    return (
      <LayoutContainer>
        <Paper sx={{ p: 4, my: 2, backgroundColor: theme.palette.secondary.main, color: "white" }}>
          <Stack gap={2}>
            <Stack width="100%" justifyContent="center" alignItems="center" gap={1}>
              <Typography variant="h3">Nous sommes disponible dès maintenant par téléphone,</Typography>
              <Typography variant="h6">Appelez nous pour un devis gratuit.</Typography>
            </Stack>
            <Divider sx={{ width: "100%", bgcolor: "#ffffffaa" }} />
            <Stack width="100%" justifyContent="center" alignItems="center" gap={1}>
              <Typography variant="h3">{agency?.name}</Typography>
              <Typography>{agency?.contact.name}</Typography>
              <Typography>{agency?.location.city}</Typography>
              <Stack direction="row" alignItems="center" gap={1}>
                <PhoneIcon fontSize="small" />
                <Typography variant="bold">{agency?.contact?.phone?.replace(/(\d{2})(?=\d)/g, "$1 ")}</Typography>
              </Stack>
              {agency ? (
                <Button
                  component={Link}
                  href={`/detail-cabinet/${slugify(agency?.name)}/${agency?.location?.postcode?.slice(0, 2)}/${slugify(agency?.location?.city)}/${
                    agency?.uuid
                  }`}
                >
                  Accéder à la fiche franchisé
                </Button>
              ) : (
                <Button component={Link} href={`${settings?.quoteLink || "/"}`}>
                  Prendre un rendez-vous
                </Button>
              )}
            </Stack>
          </Stack>
        </Paper>
      </LayoutContainer>
    );
  }

  const opportunities =
    estimate.type === ESTIMATE_TYPE.AUDIT
      ? OPPORTUNITY_WANTED_AUDIT
      : estimate.type === ESTIMATE_TYPE.SELL
      ? OPPORTUNITY_WANTED_SELL
      : OPPORTUNITY_WANTED;

  return (
    <LayoutContainer gap={1} my={2}>
      <CabinetSection agency={agency} />
      <CardCabinetDevis agency={agency} />
      <Paper sx={{ py: 3, px: 4 }}>
        <Stack justifyContent="center" alignItems="center" gap={2} component="form" onSubmit={submitRecalculate}>
          <Typography variant="bold" textTransform="uppercase">
            Notre prix pour votre demande de diagnostic est :
          </Typography>
          <Stack
            gap={0}
            width="100%"
            borderRadius={2}
            border="1px solid"
            direction={{ xs: "column", sm: "row" }}
            justifyContent="space-between"
            alignItems="center"
          >
            <Stack width="100%" py={2}>
              <Typography variant="bold" textAlign="center" fontSize={{ xs: 26, sm: 33 }}>
                {calculated.proposal?.secondPrice
                  ? calculated.proposal.secondPrice === calculated.price
                    ? `${FormatUtils.spaceOnNumbers(calculated.price.toFixed(2))}€`
                    : `de ${FormatUtils.spaceOnNumbers(calculated.proposal?.secondPrice.toFixed(2))}€ à ${FormatUtils.spaceOnNumbers(
                        calculated.price.toFixed(2)
                      )}€`
                  : `${FormatUtils.spaceOnNumbers(calculated.price.toFixed(2))}€`}
              </Typography>
              <Typography textAlign="center">selon les conditions de paiement et d&apos;intervention</Typography>
            </Stack>
            {agency?.enablePaymentThreeTimeNoFee ? (
              <Stack gap={0} width="100%" bgcolor={theme.palette.primary.main} alignItems="center" p={2} sx={{ borderRadius: 1.8 }}>
                {/* Faîtes pas attention aux borders radius, c'est du détail pour éviter les espacements blancs d'1-2 pixels entre eux */}
                <Typography variant="bold" textTransform="uppercase" color="white" textAlign="center" px={1}>
                  Prenez rendez-vous et payer votre diagnostic en ligne et bénéficiez du 3x sans frais
                </Typography>
                <CreditCardIcon sx={{ fontSize: 40, color: "white" }} />
              </Stack>
            ) : null}
          </Stack>
          {agency?.displayAppointment ? (
            <>
              <Typography variant="bold" textTransform="uppercase" mt={2}>
                Nous vous proposons un rendez-vous :
              </Typography>
              <Stack width="100%" borderRadius={2} border={`1px solid ${theme.palette.secondary.main}`}>
                {Object.keys(datesRdv).map((date, index) => (
                  <Stack
                    key={`${date}-${index}`}
                    direction="row"
                    justifyContent="space-between"
                    alignItems="center"
                    px={2}
                    py={1}
                    borderBottom={index !== Object.keys(datesRdv).length - 1 ? `1px solid ${theme.palette.secondary.main}` : "none"}
                    bgcolor={selectedDate === date ? theme.palette.secondary.main : "white"}
                    color={selectedDate === date ? "white" : "inherit"}
                    // Faîtes pas attention aux borders radius, c'est du détail pour éviter les espacements blancs d'1-2 pixels entre eux
                    sx={{
                      borderTopLeftRadius: index === 0 ? 8 : 0,
                      borderTopRightRadius: index === 0 ? 8 : 0,
                      borderBottomLeftRadius: index === Object.keys(datesRdv).length - 1 ? 8 : 0,
                      borderBottomRightRadius: index === Object.keys(datesRdv).length - 1 ? 8 : 0,
                      cursor: "pointer",
                    }}
                    onClick={() => setSelectedDate(date)}
                    height={50}
                  >
                    <Typography
                      variant="bold"
                      textTransform="uppercase"
                      sx={{ color: selectedDate === date ? "white" : theme.palette.secondary.main, fontSize: { xs: 12, sm: 16 } }}
                    >
                      <span>{datesRdv[date].datetime}</span>
                      <span> </span>
                      <span>{datesRdv[date].price ? `(${FormatUtils.spaceOnNumbers(Number(datesRdv[date].price).toFixed(2))}€)` : null}</span>
                    </Typography>
                    {selectedDate !== date ? (
                      <Button
                        type="button"
                        variant="text"
                        color="secondary"
                        size="small"
                        onClick={(e) => {
                          setSelectedDate(date);
                          submitRecalculate(e, date);
                        }}
                        sx={{ fontSize: { xs: 12, sm: 16 } }}
                      >
                        Je valide cette date
                      </Button>
                    ) : (
                      <Typography sx={{ fontSize: { xs: 12, sm: 16 } }}>Date sélectionnée</Typography>
                    )}
                  </Stack>
                ))}
              </Stack>
            </>
          ) : null}
          <Button type="submit" variant="contained" color="secondary" size="large" fullWidth>
            {selectedDate === "null" ? "Valider et recevoir ce devis par mail" : "Recevoir ce devis par mail"}
          </Button>
        </Stack>
      </Paper>
      <Paper sx={{ py: 3, px: 4 }}>
        <Stack gap={2} component="form" onSubmit={submitCalculate}>
          <Typography>Ce prix a été étudié en fonction de vos critères.</Typography>
          <Stack gap={1} py={3} px={4} borderRadius={2} border={`1px solid ${theme.palette.secondary.main}`}>
            <Typography variant="bold" sx={{ color: theme.palette.secondary.main, textTransform: "uppercase" }}>
              Votre bien situé dans le code postal : {calculated?.city?.zip}
            </Typography>
            <ul style={{ fontFamily: "var(--font-montserrat-regular)", fontSize: "14px" }}>
              <li>{ESTIMATE_TYPE_TRANSLATED[estimate.type]}</li>
              <li>{PROPERTY_TYPE_TRANSLATED[estimate.propertyType]}</li>
              <li>{estimate.roomNumber} pièce(s)</li>
              {BUILD_YEAR_TRANSLATED?.[estimate?.buildYear] ? <li>{BUILD_YEAR_TRANSLATED[estimate.buildYear]}</li> : null}
              <li>Installation gaz de plus de 15 ans: {estimate.gazOlderThan15Years ? "oui" : "non"}</li>
              <li>Installation électrique de plus de 15 ans: {estimate.electricityOlderThan15Years ? "oui" : "non"}</li>
              <li>{calculated?.city?.name}</li>
              <li>{estimate.customerPhone}</li>
              <li>{estimate.customerEmail}</li>
            </ul>
          </Stack>
          <Stack gap={1} py={3} px={4} borderRadius={2} border={`1px solid ${theme.palette.secondary.main}`}>
            <Typography variant="bold" sx={{ color: theme.palette.secondary.main, textTransform: "uppercase" }}>
              Les diagnostics
            </Typography>
            <Typography>
              Si vous souhaitez supprimer ou ajouter un diagnostic, vous pouvez recalculer votre devis en modifiant vos sélections :
            </Typography>
            <Stack display="grid" gridTemplateColumns={{ xs: "1fr", sm: "repeat(2, 1fr)" }} gap={{ xs: 1, sm: 2 }}>
              {Object.values(opportunities).map((opportunity) => {
                return (
                  <Button
                    key={opportunity}
                    variant={opportunityWanted?.[opportunity] ? "contained" : "outlined"}
                    fullWidth
                    color="secondary"
                    size="large"
                    onClick={
                      estimate.type === ESTIMATE_TYPE.AUDIT
                        ? () => {}
                        : () => {
                            setOpportunityWanted((prev) => ({
                              ...prev,
                              [opportunity]: !prev?.[opportunity],
                            }));
                          }
                    }
                  >
                    <Stack direction="row" alignItems="center" gap={2} width="100%">
                      {opportunityWanted?.[opportunity] ? <CheckBox sx={{ color: "white" }} /> : <CheckBoxOutlineBlank />}
                      {OPPORTUNITY_WANTED_TRANSLATED[opportunity]}
                    </Stack>
                  </Button>
                );
              })}
            </Stack>
          </Stack>
          {estimate.type === ESTIMATE_TYPE.AUDIT ? null : (
            <Stack direction="row" gap={2} alignItems="center">
              <Typography width="100%">Vous avez un code avantage ?</Typography>
              <Input name="discountCode" placeholder="Code avantage" fullWidth />
            </Stack>
          )}
          {estimate.type === ESTIMATE_TYPE.AUDIT ? null : (
            <Button type="submit" variant="contained" color="secondary" size="large" fullWidth>
              Recalculer
            </Button>
          )}
        </Stack>
      </Paper>
    </LayoutContainer>
  );
}

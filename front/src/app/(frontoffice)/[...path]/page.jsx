import { notFound } from "next/navigation";
import { fetchApi } from "@/utils/fetch.utils";
import { BLOC_PAGES } from "@/enums/BLOC_PAGES";
import generatePaginationMetadata from "../../generatePaginationMetadata";
import PageRender from "@/components/superadmin/PageRender";

async function getData(url) {
  const response = await fetchApi(`content/slug?slug=${url}`);
  if (response.status === 404) {
    return notFound();
  }
  return response;
}

export async function generateMetadata({ params, searchParams }) {
  const url = params.hasOwnProperty("path") ? "/" + params?.path?.join("/") : "/";
  const data = await getData(url);

  let prevNextLinks = [];

  const hasMultiplePages = data.page.blocks.map((block) => block.type).some((type) => type.includes(BLOC_PAGES));

  if (hasMultiplePages) {
    const paginatedData = await fetchApi("posts", { itemsPerPage: 19, page: searchParams.page || 1 });
    prevNextLinks = generatePaginationMetadata(paginatedData, searchParams);
  }

  return {
    title: `${data.seo?.title}${searchParams?.page ? " | Page " + searchParams.page : ""}`,
    description: `${data.seo?.description}${searchParams?.page ? " | Page " + searchParams.page : ""}`,
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_API_URL}${url}${searchParams?.page ? "?page=" + searchParams.page : ""}`,
    },
    icons: {
      other: prevNextLinks,
    },
  };
}

export default async function CMS({ params }) {
  const url = params.hasOwnProperty("path") ? "/" + params?.path?.join("/") : "/";
  const data = await getData(url);

  return <PageRender blocks={data?.page?.blocks} />;
}

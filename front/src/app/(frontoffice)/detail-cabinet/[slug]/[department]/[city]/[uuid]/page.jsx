import generatePaginationMetadata from "@/app/generatePaginationMetadata";
import Banner from "@/components/Banner";
import LayoutContainer from "@/components/LayoutContainer";
import ListActualitesShort from "@/components/admin/ListActualitesShort";
import BlockJob from "@/components/blocks/BlockJob";
import CabinetContact from "@/components/contact/CabinetContact";
import Reviews from "@/components/reviews/Reviews";
import { fetchApi } from "@/utils/fetch.utils";
import FormatUtils from "@/utils/format.utils";
import { Paper, Stack, Typography } from "@mui/material";
import { headers } from "next/headers";
import Image from "next/image";

export async function generateMetadata({ params, searchParams }) {
  const data = await fetchApi(`agencies/${params.uuid}`);
  const paginatedData = await fetchApi(`agency-posts?agency.legacyId=${params.uuid}&itemsPerPage=5`);

  const prevNextLinks = generatePaginationMetadata(paginatedData, searchParams);

  const headersList = headers();
  const pathname = headersList.get("x-pathname") || "/";

  return {
    title: `${data.meta?.title}${searchParams.page ? " | Page " + searchParams.page : ""}`,
    description: `${data.meta?.description}${searchParams.page ? " | Page " + searchParams.page : ""}`,
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_API_URL}${pathname}${searchParams.page ? "?page=" + searchParams.page : ""}`,
    },
    icons: {
      other: prevNextLinks,
    },
  };
}

export default async function DetailCabinetPage({ params }) {
  const cabinet = await fetchApi(`agencies/${params.uuid}`);

  if (!cabinet) {
    return null;
  }

  const headersList = headers();
  const pathname = headersList.get("x-pathname") || "/";

  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: cabinet.name,
    address: {
      "@type": "PostalAddress",
      streetAddress: cabinet.location.address1,
      addressLocality: cabinet.location.city,
      postalCode: cabinet.location.postcode,
      addressCountry: "FR",
    },
    geo: {
      "@type": "GeoCoordinates",
      latitude: cabinet.location.latitude,
      longitude: cabinet.location.longitude,
    },
    url: `${process.env.NEXT_PUBLIC_API_URL}${pathname}`,
    telephone: cabinet.contact.phone,
    openingHoursSpecification: [
      {
        "@type": "OpeningHoursSpecification",
        dayOfWeek: ["Monday"],
        opens: cabinet.schedule?.monday?.morning?.openAt,
        closes: cabinet.schedule?.monday?.morning?.closeAt,
      },
      {
        "@type": "OpeningHoursSpecification",
        dayOfWeek: ["Tuesday"],
        opens: cabinet.schedule?.tuesday?.morning?.openAt,
        closes: cabinet.schedule?.tuesday?.morning?.closeAt,
      },
      {
        "@type": "OpeningHoursSpecification",
        dayOfWeek: ["Wednesday"],
        opens: cabinet.schedule?.wednesday?.morning?.openAt,
        closes: cabinet.schedule?.wednesday?.morning?.closeAt,
      },
      {
        "@type": "OpeningHoursSpecification",
        dayOfWeek: ["Thursday"],
        opens: cabinet.schedule?.thursday?.morning?.openAt,
        closes: cabinet.schedule?.thursday?.morning?.closeAt,
      },
      {
        "@type": "OpeningHoursSpecification",
        dayOfWeek: ["Friday"],
        opens: cabinet.schedule?.friday?.morning?.openAt,
        closes: cabinet.schedule?.friday?.morning?.closeAt,
      },
      {
        "@type": "OpeningHoursSpecification",
        dayOfWeek: ["Saturday"],
        opens: cabinet.schedule?.saturday?.morning?.openAt,
        closes: cabinet.schedule?.saturday?.morning?.closeAt,
      },
      {
        "@type": "OpeningHoursSpecification",
        dayOfWeek: ["Sunday"],
        opens: cabinet.schedule?.sunday?.morning?.openAt,
        closes: cabinet.schedule?.sunday?.morning?.closeAt,
      },
    ],
  };

  return (
    <Stack gap={4} mb={2}>
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }} />
      <Banner title={cabinet.name} />
      <LayoutContainer>
        <Stack gap={4}>
          <CabinetContact cabinet={cabinet} />
          <Reviews js={cabinet.ratingWidget?.js} html={cabinet.ratingWidget?.html} />
          <Stack component={Paper} p={4}>
            <Stack width="100%" overflow="hidden" position="relative" height={{ xs: 100, sm: 250, xl: 300 }} alignItems="center">
              <Image
                src={cabinet.upload ? FormatUtils.binary(cabinet.upload) : "/assets/images/header.jpg"}
                alt={cabinet.name}
                fill
                style={{ objectFit: "contain" }}
              />
            </Stack>
            <Typography
              component="div"
              sx={{
                wordBreak: "break-word",
              }}
              dangerouslySetInnerHTML={{
                __html: cabinet.description,
              }}
            />
          </Stack>
          <ListActualitesShort cabinetId={params.uuid} />
          <BlockJob agencyIRI={`/api/agencies/${cabinet.uuid}`} />
        </Stack>
      </LayoutContainer>
    </Stack>
  );
}

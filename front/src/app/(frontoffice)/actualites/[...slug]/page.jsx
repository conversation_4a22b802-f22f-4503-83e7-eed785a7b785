import SectionTitle from "@/components/admin/SectionTitle";
import BackButton from "@/components/buttons/BackButton";
import LayoutContainer from "@/components/LayoutContainer";
import NotFound404 from "@/components/NotFound404";
import { fetchApi } from "@/utils/fetch.utils";
import FormatUtils from "@/utils/format.utils";
import { Stack, Typography } from "@mui/material";
import { headers } from "next/headers";

const getData = async (url) => {
  try {
    const data = await fetchApi(`post?slug=/${url}`);
    return data;
  } catch (e) {
    console.log(e);
  }
};

export async function generateMetadata({ params }) {
  const { slug } = params;
  const post = await getData(slug?.[0]);

  const headersList = headers();
  const pathname = headersList.get("x-pathname");

  const upload = post?.upload ? await fetchApi(post.upload.replace("/api/", "")) : null;

  return {
    title: post?.seoTitle,
    description: post?.seoDescription,
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_API_URL}${pathname}`,
    },
    openGraph: {
      title: post?.seoTitle,
      description: post?.longTitle,
      url: `${process.env.NEXT_PUBLIC_API_URL}${pathname}`,
      images: upload
        ? [
            {
              url: FormatUtils.image(upload["@id"]),
              type: upload.type,
              width: upload.width,
              height: upload.height,
              alt: upload.alt,
            },
          ]
        : [],
    },
  };
}

export default async function Page({ params }) {
  const { slug } = params;
  const post = await getData(slug?.[0]);

  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "NewsArticle",
    headline: post?.seoTitle,
    datePublished: post.createdAt,
    dateModified: post.updatedAt,
    image: FormatUtils.binary(post.upload),
  };

  if (!post || post.status === 404) {
    return <NotFound404 />;
  }

  return (
    <Stack>
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }} />
      <SectionTitle text="Nos actualités" color="primary.main" />
      <LayoutContainer bgcolor="white" py={5} px={3} gap={2}>
        <Stack direction={{ xs: "column", sm: "row" }} justifyContent={{ xs: "flex-start", sm: "space-between" }}>
          <BackButton>Retour</BackButton>
          <Typography variant="subtitle1">
            {FormatUtils.date(post.createdAt)} - {post.title}
          </Typography>
        </Stack>
        <Typography variant="h1" textAlign="center">
          {post.longTitle}
        </Typography>
        {post.content ? <Typography component="div" dangerouslySetInnerHTML={{ __html: post.content }} variant="body1" /> : null}
      </LayoutContainer>
    </Stack>
  );
}

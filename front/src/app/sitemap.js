import { fetchApi } from "@/utils/fetch.utils";

export default async function sitemap() {
  const baseUrl = `${process.env.NEXT_PUBLIC_API_URL}`;

  // Import from utils doesn't work so pasted here manually
  const slugify = (string) => {
    if (!string) return null;
    return string
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "");
  };

  const contentsData = await fetchApi("contents");
  const contents = contentsData.member
    .map((content) =>
      content.status && content.slug !== "/"
        ? {
            url: `${baseUrl}/${content.slug}`,
            lastModified: new Date().toISOString(),
            changeFrequency: "weekly",
            priority: 0.9,
          }
        : null
    )
    .filter(Boolean);

  const agenciesData = await fetchApi("agencies-map");
  const agencies = agenciesData.member.map((agency) => ({
    url: `${baseUrl}/detail-cabinet/${slugify(agency?.name)}/${agency?.location?.postcode?.slice(0, 2)}/${slugify(agency?.location?.city)}/${
      agency?.legacyId
    }`,
    lastModified: agency.updatedAt,
    changeFrequency: "weekly",
    priority: 0.9,
  }));

  const postsData = await fetchApi("posts", { pagination: false, itemsPerPage: 1000 });
  const posts = postsData.member.map((post) => ({
    url: `${baseUrl}/actualites${post.url}`,
    lastModified: post.updatedAt,
    changeFrequency: "weekly",
    priority: 0.9,
  }));

  return [
    {
      url: baseUrl,
      lastModified: new Date().toISOString(),
      changeFrequency: "yearly",
      priority: 0.1,
    },
    ...contents,
    ...agencies,
    ...posts,
  ];
}

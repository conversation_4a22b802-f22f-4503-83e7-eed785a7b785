import localFont from "next/font/local";
import "./globals.css";
import { ThemeProvider } from "@mui/material/styles";
import theme from "@/lib/theme";
import ApiProvider from "@/context/ApiProvider";
import UserProvider from "@/context/UserProvider";
import SnackProvider from "@/context/SnackProvider";
import { GoogleTagManager } from "@next/third-parties/google";
import { NavigationGuardProvider } from "next-navigation-guard";
import { fetchApi } from "@/utils/fetch.utils";
import SettingsProvider from "@/context/SettingsProvider";

const MontserratBold = localFont({
  src: "../assets/fonts/Montserrat-Bold.ttf",
  variable: "--font-montserrat-bold",
  weight: "bold",
});
const MontserratSemiBold = localFont({
  src: "../assets/fonts/Montserrat-SemiBold.ttf",
  variable: "--font-montserrat-semi-bold",
  weight: "600",
});
const MontserratRegular = localFont({
  src: "../assets/fonts/Montserrat-Regular.ttf",
  variable: "--font-montserrat-regular",
  weight: "normal",
});
const MontserratLight = localFont({
  src: "../assets/fonts/Montserrat-Light.ttf",
  variable: "--font-montserrat-light",
  weight: "light",
});

export default async function layout({ children }) {
  const { tagManager } = await fetchApi("setting");

  return (
    <html lang="fr">
      <GoogleTagManager gtmId={tagManager} />
      <body
        suppressHydrationWarning={true}
        className={`${MontserratBold.variable} ${MontserratSemiBold.variable} ${MontserratRegular.variable} ${MontserratLight.variable}`}
      >
        <NavigationGuardProvider>
          <SnackProvider>
            <ApiProvider>
              <UserProvider>
                <SettingsProvider>
                  <ThemeProvider theme={theme}>{children}</ThemeProvider>
                </SettingsProvider>
              </UserProvider>
            </ApiProvider>
          </SnackProvider>
        </NavigationGuardProvider>
      </body>
    </html>
  );
}

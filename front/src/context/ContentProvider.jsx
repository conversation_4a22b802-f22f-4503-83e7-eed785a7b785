"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { useApi } from "./ApiProvider";

const ContentContext = createContext({});

export const useContent = () => useContext(ContentContext);

export default function ContentProvider({ children }) {
  const [contents, setContents] = useState(null);
  const { get } = useApi();

  const fetchContents = async () => {
    try {
      const c = await get("/contents");
      setContents(c.member);
      return c;
    } catch {
      console.log("error");
    }
  };

  useEffect(() => {
    void fetchContents();
  }, []);

  if (!contents) {
    return null;
  }

  return <ContentContext.Provider value={{ contents, fetchContents }}>{children}</ContentContext.Provider>;
}

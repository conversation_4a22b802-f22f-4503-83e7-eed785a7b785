"use client";

import { createContext, useContext, useState } from "react";
import { Alert, Snackbar } from "@mui/material";

const SnackContext = createContext({});

export const useSnack = () => useContext(SnackContext);

export default function SnackProvider({ children }) {
  const [snack, setSnack] = useState();

  const add = (type, content) => setSnack({ type, content });

  const handleClose = () => {
    setSnack(null);
  };

  return (
    <SnackContext.Provider value={{ add }}>
      {snack ? (
        <Snackbar open={true} autoHideDuration={6000} onClose={handleClose}>
          <Alert onClose={handleClose} severity={snack.type} variant="filled" sx={{ width: "100%" }}>
            {snack.content}
          </Alert>
        </Snackbar>
      ) : null}
      {children}
    </SnackContext.Provider>
  );
}

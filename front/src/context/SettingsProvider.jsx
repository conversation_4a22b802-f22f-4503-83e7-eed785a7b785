"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { useApi } from "./ApiProvider";
import { debounce } from "@mui/material";

const settingsContext = createContext({});

export const useSettings = () => useContext(settingsContext);

export default function SettingsProvider({ children }) {
  const [settings, setSettings] = useState(null);
  const { get } = useApi();

  const fetchSettings = async () => {
    try {
      const data = await get("/setting");
      setSettings(data);
      return data;
    } catch {
      console.log("error");
    }
  };

  const handleFetch = debounce(() => {
    fetchSettings();
  }, 100);

  useEffect(() => {
    void handleFetch();
  }, []);

  if (!settings) {
    return null;
  }

  return <settingsContext.Provider value={{ settings, fetchSettings }}>{children}</settingsContext.Provider>;
}

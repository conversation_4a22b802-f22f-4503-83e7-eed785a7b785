"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { useApi } from "./ApiProvider";
import { useSnack } from "./SnackProvider";
import USER_ROLES from "@/enums/USER_ROLES";
import ROUTES from "@/enums/ROUTES";

const UserContext = createContext({});

export const useUser = () => useContext(UserContext);

export default function UserProvider({ children }) {
  const [user, setUser] = useState(null);
  const { getToken, setToken, post, get } = useApi();
  const { add } = useSnack();

  const clearUser = () => {
    setUser(null);
  };

  const fetchUser = async () => {
    try {
      const u = await get("/user");
      setUser(u);
      return u;
    } catch {
      if (document) {
        document.location.href = ROUTES.LOGOUT;
      }
    }
  };

  const login = async ({ email, password }) => {
    try {
      const token = await post("/tokens", { email, password });
      setToken(token.token);
      const u = await fetchUser();
      add("success", `Bonjour ${u.firstname} ${u.lastname} !`);
    } catch (error) {
      throw error;
    }
  };

  // calling user every 10 minutes (ensure token will be ok)
  useEffect(() => {
    if (!getToken()) {
      clearUser();
      return;
    }
    void load();
    const id = setInterval(load, 10 * 60 * 1000);
    return () => clearInterval(id);
  }, []);

  const load = async () => {
    await fetchUser();
  };

  const update = async (u) => {
    setUser((user) => ({ ...user, ...u }));
  };

  const isSuperAdmin = () => user?.role === USER_ROLES.SUPERADMIN;

  return <UserContext.Provider value={{ user, fetchUser, login, setUser, update, isSuperAdmin }}>{children}</UserContext.Provider>;
}

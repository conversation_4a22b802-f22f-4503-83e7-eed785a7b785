"use client";

import { createContext, useContext, useState } from "react";
import { deleteCookie, getCookies, setCookie } from "cookies-next";
import ROUTES from "@/enums/ROUTES";

const ApiContext = createContext({});

/**
 * @typedef {{ id: number, email: string, isAdmin: undefined|boolean, isManager: undefined|boolean }} User
 * @returns {{ user: User, fetchUser: () => void }}
 */
export const useApi = () => useContext(ApiContext);

export default function ApiProvider({ children }) {
  const [token, setToken] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [violations, setViolations] = useState([]);
  // const location = useLocation();

  // useEffect(() => {
  //   setViolations([]);
  // }, [location]);

  const updateToken = (token) => {
    if (token) {
      setToken(token);
      setCookie("token", token);
    } else {
      setToken(null);
      deleteCookie("token");
    }
  };

  const getToken = () => {
    if (!decodeURIComponent(getCookies("token")?.token)) {
      document.location.href = `${ROUTES.LOGOUT}?t=${new Date().getTime()}`;
    }
    return token || getCookies("token")?.token ? decodeURIComponent(getCookies("token")?.token) : null;
  };

  // DELETE otherUrl prop later on
  const fetching = async (uri, config = {}, otherUrl = false) => {
    try {
      setIsLoading(true);
      setViolations([]);
      const headers = config?.headers || {};
      if (!headers["Content-Type"]) {
        headers["Content-Type"] = "application/ld+json";
      }
      const token = getToken();
      token && (headers["X-Sinfin-Token"] = token);
      config.headers = headers;
      config.cache = "no-store";

      const isJson = (rct) => ["application/json", "application/ld+json", "application/problem+json"].some((bct) => rct?.startsWith(bct));

      const callUrl = `${process.env.NEXT_PUBLIC_API_URL}/api`;

      // clean response
      const response = await fetch(`${callUrl}${uri}`, config);
      const contentType = response.headers.get("Content-Type");

      if (response.status === 422) {
        const json = await response.json();
        setViolations(json.violations);
        throw json;
      }
      if ([401, 403].includes(response.status)) {
        // throw await response.json();
        updateToken(null);
        if (document && token) {
          document.location.href = `${ROUTES.LOGOUT}?t=${new Date().getTime()}`;
        }
        return;
      }
      if ([404, 422].includes(response.status)) {
        if (isJson(contentType)) {
          throw await response.json();
        }

        throw response;
      }

      if (config.method === "DELETE") {
        return response;
      }
      if (!response.ok) {
        throw response;
      }

      // if (!isJson(contentType)) {
      // 	// setToken(null);
      // 	// document.location.href = `${
      // 	// 	ROUTES.LOGOUT
      // 	// }?t=${new Date().getTime()}`;
      // 	// return;
      // }

      // renew token
      const renew = response.headers.get("X-Sinfin-Token-Renew");
      if (renew) {
        setToken(renew);
      }

      return await response.json();
    } catch (error) {
      console.error(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const get = async (uri, params = {}, otherUrl = false) => {
    const query = [];
    flatten(query, params);

    return await fetching(`${uri}${query.length > 0 ? `?${query.join("&")}` : ""}`, {}, otherUrl);
  };

  const post = async (uri, data = {}) => {
    return await fetching(uri, {
      method: "POST",
      body: JSON.stringify(data),
      headers: {
        "Content-Type": "application/ld+json",
      },
    });
  };

  const put = async (uri, data = {}) => {
    return await fetching(uri, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  };

  const patch = async (uri, data = {}) => {
    return await fetching(uri, {
      method: "PATCH",
      body: JSON.stringify(data),
      headers: {
        "Content-Type": "application/merge-patch+json",
      },
    });
  };

  const del = async (uri) => {
    return await fetching(uri, {
      method: "DELETE",
    });
  };

  const flatten = (arr, params) => {
    Object.keys(params).forEach((key) => {
      const value = params[key];
      if (key === "filters" || key === "orders") {
        arr.push(`${decodeURIComponent(key)}=${JSON.stringify(value)}`);
      } else if (Array.isArray(value)) {
        value.forEach((v, i) => {
          arr.push(`${encodeURIComponent(key)}[${i}]=${encodeURIComponent(v)}`);
        });
      } else if ("object" === typeof value && value) {
        Object.keys(value).map((k1) => {
          const innerValue = value[k1];
          if ("object" === typeof innerValue) {
            Object.keys(innerValue).map((k2) => {
              arr.push(`${encodeURIComponent(key)}[${k1}][${k2}]=${encodeURIComponent(innerValue[k2])}`);
            });
          } else {
            arr.push(`${encodeURIComponent(key)}[${k1}]=${encodeURIComponent(value[k1])}`);
          }
        });
      } else {
        arr.push(`${decodeURIComponent(key)}=${decodeURIComponent(value)}`);
      }
    });
  };

  return (
    <ApiContext.Provider
      value={{
        token,
        setToken: updateToken,
        getToken,
        fetch: fetching,
        get,
        post,
        put,
        patch,
        delete: del,
        isLoading,
        violations,
      }}
    >
      {children}
    </ApiContext.Provider>
  );
}

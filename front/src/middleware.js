import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import ROUT<PERSON> from "@/enums/ROUTES";
import USER_ROLES from "@/enums/USER_ROLES";

export async function middleware(request) {
  const { pathname } = request.nextUrl;
  const protectedRoutes = ["/admin", "/superadmin"];
  const token = cookies().get("token")?.value;

  if (pathname === ROUTES.LOGIN && token) {
    return NextResponse.redirect(new URL("/", request.url));
  }
  if (protectedRoutes.some((route) => pathname.includes(route))) {
    if (!token) {
      return NextResponse.redirect(new URL("/login", request.url));
    }

    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/user`, {
      headers: {
        "X-Sinfin-Token": token,
      },
    });
    const user = await response.json();
    const role = user.role;

    if (pathname.includes("/admin") && !token) {
      return NextResponse.redirect(new URL("/", request.url));
    }

    if (pathname.includes("/superadmin") && role !== USER_ROLES.SUPERADMIN) {
      return NextResponse.redirect(new URL("/", request.url));
    }

    return NextResponse.next();
  }

  return NextResponse.next({
    headers: {
      "x-pathname": pathname,
    },
  });
}

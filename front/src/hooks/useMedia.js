import { useApi } from "@/context/ApiProvider";

export default function useMedia() {
	const { getToken } = useApi();

	const uploadImage = async (file) => {
		let req = new XMLHttpRequest();

		return new Promise((resolve, reject) => {
			const formData = new FormData();
			formData.append("file", file);
			req.open("POST", `${process.env.NEXT_PUBLIC_API_URL}/api/uploads`);
			req.setRequestHeader("X-Sinfin-Token", getToken());
			req.onreadystatechange = (e) => {
				if (req.readyState !== 4) {
					return;
				}

				if (req.status === 201) {
					resolve(JSON.parse(req.responseText));
				}
			};

			req.send(formData);
		});
	};

	const uploadPdf = (file, isPublic = false) => {
    const req = new XMLHttpRequest();

    return new Promise((resolve, reject) => {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("alt", file.name);
			formData.append("isPublic", isPublic)
      req.open("POST", `${process.env.NEXT_PUBLIC_API_URL}/api/uploads`);
      req.setRequestHeader("X-Sinfin-Token", getToken());
      req.onreadystatechange = (e) => {
        if (req.readyState !== 4) {
          return;
        }

        if (req.status === 201) {
          resolve(JSON.parse(req.responseText));
        }
      };

      req.send(formData);
    });
  };

  return { uploadImage, uploadPdf };
}

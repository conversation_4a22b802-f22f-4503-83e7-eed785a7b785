import { useEffect, useState } from "react";
import { v4 as uuidv4 } from "uuid";

export default function useCollection({ defaultItems = [], defaultValues = {}, onChange, requiredFields = [] }) {
  const [items, setItems] = useState(defaultItems);
  const [values, setValues] = useState(defaultValues);
  const [hasError, setHasError] = useState(false);

  const create = () => {
    if (!checkValues()) return;
    const id = uuidv4();
    setItems((items) => [
      ...items,
      {
        id,
        ...values,
      },
    ]);
    resetValues();
  };

  const remove = (id) => {
    setItems((items) => items.filter((item) => item.id !== id));
  };

  const update = (id) => {
    setItems((items) => items.map((item) => (item.id === id ? { ...item, ...values } : item)));
  };

  const resetValues = () => {
    setValues(defaultValues);
  };

  const checkValues = () => {
    setHasError(false);
    const isValid = requiredFields.every((field) => values[field]);
    if (!isValid) {
      setHasError(true);
      return false;
    }
    return true;
  };

  useEffect(() => {
    if (onChange) {
      onChange(items);
    }
  }, [items]);

  return {
    items,
    values,
    setValues,
    hasError,
    create,
    update,
    remove,
    resetValues,
    checkValues,
  };
}

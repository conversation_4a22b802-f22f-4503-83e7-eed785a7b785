"use client";

import { usePathname, useRouter, useSearchParams } from "next/navigation";

export default function useQueryParams() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const createQueryString = (params) => {
    const newParams = new URLSearchParams();

    for (const key in params) {
      newParams.set(key, params[key]);
    }

    return newParams.toString();
  };

  const setParams = (params) => {
    const newParams = new URLSearchParams(searchParams);

    for (const key in params) {
      newParams.set(key, params[key]);
    }

    router.push(`${pathname}?${newParams.toString()}`);
  };

  const remove = (name) => {
    const params = new URLSearchParams(searchParams);
    params.delete(name);

    router.push(`${pathname}?${params.toString()}`);
  };

  const clear = () => {
    router.push(pathname);
  };

  return {
    setParams,
    remove,
    searchParams,
    clear,
    createQueryString,
  };
}

import { useApi } from "@/context/ApiProvider";
import { useState } from "react";

export default function usePositions() {
	const { post } = useApi();
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState(null);

	const handleChangePosition = async (items) => {
		try {
			setIsLoading(true);
			await post("/content-orders", {
				orders: items.map((item, index) => ({
					uuid: item.uuid,
					position: item.index,
				})),
			});
		} catch (error) {
			setError(error);
			throw error;
		} finally {
			setIsLoading(false);
		}
	};

	return {
		handleChangePosition,
		isLoading,
		error,
	};
}

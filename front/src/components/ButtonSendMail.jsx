"use client";

import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import { Button } from "@mui/material";

export default function ButtonSendMail({ devis }) {
  const { post } = useApi();
  const { add } = useSnack();

  const submit = async (e) => {
    e.preventDefault();
    try {
      await post(`/estimates/${devis.uuid}/email`, {
        uuid: devis.uuid,
      });
      add("success", "Email envoyé avec succès");
    } catch (error) {
      add("error", "Erreur lors de l'envoi de l'email");
    }
  };

  return (
    <Button variant="text" style={{ textDecoration: "underline" }} onClick={submit}>
      Renvoyer l&apos;email
    </Button>
  );
}

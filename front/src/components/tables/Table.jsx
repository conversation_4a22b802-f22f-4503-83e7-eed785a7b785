"use client";

import DIRECTIONS from "@/enums/DIRECTIONS";
import FormatUtils from "@/utils/format.utils";
import AddIcon from "@mui/icons-material/Add";
import {
  Button,
  Table as MuiTable,
  Pagination,
  Paper,
  Stack,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TableSortLabel,
  Typography,
} from "@mui/material";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useState } from "react";
import DeleteModal from "../DeleteModal";
import QSearch from "../QSearch";
import RenderValue from "./RenderValue";

export default function Table({ data, page, title, searchParams, options, readOnly, hideActions = false, canUpdate = true, canDelete = true, onRefresh, onClickAdd }) {
  const router = useRouter();
  const pathname = usePathname();
  const [direction, setDirection] = useState(
    searchParams[Object.keys({ ...searchParams }).find((key) => key.startsWith("order["))] || DIRECTIONS.ASC
  );
  const [itemDelete, setItemDelete] = useState(null);

  const toggleSort = (field) => {
    let newDirection = direction === DIRECTIONS.ASC ? DIRECTIONS.DESC : DIRECTIONS.ASC;
    let params = { ...searchParams };
    Object.keys(params).forEach((key) => {
      if (key.startsWith("order[")) {
        delete params[key];
      }
    });
    router.push(
      FormatUtils.formatUrl({
        ...params,
        [`${field}`]: newDirection,
        page: 1,
      })
    );
    setDirection(newDirection);
  };

  return (
    <Stack component={Paper} p={5} gap={3}>
      <Typography variant="h4" fontWeight={600}>
        {title} ({FormatUtils.spaceOnNumbers(data.totalItems)})
      </Typography>
      <Stack direction="row" justifyContent="space-between" alignItems="flex-end">
        <QSearch />
        {readOnly && !onClickAdd ? null : (
          <Stack direction="row" gap={2}>
            <Button
              startIcon={<AddIcon />}
              component={onClickAdd ? null : Link}
              href={onClickAdd ? "" : `${pathname}/new${FormatUtils.formatUrl(searchParams)}`}
              onClick={onClickAdd}
            >
              Ajouter
            </Button>
          </Stack>
        )}
      </Stack>
      {data.member.length === 0 ? (
        <Typography variant="subtitle1">Aucun élément</Typography>
      ) : (
        <>
          <TableContainer>
            <MuiTable sx={{ minWidth: 650 }} aria-label="simple table">
              <TableHead>
                <TableRow>
                  {options.map((order, index) => (
                    <TableCell key={index}>
                      {order.variable ? (
                        <TableSortLabel active={!!searchParams?.[order.variable]} direction={direction} onClick={() => toggleSort(order.variable)}>
                          {order.label}
                        </TableSortLabel>
                      ) : (
                        order.label
                      )}
                    </TableCell>
                  ))}
                  {hideActions ? null : readOnly ? (
                    <TableCell padding="checkbox" />
                  ) : (
                    <>
                      {canUpdate && <TableCell padding="checkbox" />}
                      {canDelete && <TableCell padding="checkbox" />}
                    </>
                  )}
                </TableRow>
              </TableHead>
              <TableBody>
                {data.member.map((item) => (
                  <TableRow key={item.uuid}>
                    {options.map((option, index) => (
                      <TableCell key={index}>
                        <RenderValue item={item} option={option} />
                      </TableCell>
                    ))}
                    {hideActions ? null : readOnly ? (
                      <TableCell>
                        <Typography component={Link} variant="link" href={`${pathname}/${item.uuid}${FormatUtils.formatUrl(searchParams)}`}>
                          Voir
                        </Typography>
                      </TableCell>
                    ) : (
                      <>
                        {canUpdate && (
                          <TableCell>
                            <Typography component={Link} variant="link" href={`${pathname}/${item.uuid}${FormatUtils.formatUrl(searchParams)}`}>
                              Modifier
                            </Typography>
                          </TableCell>
                        )}
                        {canDelete && (
                          <TableCell>
                            <Typography onClick={() => setItemDelete(item)} variant="link">
                              Supprimer
                            </Typography>
                          </TableCell>
                        )}
                      </>
                    )}
                  </TableRow>
                ))}
              </TableBody>
            </MuiTable>
          </TableContainer>
          {Math.ceil(data.totalItems / 30) > 1 ? (
            <Stack alignItems="center" justifyContent="center" mt={3}>
              <Pagination
                showFirstButton
                showLastButton
                count={Math.ceil(data.totalItems / 30)}
                page={Number(page)}
                onChange={(e, page) =>
                  router.push(
                    FormatUtils.formatUrl({
                      ...searchParams,
                      page,
                    })
                  )
                }
              />
            </Stack>
          ) : null}
          <DeleteModal item={itemDelete} onClose={() => setItemDelete(null)} onRefresh={onRefresh} />
        </>
      )}
    </Stack>
  );
}

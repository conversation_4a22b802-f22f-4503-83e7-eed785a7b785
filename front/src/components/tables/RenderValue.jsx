"use client";

import FormatUtils from "@/utils/format.utils";
import TranslationUtils from "@/utils/translation.utils";
import Image from "next/image";
import { useEffect, useState } from "react";
import TruncatedText from "../TruncatedText";

export default function RenderValue({ item, option }) {
  const [value, setValue] = useState(null);

  const dateRegex = /(?:\d{4})-(?:\d{2})-(?:\d{2})T(?:\d{2}):(?:\d{2}):(?:\d{2}(?:\.\d*)?)(?:(?:-(?:\d{2}):(?:\d{2})|Z)?)/g;

  useEffect(() => {
    if (option?.emptyValue && !item[option.property]) {
      setValue(option.emptyValue);
    } else if (option?.action === "download") {
      setValue(
        <a
          style={{
            textDecoration: "underline",
          }}
          href={FormatUtils.binary(item[option.property])}
          target="_blank"
          rel="noreferrer"
        >
          {FormatUtils.binary(item[option.property])}
        </a>
      );
    } else if (Array.isArray(option.property)) {
      let value = item;
      option.property.forEach((e) => {
        value = value?.[e];
      });
      setValue(value);
    } else if (typeof item[option.property] === "number" && option.label === "Poids") {
      setValue(`${(item[option.property] / (1024 * 1024)).toFixed(2)} MB`);
    } else if (typeof item[option.property] === "string" && item[option.property].includes("<p>")) {
      setValue(<TruncatedText string={item[option.property]} />);
    } else if (typeof item[option.property] === "string" && dateRegex.test(item[option.property])) {
      const date = FormatUtils.formatDate(item[option.property]);
      setValue(date);
    } else if (option.property === "publicationDate") {
      const date = FormatUtils.formatDate(item[option.property]);
      setValue(date);
    } else if (typeof item[option.property] === "object") {
      setValue(item[option.property]?.name);
    } else if (typeof item[option.property] === "boolean") {
      setValue(item[option.property] ? "Oui" : "Non");
    } else if (item[option.property].includes("/uploads/")) {
      const img = FormatUtils.image(item[option.property]);
      setValue(<Image src={img} alt={item.name} width={100} height={50} style={{ objectFit: "contain" }} />);
    } else if (TranslationUtils.get(item[option.property])) {
      setValue(TranslationUtils.get(item[option.property]));
    } else if (option.property === ("createdAt" || "updatedAt")) {
      const date = FormatUtils.formatDate(item[option.property]) + " à " + FormatUtils.formatTime(item[option.property]);
      setValue(date);
    } else if (option.render) {
      setValue(option.render(item));
    } else {
      setValue(item[option.property]);
    }
  }, [item, option]);

  return value;
}

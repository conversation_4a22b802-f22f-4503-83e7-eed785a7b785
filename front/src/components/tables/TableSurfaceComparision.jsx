import TranslationUtils from "@/utils/translation.utils";
import CancelIcon from "@mui/icons-material/Cancel";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import DeleteRoundedIcon from "@mui/icons-material/DeleteRounded";
import { IconButton, Stack, Typography } from "@mui/material";

const Row = ({ row, remove, index }) => {
  return (
    <Stack
      display="grid"
      gridTemplateColumns={remove ? "1fr 1fr 1fr 1fr" : "1fr 1fr 1fr"}
      py={{ sx: 1, md: remove ? 1 : 2 }}
      px={{ xs: 2, md: remove ? 2 : 5 }}
      bgcolor={index % 2 !== 0 ? "white" : "grey.extraLight"}
      alignItems="center"
      justifyContent="center"
    >
      <Typography variant="bold">{row.label}</Typography>
      <Stack alignItems="center">{row.surface_habitable ? <CheckCircleIcon color="success" /> : <CancelIcon color="error" />}</Stack>
      <Stack alignItems="center">{row.surface_privative ? <CheckCircleIcon color="success" /> : <CancelIcon color="error" />}</Stack>
      {remove ? (
        <IconButton onClick={() => remove(row.id)}>
          <DeleteRoundedIcon />
        </IconButton>
      ) : null}
    </Stack>
  );
};

export default function TableSurfaceComparision({ rows, remove }) {
  return (
    <Stack>
      <Stack
        display="grid"
        gridTemplateColumns={remove ? "1fr 1fr 1fr 1fr" : "1fr 1fr 1fr"}
        py={{ sx: 1, md: remove ? 1 : 2 }}
        px={{ xs: 2, md: remove ? 2 : 5 }}
      >
        <Typography variant="bold">{TranslationUtils.get("blocks.blockSurfaceComparision.header.surface")}</Typography>
        <Typography variant="bold" textAlign="center">
          {TranslationUtils.get("blocks.blockSurfaceComparision.header.surfaceHabitable")}
        </Typography>
        <Typography variant="bold" textAlign="center">
          {TranslationUtils.get("blocks.blockSurfaceComparision.header.surfacePrivative")}
        </Typography>
      </Stack>
      {rows.map((row, index) => (
        <Row key={row.id} row={row} remove={remove} index={index} />
      ))}
    </Stack>
  );
}

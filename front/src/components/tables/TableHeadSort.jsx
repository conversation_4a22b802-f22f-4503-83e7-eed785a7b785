"use client";

import DIRECTIONS from "@/enums/DIRECTIONS";
import FormatUtils from "@/utils/format.utils";
import { TableCell, TableSortLabel } from "@mui/material";
import { useRouter } from "next/navigation";
import { useState } from "react";

export default function TableHeadSort({ searchParams, code, children }) {
  const router = useRouter();

  const [direction, setDirection] = useState(
    searchParams[Object.keys({ ...searchParams }).find((key) => key.startsWith("order["))] || DIRECTIONS.ASC
  );

  const toggleSort = () => {
    let newDirection = direction === DIRECTIONS.ASC ? DIRECTIONS.DESC : DIRECTIONS.ASC;
    let params = { ...searchParams };
    Object.keys(params).forEach((key) => {
      if (key.startsWith("order[")) {
        delete params[key];
      }
    });
    router.push(
      FormatUtils.formatUrl({
        ...params,
        [`order[${code}]`]: newDirection,
        page: 1,
      })
    );
    setDirection(newDirection);
  };

  return (
    <TableCell color="white">
      <TableSortLabel active={!!searchParams?.[`order[${code}]`]} direction={direction} onClick={() => toggleSort(code)}>
        {children}
      </TableSortLabel>
    </TableCell>
  );
}

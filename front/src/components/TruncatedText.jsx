import { Typography } from "@mui/material";
import React from "react";

export default function TruncatedText({ string }) {
  const firstParagraph = string?.match(/<p>(.*?)<\/p>/)?.[0] || string;
  return (
    <Typography
      component="div"
      sx={{
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
        maxWidth: "250px",
        "& p": {
          display: "inline",
        },
      }}
      dangerouslySetInnerHTML={{ __html: firstParagraph }}
    />
  );
}

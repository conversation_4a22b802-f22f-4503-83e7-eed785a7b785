import TranslationUtils from "@/utils/translation.utils";
import { Button, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle } from "@mui/material";
import { useNavigationGuard } from "next-navigation-guard";

export default function LeavingDialog({ isDirty, save }) {
  const navGuard = useNavigationGuard({ enabled: isDirty });
  return (
    <Dialog open={navGuard.active} aria-labelledby="alert-dialog-title" aria-describedby="alert-dialog-description">
      <DialogTitle id="alert-dialog-title">{TranslationUtils.get("contents.lose_title")}</DialogTitle>
      <DialogContent>
        <DialogContentText id="alert-dialog-description">{TranslationUtils.get("contents.lose_description")}</DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button variant="outlined" onClick={navGuard.reject}>
          {TranslationUtils.get("global.cancel")}
        </Button>
        <Button variant="outlined" color="error" onClick={navGuard.accept} autoFocus>
          {TranslationUtils.get("contents.continue_without_saving")}
        </Button>
        <Button
          onClick={() => {
            save();
            setTimeout(() => {
              navGuard.accept();
            }, 100);
          }}
          autoFocus
        >
          {TranslationUtils.get("contents.continue_and_save")}
        </Button>
      </DialogActions>
    </Dialog>
  );
}

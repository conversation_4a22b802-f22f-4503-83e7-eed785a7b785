"use client";

import { useState } from "react";

import Input from "./inputs/Input";
import theme from "@/lib/theme";
import { <PERSON>ton, Stack, Typography } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";

export default function InputCodeAvantage({ price, advantage, setAdvantage }) {
  const [code, setCode] = useState("");
  const [percent, setPercent] = useState(0);
  const [errorCode, setErrorCode] = useState(false);
  const [errorPercent, setErrorPercent] = useState(false);

  const add = (e) => {
    if (code === "" || percent === 0) {
      if (code === "") {
        setErrorCode(true);
      } else {
        setErrorCode(false);
      }
      if (percent === 0) {
        setErrorPercent(true);
      } else {
        setErrorPercent(false);
      }
    } else {
      setAdvantage((prev) => [
        ...prev,
        {
          code,
          percentage: +percent,
        },
      ]);
      setCode("");
      setPercent(0);
    }
  };

  const remove = (index) => {
    const newArray = [...advantage].filter((_, i) => i !== index);
    setAdvantage(newArray);
  };

  return (
    <Stack gap={1} width={"fit-content"}>
      <Typography variant="bold">Code avantage</Typography>
      <Stack direction="row" gap={2} alignItems="center" mb={1}>
        <Input
          slotProps={{
            input: {
              style: { color: theme.palette.secondary.main, borderRadius: 0 },
            },
          }}
          error={errorCode}
          placeholder="Code avantage"
          name="advantageCode"
          onChange={(e) => setCode(e.target.value)}
          value={code}
        />
        <Input
          slotProps={{
            htmlInput: {
              className: `number-without-arrows`,
              min: 0,
              max: 100,
            },
            input: {
              style: { color: theme.palette.secondary.main, borderRadius: 0 },
              endAdornment: (
                <Typography variant="bold" color="black">
                  %
                </Typography>
              ),
            },
          }}
          error={errorPercent}
          onChange={(e) => setPercent(e.target.value)}
          name="advantagePercent"
          type="number"
          value={percent}
        />
        <Button variant="text" onClick={add}>
          <AddIcon />
        </Button>
      </Stack>
      {advantage.map((e, index) => {
        return (
          <Stack gap={1} key={index} display="grid" gridTemplateColumns="1fr max-content max-content" alignItems="center">
            <Typography variant="bold" color="black">
              {e.code}
            </Typography>
            <Typography color="black">{e.percentage} %</Typography>
            <Button variant="text" onClick={() => remove(index)}>
              <RemoveIcon />
            </Button>
          </Stack>
        );
      })}
    </Stack>
  );
}

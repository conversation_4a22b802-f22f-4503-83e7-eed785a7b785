import { Button, <PERSON>alog, Stack, Typography } from "@mui/material";
import TranslationUtils from "@/utils/translation.utils";
import { useState } from "react";

export default function ButtonCopy({ onClick, disabled }) {
  const [open, setOpen] = useState(false);

  const click = () => {
    onClick();
    setOpen(false);
  };

  return (
    <>
      <Button variant="outlined" color="secondary" onClick={() => setOpen(true)} disabled={disabled}>
        {TranslationUtils.get("global.copy")}
      </Button>
      <Dialog open={open} onClose={() => setOpen(false)}>
        <Stack p={3}>
          <Typography mb={2} variant="h4">
            Dupliquer une page
          </Typography>
          <Typography variant="body1" mb={2}>
            {TranslationUtils.get("global.copyConfirmation")}
          </Typography>
          <Stack direction="row" gap={2} justifyContent="flex-end">
            <Button color="secondary" onClick={() => setOpen(false)}>
              {TranslationUtils.get("global.cancel")}
            </Button>
            <Button onClick={click}>{TranslationUtils.get("global.copy")}</Button>
          </Stack>
        </Stack>
      </Dialog>
    </>
  );
}

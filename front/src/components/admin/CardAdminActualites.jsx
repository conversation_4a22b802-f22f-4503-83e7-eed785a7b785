"use client";
import FormatUtils from "@/utils/format.utils";
import theme from "@/lib/theme";
import { Paper, Stack, styled, Typography } from "@mui/material";
import placeholder from "@public/assets/images/placeholder_news.png";
import { useRouter } from "next/navigation";

const MediaContainer = styled(Stack, {
  shouldForwardProp: (prop) => prop !== "url",
})(({ url }) => ({
  minHeight: 150,
  width: "100%",
  background: `url(${url}) center/contain no-repeat`,
}));

export default function CardAdminActualites({ news }) {
  const router = useRouter();
  return (
    <Stack
      direction={"row"}
      component={Paper}
      border={`solid 1px ${theme.palette.divider}`}
      width="100%"
      overflow="hidden"
      maxHeight={250}
      onClick={() => router.push(`/actualites-cabinet/${news.url}`)}
      sx={{
        cursor: "pointer",
      }}
    >
      <MediaContainer url={news.upload ? FormatUtils.binary(news.upload) : placeholder.src} />
      <Stack p={2} gap={1} width="100%" overflow="hidden">
        <Typography variant="h6" color="primary">
          {news.title}
        </Typography>
        <Typography
          component="div"
          width="100%"
          variant="body1"
          overflow="hidden"
          dangerouslySetInnerHTML={{
            __html: news.content,
          }}
        />
        <Typography variant="h6">{news.date}</Typography>
      </Stack>
    </Stack>
  );
}

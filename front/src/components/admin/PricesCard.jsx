"use client";

import { <PERSON><PERSON>, <PERSON>, CardContent, Paper, Stack, Typography } from "@mui/material";
import { palette } from "@/lib/theme";
import CircleOutlinedIcon from "@mui/icons-material/CircleOutlined";
import RadioButtonCheckedIcon from "@mui/icons-material/RadioButtonChecked";
import { Link } from "next/link";
import ROUTES from "@/enums/ROUTES";
import { PRICE_TYPE } from "@/enums/PRICE_TYPE";
import { useApi } from "@/context/ApiProvider";
import TranslationUtils from "@/utils/translation.utils";
import { useSnack } from "@/context/SnackProvider";

const GridCard = ({ type, active, onClick }) => (
  <Card
    sx={{
      width: "100‰",
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      bgcolor: active ? palette.red : "white",
      color: active ? "white" : palette.red,
      border: "1px solid #e0e0e0",
      cursor: "pointer",
      my: 2,
    }}
    onClick={onClick}
  >
    <CardContent
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <Stack justifyContent="center" alignItems="center" mb={1}>
        {active ? <RadioButtonCheckedIcon /> : <CircleOutlinedIcon />}
      </Stack>
      <Typography align="center">Grille {type}</Typography>
    </CardContent>
  </Card>
);

export default function PricesCard({ agency, onSuccess }) {
  const { post } = useApi();
  const { add } = useSnack();

  const changeGridType = async (agency, type) => {
    await post("/activate-price-grids", { agency: agency["@id"], type });
    add("success", `La grille ${type} a été activée sur ${agency.name}`);
    onSuccess();
  };

  return (
    <Stack component={Paper} p={2} justifyContent="space-between" height="100%">
      <Stack m="auto" py={2}>
        <Typography variant="h5" gutterBottom fontFamily="var(--font-montserrat-bold), sans-serif">
          {agency.name}
        </Typography>
      </Stack>
      <Stack display="grid" gridTemplateColumns={{ xs: "1fr", sm: "1fr 1fr 1fr" }} gap={2}>
        {Object.values(PRICE_TYPE).map((t) => {
          return (
            <Stack key={t}>
              <GridCard type={t} active={agency.priceGridType === t} onClick={() => changeGridType(agency, agency.priceGridType === t ? null : t)} />
              <Stack direction="row" justifyContent="center">
                <Button component={Link} href={`${ROUTES.PRICES}/${agency.uuid}/${t}`} color="secondary">
                  {TranslationUtils.get("global.modify")}
                </Button>
              </Stack>
            </Stack>
          );
        })}
      </Stack>
    </Stack>
  );
}

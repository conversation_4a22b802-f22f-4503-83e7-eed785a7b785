import { Stack, Typography } from "@mui/material";
import React from "react";

const days = {
  monday: "Lundi",
  tuesday: "Mardi",
  wednesday: "Mercredi",
  thursday: "<PERSON><PERSON>",
  friday: "Vendredi",
  saturday: "<PERSON>di",
  sunday: "Diman<PERSON>",
};

export default function ScheduleLine({ label, schedule }) {
  const day = days[label];
  const empty = !schedule?.morning?.openAt && !schedule?.morning?.closeAt && !schedule?.afternoon?.openAt && !schedule?.afternoon?.closeAt;

  if (!day) {
    return null;
  }

  return (
    <Stack direction="row" gap={2} display="grid" gridTemplateColumns="1fr 2fr">
      <Typography>{day}</Typography>
      <Typography>
        {empty ? (
          "Fermé"
        ) : (
          <>
            {schedule?.morning?.openAt} {schedule?.morning?.closeAt && schedule?.morning?.openAt && "à"} {schedule?.morning?.closeAt}{" "}
            {schedule?.afternoon?.openAt && schedule?.afternoon?.closeAt && "-"} {schedule?.afternoon?.openAt}{" "}
            {schedule?.afternoon?.openAt && schedule?.afternoon?.closeAt && "à"} {schedule?.afternoon?.closeAt}
          </>
        )}
      </Typography>
    </Stack>
  );
}

"use client";

import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import { Button, CircularProgress } from "@mui/material";
import React, { useState } from "react";

export default function ExportButton({ data }) {
  const { post } = useApi();
  const { add } = useSnack();

  const [isExportingTable, setIsExportingTable] = useState(false)
  const [isExportingAll, setIsExportingAll] = useState(false)

  const exportTable = async () => {
    try {
      setIsExportingTable(true)
      const response = await post("/excel-exports", {
        type: "Estimate",
        uuids: data,
      });
      window.open(`${process.env.NEXT_PUBLIC_API_URL}${response.url}`, "_blank");
    } catch (error) {
      add("error", "Une erreur est survenue lors de l'exportation du tableau");
      console.log(error);
    } finally {
      setIsExportingTable(false)
    }
  };

  const exportAll = async () => {
    try {
      setIsExportingAll(true)
      const response = await post("/excel-exports", {
        type: "all_estimate",
      });
      window.open(`${process.env.NEXT_PUBLIC_API_URL}${response.url}`, "_blank");
    } catch (error) {
      add("error", "Une erreur est survenue lors de l'exportation des devis");
      console.log(error);
    } finally {
      setIsExportingAll(false)
    }
  };
  return (<>
    <Button variant="contained" color="secondary" sx={{ height: "fit-content" }} onClick={exportTable} disabled={isExportingTable}>
      {isExportingTable ? <>Export en cours <CircularProgress size={12} sx={{marginLeft: 1}}></CircularProgress></>: <>Exporter le tableau</>}
    </Button>
    <Button variant="contained" color="secondary" sx={{ height: "fit-content" }} onClick={exportAll} disabled={isExportingAll}>
      {isExportingAll ? <>
        Export en cours <CircularProgress size={12} sx={{marginLeft: 1}} ></CircularProgress>
      </> : <>Exporter tous les devis</>}
    </Button>
  </>);
}

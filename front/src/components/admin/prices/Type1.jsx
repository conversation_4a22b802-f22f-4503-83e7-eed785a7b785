"use client";

import BackButton from "@/components/buttons/BackButton";
import Input from "@/components/inputs/Input";
import theme from "@/lib/theme";
import TranslationUtils from "@/utils/translation.utils";
import { Button, IconButton, Paper, Stack, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography } from "@mui/material";
import { Fragment, useState } from "react";
import InputToggleButton from "@/components/inputs/InputToggleButton";
import ButtonSave from "@/components/forms/agencies/ButtonSave";
import { DEFAULT_PRICE_ACTIVITY_TYPE, PRICE_ACTIVITY_TYPE } from "@/enums/PRICE_ACTIVITY_TYPE";
import SelectPriceActivities from "@/components/inputs/SelectPriceActivities";
import { v4 as uuidv4 } from "uuid";
import DeleteIcon from "@mui/icons-material/Delete";
import { useApi } from "@/context/ApiProvider";
import { FLAT_TYPE, HOUSE_TYPE, PRICE_HOME_TYPE, TYPE } from "@/enums/PRICE_HOME_TYPE";
import { useSnack } from "@/context/SnackProvider";
import InputCodeAvantage from "@/components/InputCodeAvantage";
import EditIcon from "@mui/icons-material/Edit";

export default function Type1({ params, agency, price, onSuccess }) {
  const { put } = useApi();
  const { add } = useSnack();

  const defaultPricings = Object.keys(DEFAULT_PRICE_ACTIVITY_TYPE).map((key) => ({
    pack: false,
    type: key,
    types: null,
    packName: null,
    f1: 0,
    f2: 0,
    f3: 0,
    f4: 0,
    f5: 0,
    f6: 0,
    f7: 0,
    t2: 0,
    t3: 0,
    t4: 0,
    t5: 0,
    t6: 0,
    t7: 0,
  }));

  const [type, setType] = useState(PRICE_HOME_TYPE.FLAT);
  const [discountType, setDiscountType] = useState(price.gridData.discountType);
  const [pricings, setPricings] = useState(price.gridData.pricings.length ? price.gridData.pricings : defaultPricings);
  const [advantage, setAdvantage] = useState(price.advantages.advantages);
  const [isEditPackage, setIsEditPackage] = useState(null);

  const discounts = Object.keys(price.gridData)
    .map((key) => {
      if (key.startsWith("discountFor")) {
        return {
          type: key.replace("discountFor", ""),
          discount: price.gridData[key],
        };
      }
    })
    .filter((discount) => discount);

  const mapPricings = (e) => {
    let obj = [];
    Object.values(PRICE_ACTIVITY_TYPE)
      .filter((key) => key !== PRICE_ACTIVITY_TYPE.PACK)
      .forEach((key) => {
        const flat = {};
        Object.values(FLAT_TYPE).forEach((flatKey) => {
          if (e.target[`${key}-${flatKey}`]) {
            flat[flatKey] = +e.target[`${key}-${flatKey}`].value;
          }
        });
        const house = {};
        Object.values(HOUSE_TYPE).forEach((houseKey) => {
          if (e.target[`${key}-${houseKey}`]) {
            house[houseKey] = +e.target[`${key}-${houseKey}`].value;
          }
        });
        if (Object.keys(flat).length > 0 || Object.keys(house).length > 0) {
          const o = {
            ...pricings.find((pricing) => pricing.type === key),
            ...flat,
            ...house,
            type: key,
          };
          if (o?.["@id"]) {
            delete o["@id"];
          }
          obj.push(o);
        }
      });
    pricings
      .filter((pricing) => pricing.pack)
      .forEach((pricing) => {
        const flat = {};
        Object.values(FLAT_TYPE).forEach((flatKey) => {
          if (e.target[`${pricing.packName}-${flatKey}`]) {
            flat[flatKey] = +e.target[`${pricing.packName}-${flatKey}`].value;
          }
        });
        const house = {};
        Object.values(HOUSE_TYPE).forEach((houseKey) => {
          if (e.target[`${pricing.packName}-${houseKey}`]) {
            house[houseKey] = +e.target[`${pricing.packName}-${houseKey}`].value;
          }
        });
        const o = {
          ...pricing,
          ...flat,
          ...house,
        };
        if (o?.["@id"]) {
          delete o["@id"];
        }
        obj.push(o);
      });
    return obj;
  };

  const submit = async (e) => {
    e.preventDefault();
    const prices = mapPricings(e);
    const params = {
      ...price,
      area: price.area ? price.area["@id"] : null,
      advantages: {
        ...price.advantages,
        advantages: advantage,
      },
      gridData: {
        ...price.gridData,
        amiantePrice: +e.target.amiantePrice.value,
        appointmentDiscountPercent: +e.target.appointmentDiscountPercent.value,
        discountType: discountType,
        discountFor7AndMore: +e.target.discountFor7AndMore.value,
        discountFor6: +e.target.discountFor6.value,
        discountFor5: +e.target.discountFor5.value,
        discountFor4: +e.target.discountFor4.value,
        discountFor3: +e.target.discountFor3.value,
        discountFor2: +e.target.discountFor2.value,
        cgv: e.target.cgv.value,
        pricings: prices,
      },
    };
    delete params.gridData?.["@id"];
    try {
      await put(`/price-grids/${price.uuid}`, params);
      add("success", "La grille de prix a bien été modifiée !");
      onSuccess();
    } catch (error) {
      add("error", error.description);
    }
  };

  const addPackage = () => {
    const newPricing = {
      new: true,
      uuid: uuidv4(),
      pack: true,
      packName: "Package " + (pricings.filter((pricing) => pricing.pack).length + 1),
      type: PRICE_ACTIVITY_TYPE.PACK,
    };
    setPricings([...pricings, newPricing]);
  };

  const removePackage = (uuid) => {
    const newPricings = pricings.filter((pricing) => pricing?.uuid !== uuid && pricing?.["@id"] !== uuid);
    setPricings(newPricings);
  };

  const handlePackageChange = (uuid, value, key) => {
    const newPricings = pricings.map((pricing) => (pricing.uuid === uuid || pricing["@id"] === uuid ? { ...pricing, [key]: value } : pricing));
    setPricings(newPricings);
  };

  return (
    <Stack gap={2} component={"form"} onSubmit={submit}>
      <BackButton>Retour</BackButton>
      <Stack gap={1}>
        <Typography variant="h2">{agency.name}</Typography>
        <Typography variant="body1">Grille {params.type}</Typography>
        <Typography variant="body1">{price?.area?.name || price.name}</Typography>
      </Stack>
      <Stack gap={5}>
        <Stack>
          <Stack direction="row" gap={1} justifyContent="flex-end" mb={2}>
            {Object.keys(PRICE_HOME_TYPE).map((key) => (
              <Button
                key={key}
                color="secondary"
                variant={type === PRICE_HOME_TYPE[key] ? "contained" : "outlined"}
                onClick={() => setType(PRICE_HOME_TYPE[key])}
              >
                {TranslationUtils.get(`prices.${PRICE_HOME_TYPE[key]}`)}
              </Button>
            ))}
          </Stack>
          <TableContainer
            sx={{
              overflowX: "visible",
            }}
          >
            <Table sx={{ minWidth: 650 }} aria-label="simple table">
              <TableHead sx={{ backgroundColor: "transparent" }}>
                <TableRow sx={{ borderLeft: "none !important", borderRight: "none !important" }}>
                  <TableCell width={"35%"} sx={{ border: "none" }} />
                  {Object.keys(TYPE[type]).map((key, index) => (
                    <TableCell
                      key={key}
                      sx={{
                        backgroundColor: theme.palette.secondary.main,
                        borderTopLeftRadius: index === 0 ? 10 : 0,
                        borderTopRightRadius: index === Object.keys(TYPE[type]).length - 1 ? 10 : 0,
                        fontWeight: "bold",
                      }}
                      align="center"
                    >
                      <Typography variant="bold">{key}</Typography>
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {pricings.map((pricing, index) => (
                  <TableRow
                    key={index}
                    sx={{
                      backgroundColor: index % 2 === 0 ? theme.palette.grey.extraLight : "transparent",
                      borderLeft: "none",
                      position: "relative",
                    }}
                  >
                    <TableCell sx={{ border: "none" }}>
                      {pricing.new ? (
                        <>
                          <SelectPriceActivities
                            onChange={(v) =>
                              handlePackageChange(
                                pricing.uuid,
                                v.map((v) => v.value),
                                "types"
                              )
                            }
                          />
                          <IconButton
                            onClick={() => removePackage(pricing.uuid || pricing["@id"])}
                            sx={{
                              position: "absolute",
                              right: "-30px",
                              top: "50%",
                              transform: "translateY(-50%)",
                            }}
                          >
                            <DeleteIcon color="secondary" />
                          </IconButton>
                        </>
                      ) : pricing.pack ? (
                        <>
                          {isEditPackage === pricing.uuid || isEditPackage === pricing["@id"] ? (
                            <SelectPriceActivities
                              key={isEditPackage}
                              defaultValue={pricing.types.map((activity) => ({
                                value: activity,
                                label: TranslationUtils.get(`prices.activity_type.${activity}`),
                              }))}
                              onChange={(v) =>
                                handlePackageChange(
                                  pricing.uuid || pricing["@id"],
                                  v.map((v) => v.value),
                                  "types"
                                )
                              }
                            />
                          ) : (
                            <Stack>
                              <Typography variant="bold">{pricing.packName}</Typography>
                              <Typography>{pricing.types.map((type) => TranslationUtils.get(`prices.activity_type.${type}`)).join(", ")}</Typography>
                            </Stack>
                          )}
                          <IconButton
                            onClick={() => setIsEditPackage(pricing.uuid || pricing["@id"])}
                            sx={{
                              position: "absolute",
                              right: "-30px",
                              top: "50%",
                              transform: "translateY(-50%)",
                            }}
                          >
                            <EditIcon color="secondary" />
                          </IconButton>
                          <IconButton
                            onClick={() => removePackage(pricing.uuid || pricing["@id"])}
                            sx={{
                              position: "absolute",
                              right: "-60px",
                              top: "50%",
                              transform: "translateY(-50%)",
                            }}
                          >
                            <DeleteIcon color="secondary" />
                          </IconButton>
                        </>
                      ) : (
                        <Typography variant="bold">{TranslationUtils.get(`prices.activity_type.${pricing.type}`)}</Typography>
                      )}
                    </TableCell>
                    {Object.values(FLAT_TYPE).map((key) => (
                      <TableCell
                        key={key}
                        align="center"
                        sx={{
                          display: type === PRICE_HOME_TYPE.FLAT ? "table-cell" : "none",
                        }}
                      >
                        <Input
                          name={`${pricing.pack ? pricing.packName : pricing.type}-${key}`}
                          slotProps={{
                            htmlInput: {
                              className: "number-without-arrows",
                            },
                            input: {
                              style: {
                                color: theme.palette.secondary.main,
                                fontWeight: "bold",
                                borderRadius: 0,
                              },
                            },
                          }}
                          type="number"
                          defaultValue={pricing[key] || 0}
                        />
                      </TableCell>
                    ))}
                    {Object.values(HOUSE_TYPE).map((key) => (
                      <TableCell
                        key={key}
                        align="center"
                        sx={{
                          display: type === PRICE_HOME_TYPE.HOUSE ? "table-cell" : "none",
                        }}
                      >
                        <Input
                          name={`${pricing.pack ? pricing.packName : pricing.type}-${key}`}
                          slotProps={{
                            htmlInput: {
                              className: "number-without-arrows",
                            },
                            input: {
                              style: {
                                color: theme.palette.secondary.main,
                                fontWeight: "bold",
                                borderRadius: 0,
                              },
                            },
                          }}
                          type="number"
                          defaultValue={pricing[key] || 0}
                        />
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          <Stack
            bgcolor="#fff"
            p={1}
            justifyContent="center"
            sx={{
              borderBottomRightRadius: 10,
              borderBottomLeftRadius: 10,
            }}
          >
            <Button variant="text" color="black" onClick={addPackage}>
              <Typography variant="bold">+ Ajouter un package</Typography>
            </Button>
          </Stack>
        </Stack>
        <Stack component={Paper} p={4} gap={5}>
          <Typography variant="h2">Remises pour des prestations groupées</Typography>
          <Stack display="grid" gridTemplateColumns="max-content 1fr" gap={5}>
            <Stack gap={3}>
              <InputToggleButton onChange={(v) => setDiscountType(v)} value={discountType} />
              <Stack gap={1} display="grid" gridTemplateColumns="max-content 1fr">
                {discounts.map((discount) => (
                  <Fragment key={discount.type}>
                    <Typography variant="bold">{discount.type === "7AndMore" ? "7+" : discount.type} prestations</Typography>
                    <Input
                      slotProps={{
                        htmlInput: {
                          className: "number-without-arrows",
                          min: 0,
                          max: discountType === "percent" ? 100 : 100000,
                          step: 0.01,
                        },
                        input: {
                          style: { color: theme.palette.secondary.main, borderRadius: 0 },
                          endAdornment:
                            discountType === "percent" ? (
                              <Typography variant="bold" color="black">
                                %
                              </Typography>
                            ) : null,
                        },
                      }}
                      name={`discountFor${discount.type}`}
                      type="number"
                      defaultValue={discount.discount}
                    />
                  </Fragment>
                ))}
              </Stack>
            </Stack>
            <Stack flexGrow={1} gap={3}>
              <Typography variant="body1">
                Pour la détermination du nombre de prestations, l’ERP n’est pas pris en compte. Pour le calcul de la remise, toutes les prestations
                sont prises en compte.
              </Typography>
              <Stack display="grid" gridTemplateColumns="max-content 1fr" columnGap={3} rowGap={1} alignItems="center">
                <Typography variant="bold">Frais prélèvement amiante</Typography>
                <Input
                  slotProps={{
                    htmlInput: {
                      className: "number-without-arrows",
                      min: 0,
                      max: 100000,
                    },
                    input: {
                      style: { color: theme.palette.secondary.main, borderRadius: 0 },
                    },
                  }}
                  name="amiantePrice"
                  type="number"
                  defaultValue={price.gridData.amiantePrice}
                />
                <Typography variant="bold">Réduction RDV</Typography>
                <Input
                  slotProps={{
                    htmlInput: {
                      className: "number-without-arrows",
                      min: 0,
                      max: 100,
                    },
                    input: {
                      style: { color: theme.palette.secondary.main, borderRadius: 0 },
                      endAdornment: (
                        <Typography variant="bold" color="black">
                          %
                        </Typography>
                      ),
                    },
                  }}
                  name="appointmentDiscountPercent"
                  type="number"
                  defaultValue={price.gridData.appointmentDiscountPercent}
                />
                <Stack
                  style={{
                    gridColumn: "1 / -1",
                  }}
                >
                  <InputCodeAvantage price={price} advantage={advantage} setAdvantage={setAdvantage} />
                </Stack>
              </Stack>
            </Stack>
          </Stack>
        </Stack>
        <Stack gap={1}>
          <Typography variant="bold">Conditions générales de vente</Typography>
          <Input
            placeholder="Devis, personnalisation de vos conditions générales de vente (phrase ajoutée dans le bas du devis)"
            multiline
            rows={7}
            fullWidth
            name="cgv"
            defaultValue={price.gridData.cgv}
          />
        </Stack>
        <ButtonSave label="Enregistrer" />
      </Stack>
    </Stack>
  );
}

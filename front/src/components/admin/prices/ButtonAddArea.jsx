import Loader from "@/components/Loader";
import { useApi } from "@/context/ApiProvider";
import { Button, Dialog, IconButton, Stack, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import CloseIcon from "@mui/icons-material/Close";
import Input from "@/components/inputs/Input";
import { DEFAULT_PRICE_DATA } from "@/enums/PRICE_TYPE";

export default function ButtonAddArea({ agency, type, prices, onSuccess }) {
  const { post, get } = useApi();
  const [open, setOpen] = useState(false);
  const [areas, setAreas] = useState(null);
  const [search, setSearch] = useState("");

  const handleAddArea = async (areaId) => {
    const price = DEFAULT_PRICE_DATA[type](agency['@id'], areaId);
    await post(`/price-grids`, price);
    setOpen(false);
    onSuccess();
  };

  const getData = async () => {
    const areas = await get(`/location-areas`, { agency: agency.uuid, pagination: false, "order[name]": "asc" });
    setAreas(areas.member);
  };

  useEffect(() => {
    if (open) {
      getData();
    }
  }, [open]);

  return (
    <>
      <Stack border="1px solid #e0e0e0" p={2} borderRadius={2} alignItems="center" justifyContent="center">
        <Button variant="text" color="text" onClick={() => setOpen(true)}>
          <Typography variant="bold">+ Ajouter un canton</Typography>
        </Button>
      </Stack>
      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        fullWidth
        maxWidth="lg"
        sx={{
          ".MuiPaper-root": {
            height: "80vh",
          },
        }}
      >
        <Stack p={2} alignItems="center" justifyContent="space-between" direction="row">
          <Stack />
          <Typography variant="h2">Ajouter un canton</Typography>
          <IconButton onClick={() => setOpen(false)}>
            <CloseIcon />
          </IconButton>
        </Stack>
        <Stack px={4} py={2} gap={1} direction="row">
          <Input placeholder="Rechercher" fullWidth onChange={(e) => setSearch(e.target.value)} />
        </Stack>
        {!areas ? (
          <Loader />
        ) : (
          <Stack p={4} pt={0} gap={1}>
            {areas.filter((area) => area.name.toLowerCase().includes(search.toLowerCase())).length === 0 ? (
              <Typography variant="bold">Aucun canton trouvé</Typography>
            ) : null}
            {areas
              .filter((area) => area.name.toLowerCase().includes(search.toLowerCase()))
              .map((area) => (
                <Stack
                  key={area.uuid}
                  border="1px solid #e0e0e0"
                  p={1}
                  borderRadius={2}
                  direction="row"
                  justifyContent="space-between"
                  alignItems="center"
                >
                  <Typography variant="bold">{area.name}</Typography>
                  <Button
                    color="secondary"
                    size="small"
                    disabled={prices.some((price) => price.area?.uuid === area.uuid)}
                    onClick={() => handleAddArea(area["@id"])}
                  >
                    Ajouter
                  </Button>
                </Stack>
              ))}
          </Stack>
        )}
      </Dialog>
    </>
  );
}

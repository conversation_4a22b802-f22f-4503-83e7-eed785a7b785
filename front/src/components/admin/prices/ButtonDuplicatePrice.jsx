"use client";

import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import { <PERSON>ton, <PERSON>box, Dialog, IconButton, <PERSON>ack, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import CloseIcon from "@mui/icons-material/Close";
import Input from "@/components/inputs/Input";
import { useApi } from "@/context/ApiProvider";
import Loader from "@/components/Loader";
import FormatUtils from "@/utils/format.utils";
import { useSnack } from "@/context/SnackProvider";

export default function ButtonDuplicatePrice({ item, agency, prices, onSuccess }) {
  const { post, put, get } = useApi();
  const { add } = useSnack();
  const [open, setOpen] = useState(false);
  const [openConfirm, setOpenConfirm] = useState(false);
  const [areas, setAreas] = useState(null);
  const [search, setSearch] = useState("");
  const [selectedArea, setSelectedArea] = useState([]);

  const getData = async () => {
    const areas = await get("/location-areas", { agency: agency, pagination: false, "order[name]": "asc" });
    setAreas(areas.member);
  };

  useEffect(() => {
    if (open) {
      getData();
    }
  }, [open]);

  const handleArea = async (area) => {
    if (selectedArea.includes(area)) {
      setSelectedArea(selectedArea.filter((e) => e !== area));
    } else {
      setSelectedArea([...selectedArea, area]);
    }
  };

  const duplicate = async () => {
    try {
      const toCreate = selectedArea.filter((area) => !prices.find((price) => price.area?.["@id"] === area));
      const toUpdateAreas = selectedArea.filter((area) => prices.find((price) => price.area?.["@id"] === area));
      const toUpdate = prices.filter((price) => toUpdateAreas.includes(price.area?.["@id"]));
      const itemToDuplicate = { ...item };
      delete itemToDuplicate["@id"];
      delete itemToDuplicate.gridData["@id"];
      delete itemToDuplicate.createdAt;
      delete itemToDuplicate.updatedAt;
      delete itemToDuplicate.uuid;
      itemToDuplicate.gridData.pricings = itemToDuplicate.gridData.pricings?.map((pricing) => {
        const o = pricing;
        delete o["@id"];
        return {
          ...o,
        };
      });
      await Promise.all(
        toCreate.map(
          async (area) =>
            setTimeout(post("/price-grids", {
              ...itemToDuplicate,
              area: area,
            }), 200)
        )
      );
      await Promise.all(
        toUpdate.map(
          async (price) =>
            await put(`/price-grids/${price.uuid}`, {
              ...itemToDuplicate,
              area: price.area["@id"],
            })
        )
      );
      add("success", "Tarif dupliqué avec succès");
      onSuccess();
      setOpenConfirm(false);
      setOpen(false);
    } catch (error) {
      console.log(error);
      add("error", "Une erreur est survenue lors de la duplication du tarif");
    }
  };

  const handleValidate = async () => {
    const exists = selectedArea.filter((area) => prices.find((price) => price.area?.["@id"] === area));
    if (exists.length > 0) {
      setOpenConfirm(true);
    } else {
      duplicate();
    }
  };

  return (
    <>
      <Button startIcon={<ContentCopyIcon />} variant="contained" color="secondary" size="small" onClick={() => setOpen(true)}>
        Dupliquer
      </Button>
      <Dialog open={openConfirm} onClose={() => setOpenConfirm(false)}>
        <Stack p={2}>
          <Typography variant="body1">Certains cantons existent déjà et risquent d&apos;être perdu</Typography>
          <Typography variant="body1">Êtes-vous sur de vouloir continuer ?</Typography>
          <Stack direction="row" gap={1} pt={2} justifyContent="flex-end">
            <Button variant="contained" color="secondary" onClick={() => setOpenConfirm(false)}>
              Annuler
            </Button>
            <Button variant="contained" color="primary" onClick={duplicate}>
              Continuer
            </Button>
          </Stack>
        </Stack>
      </Dialog>
      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        fullWidth
        maxWidth="lg"
        sx={{
          ".MuiPaper-root": {
            height: "80vh",
            display: "grid",
            gridTemplateRows: "max-content max-content 1fr max-content",
            rowGap: 1,
          },
        }}
      >
        <Stack p={2} alignItems="center" justifyContent="space-between" direction="row">
          <Stack />
          <Typography variant="h2">Dupliquer le tarif</Typography>
          <IconButton onClick={() => setOpen(false)}>
            <CloseIcon />
          </IconButton>
        </Stack>
        <Stack px={4} py={2} gap={1} direction="row">
          <Input placeholder="Rechercher" fullWidth onChange={(e) => setSearch(e.target.value)} />
        </Stack>
        {!areas ? (
          <Loader />
        ) : (
          <Stack p={4} pt={0} gap={1} overflow="auto">
            {areas.filter((area) => area.name.toLowerCase().includes(search.toLowerCase())).length === 0 ? (
              <Typography variant="bold">Aucun canton trouvé</Typography>
            ) : null}
            {areas
              .filter((area) => area.name.toLowerCase().includes(search.toLowerCase()))
              .map((area) => (
                <Stack
                  key={area.uuid}
                  border="1px solid #e0e0e0"
                  p={1}
                  borderRadius={2}
                  direction="row"
                  justifyContent="space-between"
                  alignItems="center"
                  gap={10}
                  bgcolor={selectedArea.includes(area["@id"]) ? "secondary.main" : "white"}
                  onClick={() => handleArea(area["@id"])}
                  sx={{
                    cursor: "pointer",
                  }}
                  color={selectedArea.includes(area["@id"]) ? "white" : "text.primary"}
                >
                  <Checkbox
                    checked={selectedArea.includes(area["@id"])}
                    color="white"
                    sx={{
                      color: selectedArea.includes(area["@id"]) ? "#FFF" : "text.primary",
                    }}
                  />
                  <Typography flexGrow={1} variant="bold">
                    {area.name}
                  </Typography>
                  <Typography variant="bold">
                    {FormatUtils.formatDate(area.updatedAt || area.createdAt)} à {FormatUtils.formatTime(area.updatedAt || area.createdAt)}
                  </Typography>
                </Stack>
              ))}
          </Stack>
        )}
        <Stack px={4} py={2} gap={1} direction="row" justifyContent="center">
          <Button variant="contained" color="secondary" onClick={handleValidate}>
            Valider
          </Button>
        </Stack>
      </Dialog>
    </>
  );
}

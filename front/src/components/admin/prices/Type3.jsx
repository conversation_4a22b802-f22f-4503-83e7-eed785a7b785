import BackButton from "@/components/buttons/BackButton";
import TranslationUtils from "@/utils/translation.utils";
import { Button, Paper, Stack, Typography } from "@mui/material";
import { useState } from "react";

import Input from "@/components/inputs/Input";
import theme from "@/lib/theme";
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from "@mui/material";
import ButtonSave from "@/components/forms/agencies/ButtonSave";
import { FLAT_TYPE, HOUSE_TYPE, TYPE, PRICE_HOME_TYPE } from "@/enums/PRICE_HOME_TYPE";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import InputCodeAvantage from "@/components/InputCodeAvantage";

const diagNum = [1, 2, 3, 4, 5, 6, 7];

export default function Type3({ params, agency, price, onSuccess }) {
  const { put } = useApi();
  const { add } = useSnack();
  const [type, setType] = useState(PRICE_HOME_TYPE.FLAT);
  const [advantage, setAdvantage] = useState(price.advantages.advantages);

  const defaultDiags = ["auditPrice", "erpPrice"];

  const submit = async (e) => {
    e.preventDefault();
    const gridData = {};
    diagNum.forEach((diag) => {
      const flat = {};
      Object.values(FLAT_TYPE).forEach((flatKey) => {
        if (e.target[`${diag}-${flatKey}`]) {
          flat[flatKey] = +e.target[`${diag}-${flatKey}`].value;
        }
      });
      const house = {};
      Object.values(HOUSE_TYPE).forEach((houseKey) => {
        if (e.target[`${diag}-${houseKey}`]) {
          house[houseKey] = +e.target[`${diag}-${houseKey}`].value;
        }
      });
      if (Object.keys(flat).length > 0 || Object.keys(house).length > 0) {
        const o = {
          ...flat,
          ...house,
        };
        gridData[`activity${diag}`] = o;
      }
    });
    defaultDiags.forEach((diag) => {
      const flat = {};
      Object.values(FLAT_TYPE).forEach((flatKey) => {
        if (e.target[`${diag}-${flatKey}`]) {
          flat[flatKey] = +e.target[`${diag}-${flatKey}`].value;
        }
      });
      const house = {};
      Object.values(HOUSE_TYPE).forEach((houseKey) => {
        if (e.target[`${diag}-${houseKey}`]) {
          house[houseKey] = +e.target[`${diag}-${houseKey}`].value;
        }
      });
      if (Object.keys(flat).length > 0 || Object.keys(house).length > 0) {
        const o = {
          ...flat,
          ...house,
        };
        gridData[diag] = o;
      }
    });
    gridData.cgv = e.target.cgv.value.length > 0 ? e.target.cgv.value : null;
    gridData.supplementCrep = +e.target.supplementCrep.value;
    gridData.appointmentDiscount = +e.target.appointmentDiscount.value;
    gridData.discountCode = null;
    gridData.discountPercent = null;
    const params = {
      ...price,
      area: price.area ? price.area["@id"] : null,
      advantages: {
        ...price.advantages,
        advantages: advantage,
      },
      gridData,
    };
    try {
      await put(`/price-grids/${price.uuid}`, params);
      add("success", "La grille de prix a bien été modifiée !");
      onSuccess();
    } catch (error) {
      add("error", error.description);
    }
  };

  return (
    <Stack gap={2} component={"form"} onSubmit={submit}>
      <BackButton>Retour</BackButton>
      <Stack gap={1}>
        <Typography variant="h2">{agency.name}</Typography>
        <Typography variant="body1">Grille {params.type}</Typography>
        <Typography variant="body1">{price?.area?.name || price.name}</Typography>
      </Stack>
      <Stack direction="row" gap={1} justifyContent="flex-end">
        {Object.keys(PRICE_HOME_TYPE).map((key) => (
          <Button
            key={key}
            color="secondary"
            variant={type === PRICE_HOME_TYPE[key] ? "contained" : "outlined"}
            onClick={() => setType(PRICE_HOME_TYPE[key])}
          >
            {TranslationUtils.get(`prices.${PRICE_HOME_TYPE[key]}`)}
          </Button>
        ))}
      </Stack>
      <Stack gap={5}>
        <TableContainer
          sx={{
            overflowX: "visible",
          }}
        >
          <Table sx={{ minWidth: 650 }} aria-label="simple table">
            <TableHead sx={{ backgroundColor: "transparent" }}>
              <TableRow sx={{ borderLeft: "none !important", borderRight: "none !important" }}>
                <TableCell width={"35%"} sx={{ border: "none" }} />
                {Object.keys(TYPE[type]).map((key, index) => (
                  <TableCell
                    key={key}
                    sx={{
                      backgroundColor: theme.palette.secondary.main,
                      borderTopLeftRadius: index === 0 ? 10 : 0,
                      borderTopRightRadius: index === Object.keys(TYPE[type]).length - 1 ? 10 : 0,
                      fontWeight: "bold",
                    }}
                    align="center"
                  >
                    <Typography variant="bold">{key}</Typography>
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {diagNum.map((diag, index) => (
                <TableRow
                  key={index}
                  sx={{
                    backgroundColor: index % 2 === 0 ? theme.palette.grey.extraLight : "transparent",
                    borderLeft: "none",
                    position: "relative",
                  }}
                >
                  <TableCell sx={{ border: "none" }}>
                    <Typography variant="bold">
                      {diag} diagnostic{index === 0 ? null : "s"}
                    </Typography>
                  </TableCell>
                  {Object.values(FLAT_TYPE).map((key) => (
                    <TableCell
                      key={key}
                      align="center"
                      sx={{
                        display: type === PRICE_HOME_TYPE.FLAT ? "table-cell" : "none",
                      }}
                    >
                      <Input
                        name={`${diag}-${key}`}
                        slotProps={{
                          htmlInput: {
                            className: "number-without-arrows",
                          },
                          input: {
                            style: {
                              color: theme.palette.secondary.main,
                              fontWeight: "bold",
                              borderRadius: 0,
                            },
                          },
                        }}
                        type="number"
                        defaultValue={price.gridData[`activity${diag}`]?.[key] || 0}
                      />
                    </TableCell>
                  ))}
                  {Object.values(HOUSE_TYPE).map((key) => (
                    <TableCell
                      key={key}
                      align="center"
                      sx={{
                        display: type === PRICE_HOME_TYPE.HOUSE ? "table-cell" : "none",
                      }}
                    >
                      <Input
                        name={`${diag}-${key}`}
                        slotProps={{
                          htmlInput: {
                            className: "number-without-arrows",
                          },
                          input: {
                            style: {
                              color: theme.palette.secondary.main,
                              fontWeight: "bold",
                              borderRadius: 0,
                            },
                          },
                        }}
                        type="number"
                        defaultValue={price.gridData[`activity${diag}`]?.[key] || 0}
                      />
                    </TableCell>
                  ))}
                </TableRow>
              ))}
              {defaultDiags.map((diag, index) => (
                <TableRow
                  key={index}
                  sx={{
                    backgroundColor: index % 2 === 0 ? theme.palette.grey.extraLight : "transparent",
                    borderLeft: "none",
                    position: "relative",
                  }}
                >
                  <TableCell sx={{ border: "none" }}>
                    <Typography variant="bold">{TranslationUtils.get(`prices.${diag}`)}</Typography>
                  </TableCell>
                  {Object.values(FLAT_TYPE).map((key) => (
                    <TableCell
                      key={key}
                      align="center"
                      sx={{
                        display: type === PRICE_HOME_TYPE.FLAT ? "table-cell" : "none",
                      }}
                    >
                      <Input
                        name={`${diag}-${key}`}
                        slotProps={{
                          htmlInput: {
                            className: "number-without-arrows",
                          },
                          input: {
                            style: {
                              color: theme.palette.secondary.main,
                              fontWeight: "bold",
                              borderRadius: 0,
                            },
                          },
                        }}
                        type={type === PRICE_HOME_TYPE.FLAT && params.type === "3" && diag === "auditPrice" ? "hidden" : "number"}
                        defaultValue={price.gridData[diag][key] || 0}
                      />
                    </TableCell>
                  ))}
                  {Object.values(HOUSE_TYPE).map((key) => (
                    <TableCell
                      key={key}
                      align="center"
                      sx={{
                        display: type === PRICE_HOME_TYPE.HOUSE ? "table-cell" : "none",
                      }}
                    >
                      <Input
                        name={`${diag}-${key}`}
                        slotProps={{
                          htmlInput: {
                            className: "number-without-arrows",
                          },
                          input: {
                            style: {
                              color: theme.palette.secondary.main,
                              fontWeight: "bold",
                              borderRadius: 0,
                            },
                          },
                        }}
                        type="number"
                        defaultValue={price.gridData[diag][key] || 0}
                      />
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        <Stack component={Paper} p={4} gap={2} display="grid" gridTemplateColumns="max-content 1fr" alignItems="center">
          <Typography variant="bold">Supplément CREP (€)</Typography>
          <Input
            slotProps={{
              htmlInput: {
                className: "number-without-arrows",
                min: 0,
                max: 100000,
              },
              input: {
                style: { color: theme.palette.secondary.main, borderRadius: 0 },
              },
            }}
            name="supplementCrep"
            type="number"
            defaultValue={price.gridData.supplementCrep || 0}
          />
          <Typography variant="bold">Réduction RDV (%)</Typography>
          <Input
            slotProps={{
              htmlInput: {
                className: "number-without-arrows",
                min: 0,
                max: 100000,
              },
              input: {
                style: { color: theme.palette.secondary.main, borderRadius: 0 },
              },
            }}
            name="appointmentDiscount"
            type="number"
            defaultValue={price.gridData.appointmentDiscount || 0}
          />
          <Stack
            style={{
              gridColumn: "1 / -1",
            }}
          >
            <InputCodeAvantage price={price} advantage={advantage} setAdvantage={setAdvantage} />
          </Stack>
        </Stack>
        <Stack gap={1}>
          <Typography variant="bold">Conditions générales de vente</Typography>
          <Input
            name="cgv"
            placeholder="Devis, personnalisation de vos conditions générales de vente (phrase ajoutée dans le bas du devis)"
            multiline
            rows={7}
            fullWidth
            defaultValue={price.gridData.cgv || ""}
          />
        </Stack>
        <ButtonSave label="Enregistrer" />
      </Stack>
    </Stack>
  );
}

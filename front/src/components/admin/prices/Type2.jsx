"use client";

import BackButton from "@/components/buttons/BackButton";
import ButtonSave from "@/components/forms/agencies/ButtonSave";
import InputCodeAvantage from "@/components/InputCodeAvantage";
import Input from "@/components/inputs/Input";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import TranslationUtils from "@/utils/translation.utils";
import { FormControlLabel, Radio, RadioGroup, Stack, Typography, useTheme } from "@mui/material";
import React, { useState } from "react";

export default function Type2({ params, agency, price, onSuccess }) {
  const theme = useTheme();
  const { put } = useApi();
  const { add } = useSnack();
  const [advantage, setAdvantage] = useState(price.advantages.advantages);

  const submit = async (e) => {
    e.preventDefault();

    const obj = {
      ...price,
      area: price.area ? price.area["@id"] : null,
      gridData: {
        ...price.gridData,
        basePrice: +e.target.basePrice.value,
        apartmentRatio: +e.target.apartmentRatio.value,
        amiantePrice: +e.target.amiantePrice.value,
        appointmentDiscount: +e.target.appointmentDiscount.value,
        crepRatio: +e.target.crepRatio.value,
        erpPrice: +e.target.erpPrice.value,
        cgv: e.target.cgv.value.length > 0 ? e.target.cgv.value : null,
      },
      advantages: {
        ...price.advantages,
        advantages: advantage,
      },
    };
    if (obj?.["@id"]) {
      delete obj["@id"];
    }
    if (obj.gridData?.["@id"]) {
      delete obj.gridData["@id"];
    }

    Array.from({ length: 8 }).map((_, index) => {
      const key = `audit${index + 1}Room`;
      if (e.target[key]) {
        obj.gridData[key] = +e.target[key].value;
      }
    });

    Array.from({ length: 7 }).map((_, idx) => {
      const key = `priceRoom${idx + 2}`;
      if (e.target[key]) {
        obj.gridData[key] = +e.target[key].value;
      }
    });

    Array.from({ length: 7 }).map((_, i) => {
      const key = `decrementActivity0${i + 2}`;
      if (e.target[key]) {
        obj.gridData[key] = +e.target[key].value;
      }
    });

    try {
      await put(`/price-grids/${price.uuid}`, obj);
      add("success", "Prix modifiés avec succès");
      onSuccess();
    } catch (error) {
      add("error", TranslationUtils.get("global.error"));
      console.error(error);
    }
  };

  const InputNumberRounded = ({ label, name, defaultValue, maxWidth = null, type = "number" }) => (
    <Stack gap={0.5} width={maxWidth ? "unset" : "100%"}>
      <Typography variant="bold">{label}</Typography>
      <Input
        name={name}
        defaultValue={defaultValue}
        slotProps={{
          htmlInput: {
            className: "input-without-arrows",
            min: 0,
            step: 0.01,
          },
          input: {
            style: {
              fontWeight: "bold",
              borderRadius: 5,
              maxWidth: maxWidth ? maxWidth : "unset",
            },
          },
        }}
        type={type}
        fullWidth={!maxWidth}
      />
    </Stack>
  );

  const RadioSecondary = ({ ...props }) => (
    <Radio
      {...props}
      sx={{
        "&.Mui-checked": {
          color: theme.palette.secondary.main,
        },
      }}
    />
  );

  return (
    <Stack gap={2} component="form" onSubmit={submit}>
      <BackButton>Retour</BackButton>
      <Stack gap={1}>
        <Typography variant="h2">{agency.name}</Typography>
        <Typography variant="body1">Grille {params.type}</Typography>
        <Typography variant="body1">{price?.area?.name || price.name}</Typography>
      </Stack>
      <Stack bgcolor="#fff" p={4} justifyContent="center" sx={{ borderRadius: 2 }}>
        <Stack gap={2}>
          <Typography variant="bold">AUDIT ÉNERGÉTIQUE / PIÈCE</Typography>
          <Stack gap={1} direction="row" flexWrap="wrap">
            {Array.from({ length: 8 }).map((_, i) => (
              <InputNumberRounded
                key={i}
                label={`${i + 1} pièce`}
                name={`audit${i + 1}Room`}
                defaultValue={price.gridData?.[`audit${i + 1}Room`]}
                maxWidth={120}
              />
            ))}
          </Stack>
          <Stack direction="row" gap={2}>
            <InputNumberRounded label="TARIF (1-MISSION,MAISON,T1)" name="basePrice" defaultValue={price.gridData?.basePrice} />
            <InputNumberRounded label="COEFF APPART" name="apartmentRatio" defaultValue={price.gridData?.apartmentRatio} />
          </Stack>
          <Typography variant="bold">INCRÉMENT / PIÈCE</Typography>
          <Stack gap={1} direction="row" flexWrap="wrap">
            {Array.from({ length: 7 }).map((_, i) => (
              <InputNumberRounded
                key={i}
                label={`${i + 2} pièce`}
                name={`priceRoom${i + 2}`}
                defaultValue={price.gridData?.[`priceRoom${i + 2}`]}
                maxWidth={120}
              />
            ))}
          </Stack>
          <Typography variant="bold">DÉGRESSION / MISSION</Typography>
          <Stack gap={1} direction="row" flexWrap="wrap">
            {Array.from({ length: 7 }).map((_, i) => (
              <InputNumberRounded
                key={i}
                label={`${i + 2} missions`}
                name={`decrementActivity${i + 2}`}
                defaultValue={price.gridData?.[`decrementActivity${i + 2}`]}
                maxWidth={120}
              />
            ))}
          </Stack>
          <Stack gap={0}>
            <Typography variant="bold">Coef Crep</Typography>
            <RadioGroup defaultValue={price.gridData?.crepRatio} name="crepRatio" row>
              <Stack direction="row" gap={2}>
                <FormControlLabel value={1} control={<RadioSecondary />} label="1" />
                <FormControlLabel value={1.5} control={<RadioSecondary />} label="1,5" />
                <FormControlLabel value={2} control={<RadioSecondary />} label="2" />
              </Stack>
            </RadioGroup>
          </Stack>
          <Stack direction="row" gap={2}>
            <InputNumberRounded label="FRAIS DE PRÉLÈVEMENT AMIANTE" name="amiantePrice" defaultValue={price.gridData?.amiantePrice} />
            <InputNumberRounded label="TARIF ERP" name="erpPrice" defaultValue={price.gridData?.erpPrice} />
          </Stack>
          <Stack direction="row" gap={2}>
            <InputNumberRounded label="RÉDUCTION RDV %" name="appointmentDiscount" defaultValue={price.gridData?.appointmentDiscount} />
          </Stack>
          <Stack direction="row" width="50%" pr={1}>
            <InputCodeAvantage price={price} advantage={advantage} setAdvantage={setAdvantage} />
          </Stack>
        </Stack>
      </Stack>
      <Stack gap={1}>
        <Typography variant="bold">Conditions générales de vente</Typography>
        <Input
          placeholder="Devis, personnalisation de vos conditions générales de vente (phrase ajoutée dans le bas du devis)"
          multiline
          rows={7}
          fullWidth
          name="cgv"
          defaultValue={price.gridData.cgv}
        />
      </Stack>
      <ButtonSave label="Enregistrer" />
    </Stack>
  );
}

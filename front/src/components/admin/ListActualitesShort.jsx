"use client";

import { Pagination, Paper, Skeleton, Stack, Typography } from "@mui/material";
import { Suspense, useEffect, useState } from "react";
import CardAdminActualites from "./CardAdminActualites";
import Loader from "../Loader";
import { useApi } from "@/context/ApiProvider";
import { usePathname, useRouter, useSearchParams } from "next/navigation";

export default function ListActualitesShort({ cabinetId }) {
  const { get } = useApi();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const page = searchParams.get("page") ?? 1;

  const [news, setNews] = useState(null);

  const getData = async () => {
    const data = await get(`/agency-posts?agency.legacyId=${cabinetId}&itemsPerPage=5&page=${page}`);
    setNews(data);
    return data;
  };

  useEffect(() => {
    getData();
  }, [page]);

  if (!news) return <Loader />;

  return (
    <Suspense fallback={<Skeleton variant="rectangular" />}>
      <Stack component={Paper} p={3} spacing={2}>
        <Typography variant="h2">Actualités</Typography>
        <Stack spacing={2} id="news">
          {news?.member?.length ? (
            news?.member?.map((actu, i) => <CardAdminActualites key={i} news={actu} direction="row" />)
          ) : (
            <Typography>Aucune actualité</Typography>
          )}
          <Stack alignItems="center">
            <Pagination
              count={Math.ceil(news?.totalItems / 5)}
              page={page}
              onChange={(_, page) => router.push(pathname + "?page=" + page + "#news")}
            />
          </Stack>
        </Stack>
      </Stack>
    </Suspense>
  );
}

"use client";
import BackButton from "@/components/buttons/BackButton";
import { useSnack } from "@/context/SnackProvider";
import { mapStore } from "@/mappers/stores.mapper";
import { Button, Paper, Stack, Typography } from "@mui/material";
import GeneralFormAgencies from "./GeneralFormAgencies";
import StoreSuperAdmin from "./StoreSuperAdmin";
import TimeFormAgencies from "./TimeFormAgencies";
import { useApi } from "@/context/ApiProvider";
import InputMedias from "@/components/inputs/InputMedias";
import ButtonSave from "@/components/forms/agencies/ButtonSave";
import ROUTES from "@/enums/ROUTES";
import TranslationUtils from "@/utils/translation.utils";

export default function FormAgencies({ agency }) {
  const { put } = useApi();
  const { add } = useSnack();

  const submit = async (e) => {
    try {
      e.preventDefault();
      const data = Object.fromEntries(new FormData(e.target));
      if (!data.legacyId) {
        data.legacyId = agency.legacyId
      }

      if (agency?.['@id'] ?? false) {
        data['@id'] = agency?.['@id']
      }

      await put(`/agencies/${agency?.legacyId}`, mapStore(data));
      add("success", `Le cabinet a bien été ${agency ? "modifié" : "ajouté"} !`);
    } catch (error) {
      add("error", error.description);
      console.log(error);
    }
  };

  return (
    <Stack component="form" onSubmit={submit} gap={4}>
      <Stack component={Paper} p={4} gap={2}>
        <BackButton url={ROUTES.STORES}>{TranslationUtils.get("agencies._back")}</BackButton>
        <Stack display="grid" gap={2} gridTemplateColumns="5fr 1fr">
          <Typography variant="h2">{TranslationUtils.get("agencies.info")}</Typography>
          {agency.linkToOpen ?
            <Button size={'small'} href={agency.linkToOpen} target="_blank">Aller vers la plateforme</Button>
            : <Button size={'small'} disabled={true} aria-disabled={true}>Lien vers la plateforme non défini</Button>
          }

        </Stack>
        <Stack gap={4}>
          <GeneralFormAgencies item={agency} />
          <TimeFormAgencies item={agency} />
          <ButtonSave />
        </Stack>
      </Stack>
      <Stack component={Paper} p={4} gap={2}>
        <Typography variant="h2">{TranslationUtils.get("agencies.image")}</Typography>
        <InputMedias name="image" defaultValue={agency?.upload} />
        <ButtonSave />
      </Stack>
      <Stack component={Paper} p={4} gap={2}>
        <Typography variant="h2">{TranslationUtils.get("agencies.contactImage")}</Typography>
        <InputMedias name="contactImage" defaultValue={agency?.contact?.upload} />
        <ButtonSave />
      </Stack>
      <StoreSuperAdmin item={agency} />
    </Stack>
  );
}

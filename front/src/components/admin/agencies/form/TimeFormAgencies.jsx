import Input from "@/components/inputs/Input";
import { FormControlLabel, Paper, Stack, Switch, Typography } from "@mui/material";
import TranslationUtils from "@/utils/translation.utils";

const InputTime = ({ name, defaultValue }) => (
  <Stack direction="row" gap={1} alignItems="center">
    <Input name={`start${name}`} type="time" defaultValue={defaultValue.start} />
    -
    <Input name={`end${name}`} type="time" defaultValue={defaultValue.end} />
  </Stack>
);

export default function TimeFormAgencies({ item }) {
  return (
    <Stack gap={4} component={Paper}>
      <Typography variant="h2">{TranslationUtils.get("agencies.schedule")}</Typography>
      <Stack display="grid" gridTemplateColumns="1fr 2fr 2fr" columnGap={5} rowGap={2} width="40%" alignItems="center">
        <Stack />
        <Typography variant="bold" textAlign="center">
          {TranslationUtils.get("agencies.morning")}
        </Typography>
        <Typography variant="bold" textAlign="center">
          {TranslationUtils.get("agencies.afternoon")}
        </Typography>
        <Typography variant="bold">{TranslationUtils.get("agencies.monday")}</Typography>
        <InputTime
          name="MondayMorning"
          defaultValue={{
            start: item.schedule.monday.morning.openAt,
            end: item.schedule.monday.morning.closeAt,
          }}
        />
        <InputTime
          name="MondayAfternoon"
          defaultValue={{
            start: item.schedule.monday?.afternoon?.openAt,
            end: item.schedule.monday?.afternoon?.closeAt,
          }}
        />
        <Typography variant="bold">{TranslationUtils.get("agencies.tuesday")}</Typography>
        <InputTime
          name="TuesdayMorning"
          defaultValue={{
            start: item.schedule.tuesday?.morning?.openAt,
            end: item.schedule.tuesday?.morning?.closeAt,
          }}
        />
        <InputTime
          name="TuesdayAfternoon"
          defaultValue={{
            start: item.schedule.tuesday?.afternoon?.openAt,
            end: item.schedule.tuesday?.afternoon?.closeAt,
          }}
        />
        <Typography variant="bold">{TranslationUtils.get("agencies.wednesday")}</Typography>
        <InputTime
          name="WednesdayMorning"
          defaultValue={{
            start: item.schedule.wednesday?.morning?.openAt,
            end: item.schedule.wednesday?.morning?.closeAt,
          }}
        />
        <InputTime
          name="WednesdayAfternoon"
          defaultValue={{
            start: item.schedule.wednesday?.afternoon?.openAt,
            end: item.schedule.wednesday?.afternoon?.closeAt,
          }}
        />
        <Typography variant="bold">{TranslationUtils.get("agencies.thursday")}</Typography>
        <InputTime
          name="ThursdayMorning"
          defaultValue={{
            start: item.schedule.thursday?.morning?.openAt,
            end: item.schedule.thursday?.morning?.closeAt,
          }}
        />
        <InputTime
          name="ThursdayAfternoon"
          defaultValue={{
            start: item.schedule.thursday?.afternoon?.openAt,
            end: item.schedule.thursday?.afternoon?.closeAt,
          }}
        />
        <Typography variant="bold">{TranslationUtils.get("agencies.friday")}</Typography>
        <InputTime
          name="FridayMorning"
          defaultValue={{
            start: item.schedule.friday?.morning?.openAt,
            end: item.schedule.friday?.morning?.closeAt,
          }}
        />
        <InputTime
          name="FridayAfternoon"
          defaultValue={{
            start: item.schedule.friday?.afternoon?.openAt,
            end: item.schedule.friday?.afternoon?.closeAt,
          }}
        />
        <Typography variant="bold">{TranslationUtils.get("agencies.saturday")}</Typography>
        <InputTime
          name="SaturdayMorning"
          defaultValue={{
            start: item.schedule.saturday?.morning?.openAt,
            end: item.schedule.saturday?.morning?.closeAt,
          }}
        />
        <InputTime
          name="SaturdayAfternoon"
          defaultValue={{
            start: item.schedule.saturday?.afternoon?.openAt,
            end: item.schedule.saturday?.afternoon?.closeAt,
          }}
        />
        <Typography variant="bold">{TranslationUtils.get("agencies.sunday")}</Typography>
        <InputTime
          name="SundayMorning"
          defaultValue={{
            start: item.schedule.sunday?.morning?.openAt,
            end: item.schedule.sunday?.morning?.closeAt,
          }}
        />
        <InputTime
          name="SundayAfternoon"
          defaultValue={{
            start: item.schedule.sunday?.afternoon?.openAt,
            end: item.schedule.sunday?.afternoon?.closeAt,
          }}
        />
      </Stack>
      <FormControlLabel
        control={<Switch defaultChecked={item?.enablePaymentThreeTimeNoFee} />}
        label={TranslationUtils.get("agencies.paymentEnabled")}
        name="paymentEnabled"
      />
      <FormControlLabel
          control={<Switch defaultChecked={item?.displayCallIfAvailableNow} />}
          label={"Désactiver les calculs de devis en faveur de l'appel si l'agence est ouverte"}
          name="displayCallIfAvailableNow"
      />
    </Stack>
  );
}

import Input from "@/components/inputs/Input";
import InputWysiwyg from "@/components/inputs/wysiwyg/InputWysiwyg";
import { Paper, Stack } from "@mui/material";
import TranslationUtils from "@/utils/translation.utils";

export default function GeneralFormAgencies({ item }) {
  return (
    <Stack gap={4} component={Paper}>
      <Stack display="grid" gap={2} gridTemplateColumns="1fr 1fr">
        <Input label={TranslationUtils.get("agencies.name")} name="name" fullWidth autoFocus required defaultValue={item.name} />
        <Input label={TranslationUtils.get("agencies.city")} name="city" fullWidth required defaultValue={item.location.city} />
        <Input label={TranslationUtils.get("agencies.zip")} name="zip" fullWidth required defaultValue={item.location.postcode} />
        <Input label={TranslationUtils.get("agencies.address")} name="address" fullWidth required defaultValue={item.location.address1} />
        <Input label={TranslationUtils.get("agencies.contactName")} name="contactName" fullWidth required defaultValue={item.contact.name} />
        <Input label={TranslationUtils.get("agencies.contactPhone")} name="contactPhone" fullWidth required defaultValue={item.contact.phone} />
        <Input label={TranslationUtils.get("agencies.contactEmail")} name="contactEmail" fullWidth required defaultValue={item.contact.email} />
        <Input label={TranslationUtils.get("agencies.certifications")} name="certifications" fullWidth required defaultValue={item.certifications} />
        <Input label="Lien Facebook" name="facebookLink" fullWidth defaultValue={item?.facebookLink} />
        <Input label="Lien LinkedIn" name="linkedinLink" fullWidth defaultValue={item?.linkedinLink} />
        <Input label={TranslationUtils.get("agencies.metaTitle")} name="metaTitle" fullWidth defaultValue={item.meta.title} />
        <Input label={"Lien vers la plateforme"} name="linkToOpen" fullWidth defaultValue={item.linkToOpen} />
      </Stack>
      <Input
        minRows={3}
        multiline
        label={TranslationUtils.get("agencies.metaDescription")}
        name="metaDescription"
        fullWidth
        type="textarea"
        defaultValue={item.meta.description}
      />
      <InputWysiwyg label={TranslationUtils.get("agencies.description")} name="description" defaultValue={item.description} />
    </Stack>
  );
}

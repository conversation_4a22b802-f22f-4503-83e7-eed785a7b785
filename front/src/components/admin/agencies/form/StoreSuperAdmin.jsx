"use client";
import { useUser } from "@/context/UserProvider";
import Input from "@/components/inputs/Input";
import { FormControlLabel, Paper, Stack, Switch, Typography } from "@mui/material";
import SelectCantons from "@/components/inputs/SelectCantons";
import ButtonSave from "@/components/forms/agencies/ButtonSave";
import TranslationUtils from "@/utils/translation.utils";
import SelectLyraSeller from "@/components/inputs/SelectLyraSeller";

export default function StoreSuperAdmin({ item }) {
  const { isSuperAdmin } = useUser();
  if (!isSuperAdmin()) {
    return null;
  }

  return (
    <Stack component={Paper} p={3} spacing={2}>
      <Typography variant="h2" pb={2}>
        {TranslationUtils.get("agencies.superAdmin")}
      </Typography>
      <Input label={TranslationUtils.get("agencies.html")} name="html" fullWidth multiline minRows={5} defaultValue={item.ratingWidget?.html || ""} />
      <Input label={TranslationUtils.get("agencies.js")} name="js" fullWidth multiline minRows={5} defaultValue={item.ratingWidget?.js || ""} />
      <SelectLyraSeller defaultValue={item.lyraSeller} />
      <SelectCantons label={TranslationUtils.get("agencies.cantons")} name="areas" multiple defaultValue={item.locationAreas} />
      <FormControlLabel
        control={<Switch defaultChecked={item?.displayAppointment} />}
        label={TranslationUtils.get("agencies.displayAppointment")}
        name="displayAppointment"
      />
      <ButtonSave />
    </Stack>
  );
}

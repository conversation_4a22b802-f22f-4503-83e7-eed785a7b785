"use client";

import ROUTES from "@/enums/ROUTES";
import { Button, Link, Paper, Stack, Typography } from "@mui/material";
import QSearch from "../../QSearch";
import NoResult from "../../NoResult";
import TranslationUtils from "@/utils/translation.utils";
import Pagination from "@/components/Pagination";

export default function Agencies({ data }) {
  return (
    <Stack height="100%" component={Paper} p={{ xs: 1, md: 4 }} gap={2}>
      <Typography variant="h1">
        {TranslationUtils.get("agencies.list")}
      </Typography>
      <QSearch />
      {!data.member.length ? <NoResult /> : null}
      {data.member?.map((item) => (
        <Stack key={item["@id"]} direction="row" justifyContent="space-between" alignItems="center" border="1px solid #e0e0e0" borderRadius={2} p={3}>
          <Typography variant="h6">{item?.name}</Typography>
          <Button color="secondary" component={Link} href={`${ROUTES.STORES}/${item.uuid}`}>
            {TranslationUtils.get("global.manage")}
          </Button>
        </Stack>
      ))}
      <Pagination data={data} />
    </Stack>
  );
}

"use client";

import ROUTES from "@/enums/ROUTES";
import { <PERSON>ton, Paper, Stack, Typography } from "@mui/material";
import Link from "next/link";
import { useEffect, useState } from "react";
import DeleteModal from "../DeleteModal";
import CardAdminActualites from "./CardAdminActualites";
import { useApi } from "@/context/ApiProvider";
import Input from "../inputs/Input";
import NoResult from "../NoResult";
import TranslationUtils from "@/utils/translation.utils";
import Pagination from "../Pagination";
import { useSearchParams } from "next/navigation";
import Loader from "../Loader";
import DIRECTIONS from "@/enums/DIRECTIONS";

export default function StoreActu({ agency }) {
  const [actus, setActus] = useState(null);
  const [deleteModal, setDeleteModal] = useState(false);
  const [search, setSearch] = useState(null);
  const [loading, setLoading] = useState(true);

  const searchParams = useSearchParams();
  const { get } = useApi();

  const getData = async () => {
    try {
      const params = {
        page: searchParams.get("page") || 1,
        "agency.legacyId": agency.legacyId,
        "order[createdAt]": DIRECTIONS.DESC,
      };
      if (search) {
        params.title = search;
      }
      const response = await get(`/agency-posts`, params);
      setActus(response);
    } catch (error) {
      console.error(error);
    } finally {
      if (loading) {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    void getData();
  }, [searchParams.get("page"), search]);

  if (loading) {
    return <Loader />;
  }

  return (
    <Stack component={Paper} p={4} gap={2}>
      <Typography variant="h2">
        {TranslationUtils.get("agencies.posts._plural")} ({actus?.totalItems})
      </Typography>
      <Stack
        component="form"
        direction="row"
        gap={2}
        width="100%"
        justifyContent="space-between"
        alignItems="flex-end"
        onSubmit={(e) => {
          e.preventDefault();
          setSearch(e.target.search.value);
        }}
      >
        <Stack direction="row" gap={1} alignItems="flex-end" width="100%">
          <Input label={TranslationUtils.get("global.search")} fullWidth name="search" type="search" defaultValue={search} />
          <Button type="submit">{TranslationUtils.get("global.search")}</Button>
        </Stack>
        <Button LinkComponent={Link} href={`${ROUTES.STORES}/${agency.uuid}/actualites/new/${agency.legacyId}`}>
          {TranslationUtils.get("global.add")}
        </Button>
      </Stack>
      <Stack gap={2}>
        {actus?.member?.length === 0 ? (
          <NoResult />
        ) : (
          actus?.member?.map((actu) => (
            <Stack direction="row" gap={5} key={actu.uuid}>
              <CardAdminActualites news={actu} direction="row" />
              <Stack gap={1} alignItems="flex-start">
                <Button color="secondary" variant="contained" LinkComponent={Link} href={`${ROUTES.STORES}/${agency.uuid}/actualites/${actu.uuid}`}>
                  {TranslationUtils.get("global.edit")}
                </Button>
                <Button color="secondary" variant="contained" onClick={() => setDeleteModal(actu)}>
                  {TranslationUtils.get("global.delete")}
                </Button>
              </Stack>
            </Stack>
          ))
        )}
      </Stack>
      <Stack alignItems="center" justifyContent="center">
        <Pagination data={actus} />
      </Stack>
      <DeleteModal item={deleteModal} nameKey="title" onClose={() => setDeleteModal(null)} onRefresh={() => getData()} />
    </Stack>
  );
}

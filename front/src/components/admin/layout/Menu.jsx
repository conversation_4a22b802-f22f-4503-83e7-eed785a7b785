"use client";

import ROUTES from "@/enums/ROUTES";
import { Button, Stack } from "@mui/material";
import OpenInNewIcon from "@mui/icons-material/OpenInNew";
import { Link } from "next/link";
import { usePathname } from "next/navigation";
import { useUser } from "@/context/UserProvider";
import USER_ROLES from "@/enums/USER_ROLES";
import TranslationUtils from "@/utils/translation.utils";

const links = (user) => [
  {
    label: TranslationUtils.get("navs.myprices"),
    link: ROUTES.PRICES,
  },
  {
    label: TranslationUtils.get("navs.myaccount"),
    link: ROUTES.ACCOUNT,
  },
  {
    label: TranslationUtils.get("navs.mystores"),
    link: ROUTES.STORES,
  },
  {
    label: TranslationUtils.get("navs.stats"),
    link: ROUTES.STATS,
  },
  {
    label: TranslationUtils.get("navs.mydevis"),
    link: ROUTES.DEVIS,
  },
  ...(user?.role === USER_ROLES.SUPERADMIN ? [{ label: TranslationUtils.get("navs.cms"), link: ROUTES.BUILDER }] : []),
  {
    label: TranslationUtils.get("navs.kiosque"),
    link: ROUTES.KIOSQUE,
    external: true,
  },
];

export default function Menu() {
  const pathname = usePathname();
  const { user } = useUser();

  return (
    <Stack gap={2}>
      {links(user).map((link, index) => {
        return (
          <Button
            key={index}
            component={Link}
            href={link.link}
            target={link.external ? "_blank" : "_self"}
            variant={pathname.includes(link.link) ? "contained" : "outlined"}
            color="secondary"
          >
            {link.label}
            {link.external ? <OpenInNewIcon sx={{ ml: 1, fontSize: 14 }} /> : null}
          </Button>
        );
      })}
    </Stack>
  );
}

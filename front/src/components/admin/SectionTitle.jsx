"use client";

import { Stack, Typography } from "@mui/material";
import LayoutContainer from "../LayoutContainer";
import TranslationUtils from "@/utils/translation.utils";

export default function SectionTitle({ text = TranslationUtils.get("navs.account"), color = "secondary.main", textColor = "white" }) {
  return (
    <Stack bgcolor={color} color={textColor}>
      <LayoutContainer textAlign="center" py={5} position="relative">
        <Typography variant="h1">{text}</Typography>
      </LayoutContainer>
    </Stack>
  );
}

"use client";

import { Paper, Stack, Typography, useTheme } from "@mui/material";

export default function CabinetSection({ agency }) {
  const theme = useTheme();

  return (
    <Paper sx={{ py: 3, px: 4, backgroundColor: theme.palette.secondary.main, color: "white" }}>
      <Stack bgcolor={theme.palette.secondary.main}>
        <Typography variant="h3" textAlign="center">
          Votre cabinet
        </Typography>
        <Typography textAlign="center">{agency?.name}</Typography>
        <Typography textAlign="center">{agency?.contact?.phone?.replace(/(\d{2})(?=\d)/g, "$1 ")}</Typography>
      </Stack>
    </Paper>
  );
}

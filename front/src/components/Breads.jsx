"use client";

import { Breadcrumbs, <PERSON> as <PERSON><PERSON><PERSON><PERSON>, Typography } from "@mui/material";
import Link from "next/link";
import LayoutContainer from "./LayoutContainer";
import { usePathname } from "next/navigation";
import Script from "next/script";

export default function Breads() {
  const paths = usePathname();
  const pathNames = paths.split("/").filter((path) => path);

  if (!pathNames.length) {
    return null;
  }

  function capitalizeFirstLetter(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
  }

  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Accueil",
        item: `${process.env.NEXT_PUBLIC_API_URL}/`,
      },
      ...pathNames.map((path, index) => ({
        "@type": "ListItem",
        position: index + 2,
        name: capitalize<PERSON>irstLetter(path.replace(/-/g, " ")),
        item: `${process.env.NEXT_PUBLIC_API_URL}/${path}`,
      })),
    ],
  };

  return (
    <LayoutContainer>
      <Script id="BreadcrumbList" type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }} />
      <Breadcrumbs aria-label="breadcrumb" separator=">">
        <MuiLink component={Link} underline="hover" color="inherit" href="/" fontSize={12}>
          Accueil
        </MuiLink>
        {pathNames.map((link, index) => {
          const title = capitalizeFirstLetter(link.replace(/-/g, " "));

          if (index === pathNames.length - 1) {
            return (
              <Typography key={link} fontWeight="bolder" fontSize={12}>
                {title}
              </Typography>
            );
          }

          return (
            <MuiLink key={link} component={Link} underline="hover" color="inherit" href="/" fontSize={12}>
              {title}
            </MuiLink>
          );
        })}
      </Breadcrumbs>
    </LayoutContainer>
  );
}

import { useApi } from "@/context/ApiProvider";
import { Accordion, AccordionDetails, AccordionSummary, Checkbox, FormControlLabel, Stack, Switch, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import Loader from "./Loader";

export default function InputAccordionCantons({ item, setAreas }) {
  const { get } = useApi();
  const [departments, setDepartments] = useState([]);
  const [agencyAreas, setAgencyAreas] = useState([]);
  const [allCheckedAreas, setAllCheckedAreas] = useState([]);

  useEffect(() => {
    setAreas(allCheckedAreas);
  }, [allCheckedAreas]);

  const getAgencyAreas = async () => {
    try {
      const deps = await get("/location-areas", { pagination: false, "order[code]": "asc", agency: item.uuid });
      setAgencyAreas(deps.member);
    } catch (error) {
      console.log(error);
    }
  };

  const getDepartments = async () => {
    try {
      const deps = await get("/location-departments", { pagination: false, "order[code]": "asc" });
      setDepartments(deps.member);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getAgencyAreas();
    getDepartments();
  }, []);

  return (
    <Stack>
      <Typography variant="h5" mb={2}>
        Cantons
      </Typography>
      {departments.map((dep, index) => {
        return (
          <AccordionDepartment
            setAllCheckedAreas={setAllCheckedAreas}
            agencyAreas={agencyAreas}
            key={index}
            isLast={index === departments.length - 1}
            department={dep}
          />
        );
      })}
    </Stack>
  );
}

const AccordionDepartment = ({ department, agencyAreas, isLast, setAllCheckedAreas }) => {
  const [expanded, setExpanded] = useState(false);
  const [areas, setAreas] = useState([]);
  const [checkedAreas, setCheckedAreas] = useState(agencyAreas);
  const [loading, setLoading] = useState(false);
  const [allChecked, setAllChecked] = useState(false);
  const { get } = useApi();

  useEffect(() => {
    setCheckedAreas(agencyAreas);
  }, [agencyAreas]);

  useEffect(() => {
    setAllCheckedAreas(checkedAreas);
  }, [checkedAreas]);

  const getAreas = async () => {
    setLoading(true);
    try {
      const areas = await get("/location-areas", { pagination: false, "order[name]": "asc", department: department?.["@id"] });
      setAreas(areas.member);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const addArea = (area) => {
    setCheckedAreas([...checkedAreas, area]);
  };

  const removeArea = (area) => {
    setCheckedAreas(checkedAreas.filter((e) => e.uuid !== area.uuid));
  };

  const areArraysEqual = (arr1, arr2) => {
    if (arr1.length !== arr2.length) {
      return false;
    }

    const sortedArr1 = arr1.map((obj) => JSON.stringify(obj)).sort();
    const sortedArr2 = arr2.map((obj) => JSON.stringify(obj)).sort();

    for (let i = 0; i < sortedArr1.length; i++) {
      if (sortedArr1[i] !== sortedArr2[i]) {
        return false;
      }
    }

    return true;
  };

  const isAllChecked = () => {
    return areArraysEqual(areas, checkedAreas);
  };

  useEffect(() => {
    setAllChecked(isAllChecked());
  }, [checkedAreas]);

  return (
    <Accordion
      expanded={expanded}
      onChange={(e) => {
        if (!expanded && areas?.length === 0) {
          getAreas();
        }
        setExpanded(!expanded);
      }}
      style={{
        border: "1px solid #e0e0e0",
        borderBottom: !isLast ? "none" : "1px solid #e0e0e0",
      }}
    >
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography component="span" sx={{ width: "33%", flexShrink: 0 }}>
          {department.code} - {department.name}
        </Typography>
      </AccordionSummary>
      <AccordionDetails
        style={{
          border: "none",
        }}
      >
        {loading ? (
          <Loader />
        ) : (
          <Stack>
            {areas.length ? (
              <FormControlLabel
                control={
                  <Checkbox
                    checked={allChecked}
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                    onChange={(e) => {
                      e.stopPropagation();
                      if (isAllChecked()) {
                        setCheckedAreas([]);
                      } else {
                        setCheckedAreas(areas);
                      }
                    }}
                  />
                }
                label={allChecked ? "Tout désélectionner" : "Tout sélectionner"}
              />
            ) : null}
            <Stack gap={1} display="grid" gridTemplateColumns="1fr 1fr 1fr 1fr">
              {areas.length ? (
                areas.map((area, index) => {
                  return (
                    <Stack key={index}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={checkedAreas.find((e) => e.uuid === area.uuid) || false}
                            onChange={(e) => {
                              e.target.checked ? addArea(area) : removeArea(area);
                            }}
                          />
                        }
                        label={`${area.name}`}
                      />
                    </Stack>
                  );
                })
              ) : (
                <Typography>Aucun résultat</Typography>
              )}
            </Stack>
          </Stack>
        )}
      </AccordionDetails>
    </Accordion>
  );
};

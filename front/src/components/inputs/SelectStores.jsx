"use client";

import { useCallback, useEffect, useState } from "react";
import { Autocomplete, debounce, Stack } from "@mui/material";
import Input from "./Input";
import { useApi } from "@/context/ApiProvider";
import DIRECTIONS from "@/enums/DIRECTIONS";

export default function SelectStores({ defaultValue, onChange, required, noLabel = null }) {
  const { get } = useApi();
  const [options, setOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [value, setValue] = useState({
    value: defaultValue?.["@id"] || "",
    label: defaultValue?.name || "",
  });

  const getData = async (search) => {
    setLoading(true);
    try {
      let params = {
        "order[name]": DIRECTIONS.ASC,
      };
      if (search?.length) {
        params.q = search;
      }
      const data = await get("/agencies", params);
      const newOptions = data.member.map((option) => ({
        value: option["@id"],
        label: option.name,
      }));
      if (defaultValue && !value.value.length) {
        setValue({
          value: defaultValue["@id"],
          label: defaultValue.name,
        });
      }
      setOptions(newOptions);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = useCallback(
    debounce((val) => {
      void getData(val);
    }, 500),
    []
  );

  useEffect(() => {
    if (defaultValue && !value.value.length) {
      void getData();
    }
  }, [defaultValue]);

  return (
    <Stack width="100%">
      <Autocomplete
        onFocus={getData}
        options={options}
        loading={loading}
        value={value}
        id="select-stores"
        onInputChange={(event, newInputValue) => {
          if (event?.type !== "change") {
            handleSearch(null);
          } else {
            handleSearch(newInputValue);
          }
        }}
        onChange={(e, v, r) => {
          if (v) {
            setValue({ ...v, value: v?.value });
            onChange
              ? onChange({
                  value: v?.value,
                  label: v?.label,
                })
              : null;
          } else {
            onChange
              ? onChange({
                  value: "",
                  label: "",
                })
              : null;
          }
        }}
        renderInput={(params) => <Input label={noLabel ? null : "Cabinet"} variant="outlined" {...params} required={required} />}
      />
      <input type="hidden" name="agency" value={value?.value || ""} />
    </Stack>
  );
}

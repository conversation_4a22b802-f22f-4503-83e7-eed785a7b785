"use client";

import DeleteIcon from "@mui/icons-material/Delete";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, IconButton, Stack, styled, Typography } from "@mui/material";
import Input from "./Input";
import InputSkeleton from "./InputSkeleton";
import InputWysiwyg from "./wysiwyg/InputWysiwyg";
import useCollection from "@/hooks/useCollection";
import CardBuilder from "../superadmin/CardBuilder";
import Dom from "@/utils/dom.utils";
import TranslationUtils from "@/utils/translation.utils";

const StyledContainer = styled(Stack)(({ theme }) => ({
  border: "1px solid " + theme.palette.grey.main,
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
}));

export default function InputCard({ label, required, defaultValue, onChange }) {
  const {
    items: cards,
    setValues,
    hasError,
    create,
    remove,
    values,
  } = useCollection({
    defaultItems: defaultValue,
    requiredFields: ["title"],
    onChange,
  });

  const handleCreate = () => {
    create();
    Dom.wysiwygReset("input-card_wysiwyg");
  };

  return (
    <InputSkeleton label={label} required={required} fullWidth>
      <StyledContainer spacing={1}>
        <Stack spacing={1}>
          <Input
            label={TranslationUtils.get("blocks.blockCards.form.title")}
            value={values.title || ""}
            fullWidth
            onChange={(e) => setValues((values) => ({ ...values, title: e.target.value }))}
          />
          <InputWysiwyg
            id="input-card_wysiwyg"
            label="Texte"
            value={values.text || ""}
            onChange={(e) => setValues((values) => ({ ...values, text: e }))}
          />
          {hasError && (
            <Typography variant="body2" color="error">
              Veuillez remplir tous les champs
            </Typography>
          )}
          <Button onClick={handleCreate}>Ajouter la carte</Button>
          {cards?.length > 0 && (
            <>
              <Divider />
              {cards.map((card) => (
                <Stack key={card.id} direction="row" justifyContent="space-between" alignItems="center" spacing={1}>
                  <CardBuilder card={card} />
                  <IconButton onClick={() => remove(card.id)}>
                    <DeleteIcon />
                  </IconButton>
                </Stack>
              ))}
            </>
          )}
        </Stack>
      </StyledContainer>
    </InputSkeleton>
  );
}

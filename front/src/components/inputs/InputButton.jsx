import theme from "@/lib/theme";
import { But<PERSON>, debounce, Divider, Stack, Typography } from "@mui/material";
import React, { useCallback, useEffect, useState } from "react";
import Input from "./Input";
import InputColor from "./InputColor";

export default function InputButton({ defaultValue, onChange }) {
  const [label, setLabel] = useState(defaultValue?.label || "");
  const [link, setLink] = useState(defaultValue?.link || "");
  const [color, setColor] = useState(defaultValue?.color || theme.palette.primary.main);
  const [colorText, setColorText] = useState(defaultValue?.colorText || theme.palette.primary.contrastText);

  const handleChange = useCallback(
    debounce((f) => {
      f();
    }, 500),
    []
  );

  useEffect(() => {
    onChange ? onChange({ label, link, color, colorText }) : null;
  }, [label, link, color, colorText]);

  return (
    <Stack>
      <Typography variant="subtitle2">Bouton</Typography>
      <Stack border={`1px solid ${theme.palette.grey.dark}`} p={2} borderRadius={1} direction="row" gap={2}>
        <Stack gap={1}>
          <Input label="Label" name="label" fullWidth defaultValue={label} onChange={(e) => handleChange(() => setLabel(e.target.value))} />
          <Input label="Lien" name="link" fullWidth defaultValue={link} onChange={(e) => handleChange(() => setLink(e.target.value))} />
          <InputColor label="Couleur du fond" name="color" defaultValue={color} onChange={(val) => handleChange(() => setColor(val))} />
          <InputColor label="Couleur du texte" name="colorText" defaultValue={colorText} onChange={(val) => handleChange(() => setColorText(val))} />
        </Stack>
        <Divider orientation="vertical" flexItem />
        <Stack
          justifyContent="center"
          alignItems="center"
          width={"45%"}
          sx={{
            backgroundColor: "rgba(0, 0, 0, 0.05)",
          }}
        >
          <Button sx={{ backgroundColor: color, color: colorText }}>{label ? label : "Exemple"}</Button>
        </Stack>
      </Stack>
    </Stack>
  );
}

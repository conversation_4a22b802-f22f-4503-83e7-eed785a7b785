"use client";

import { <PERSON><PERSON><PERSON><PERSON>Tex<PERSON>, <PERSON>ack, TextField } from "@mui/material";
import InputSkeleton from "./InputSkeleton";
import { v4 as uuidv4 } from "uuid";
import { useEffect, useState } from "react";

export default function Input({ label, required, helper, ...props }) {
  const [id, setId] = useState(null);

  useEffect(() => {
    if (!id) {
      setId(uuidv4().replace(/-/g, ""));
    }
  }, []);

  return (
    <InputSkeleton id={id} label={label} required={required} fullWidth={props.fullWidth}>
        <TextField id={id} required={required} {...props} />
        {helper ? <FormHelperText>{helper}</FormHelperText> : null}
    </InputSkeleton>
  );
}

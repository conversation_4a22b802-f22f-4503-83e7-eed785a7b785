"use client";

import { useCallback, useEffect, useState } from "react";
import { Autocomplete, debounce, Stack } from "@mui/material";
import Input from "./Input";
import { useApi } from "@/context/ApiProvider";
import DIRECTIONS from "@/enums/DIRECTIONS";
import useDidMountEffect from "@/hooks/useDidMountEffect";

export default function SelectCommune({ defaultValue, onChange, required, zip, noLabel = null, allValue = false, disabled = false }) {
  const { get } = useApi();
  const [options, setOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [value, setValue] = useState({
    value: defaultValue?.["@id"] || "",
    label: defaultValue?.name || "",
  });

  useDidMountEffect(() => {
    void getData();
  }, [zip])

  const getData = async (search) => {
    setLoading(true);
    try {
      let params = {
        "order[name]": DIRECTIONS.ASC,
      };
      if (zip?.length === 5) {
        params.zip = zip;
      }
      if (search?.length) {
        params.q = search;
      }
      const data = await get("/location-cities", params);
      const newOptions = data.member.map((option) => ({
        value: option["@id"],
        label: option.name,
      }));
      if (allValue) {
        newOptions.unshift({
          value: "all",
          label: "France entière",
        });
      }
      setOptions(newOptions);
      if (defaultValue && !value.value) {
        setValue({
          value: defaultValue["@id"] || "",
          label: defaultValue.name || "",
        });
      }
      if (newOptions.length === 1) {
        const option = {
          value: newOptions[0].value,
          label: newOptions[0].label,
        }
        setValue(option);
        onChange(option)
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = useCallback(
    debounce((val) => {
      void getData(val);
    }, 500),
    []
  );

  useEffect(() => {
    if (defaultValue && !value.value) {
      void getData();
    }
  }, [defaultValue]);

  return (
    <Stack width="100%">
      <Autocomplete
        onFocus={getData}
        options={options}
        loading={loading}
        value={value}
        disabled={disabled}
        id="select-department"
        onInputChange={(event, newInputValue) => {
          if (event?.type !== "change") {
            handleSearch(null);
          } else {
            handleSearch(newInputValue);
          }
        }}
        onChange={(e, v, r) => {
          if (r === "clear") {
            setValue({ value: "", label: "" });
            onChange
              ? onChange({
                  value: "",
                  label: "",
                })
              : null;
          }
          if (v) {
            setValue(v);
            onChange
              ? onChange({
                  value: v?.value,
                  label: v?.label,
                })
              : null;
          } else {
            onChange
              ? onChange({
                  value: "",
                  label: "",
                })
              : null;
          }
        }}
        renderInput={(params) => (
          <Input variant="outlined" {...params} required={required} placeholder="Commune*" label={noLabel ? null : "Commune"} />
        )}
      />
      <input type="hidden" name="department" value={value?.value || ""} />
    </Stack>
  );
}

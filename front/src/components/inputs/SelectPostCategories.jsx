"use client";

import { useState } from "react";
import { Autocomplete, Stack } from "@mui/material";
import Input from "./Input";
import { POST_CATEGORIES } from "@/enums/POST_CATEGORIES";

export default function SelectPostCategories({ defaultValue, onChange, required, noLabel = null }) {
  const options = Object.values(POST_CATEGORIES).map((activity) => ({
    value: activity,
    label: activity,
  }));
  const [value, setValue] = useState({
    value: defaultValue || "",
    label: defaultValue || "",
  });

  return (
    <Stack width="100%">
      <Autocomplete
        options={options}
        value={value}
        id="select-post-categories"
        onChange={(e, v, r) => {
          if (r === "clear") {
            setValue({ value: "", label: "" });
            onChange
              ? onChange({
                  value: "",
                  label: "",
                })
              : null;
          }
          if (v) {
            setValue({ ...v, value: v?.value });
            onChange
              ? onChange({
                  value: v?.value,
                  label: v?.label,
                })
              : null;
          } else {
            onChange
              ? onChange({
                  value: "",
                  label: "",
                })
              : null;
          }
        }}
        renderInput={(params) => <Input variant="outlined" {...params} required={required} label={noLabel ? null : "Catégorie"} />}
      />
      <input type="hidden" name="category" value={value?.value || ""} />
    </Stack>
  );
}

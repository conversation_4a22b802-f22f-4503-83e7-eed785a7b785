"use client";

import { useState } from "react";
import { Autocomplete, Chip, Radio, Stack, Tooltip, Typography } from "@mui/material";
import Input from "./Input";
import { PRICE_ACTIVITY_TYPE } from "@/enums/PRICE_ACTIVITY_TYPE";
import TranslationUtils from "@/utils/translation.utils";
import CircleOutlinedIcon from "@mui/icons-material/CircleOutlined";
import RadioButtonCheckedIcon from "@mui/icons-material/RadioButtonChecked";

export default function SelectPriceActivities({ defaultValue, onChange, required }) {
  const options = Object.values(PRICE_ACTIVITY_TYPE)
    .filter((activity) => activity !== PRICE_ACTIVITY_TYPE.PACK)
    .map((activity) => ({
      value: activity,
      label: TranslationUtils.get(`prices.activity_type.${activity}`),
    }));
  const [value, setValue] = useState(defaultValue || []);

  return (
    <Stack width="100%">
      <Autocomplete
        options={options}
        value={value}
        id="select-price-activities"
        multiple
        disableCloseOnSelect
        renderOption={(props, option) => {
          const { key, ...optionProps } = props;
          const click = () => {
            if (value.find((v) => v.value === option.value)) {
              setValue(value.filter((v) => v.value !== option.value));
              onChange ? onChange(value.filter((v) => v.value !== option.value)) : null;
            } else {
              setValue([...value, option]);
              onChange ? onChange([...value, option]) : null;
            }
          };
          return (
            <Stack key={key} {...optionProps} direction="row" alignItems="center" gap={1} onClick={click}>
              {value.find((v) => v.value === option.value) ? <RadioButtonCheckedIcon color="secondary" /> : <CircleOutlinedIcon color="secondary" />}
              {option.label}
            </Stack>
          );
        }}
        onChange={(_, v) => {
          setValue(v);
          onChange ? onChange(v) : null;
        }}
        renderInput={(params) => <Input label={null} variant="outlined" {...params} required={required} />}
        sx={{
          "& .MuiAutocomplete-inputRoot": {
            flexWrap: "nowrap",
          },
        }}
        renderTags={(value, getTagProps) => {
          const numTags = value.length;
          return (
            <Stack direction="row" gap={1} alignItems="center">
              {value.slice(0, 1).map((option, index) => {
                const { key, ...rest } = getTagProps({ index });
                return <Chip key={key} label={option.label} {...rest} />;
              })}
              {numTags > 1 && (
                <Tooltip
                  title={value.slice(1).map((v) => (
                    <Typography key={v.value} variant="body1">
                      {v.label}
                    </Typography>
                  ))}
                >
                  <Typography variant="body1"> +{numTags - 1}</Typography>
                </Tooltip>
              )}
            </Stack>
          );
        }}
      />
      <input type="hidden" name="agency" value={value?.value || ""} />
    </Stack>
  );
}

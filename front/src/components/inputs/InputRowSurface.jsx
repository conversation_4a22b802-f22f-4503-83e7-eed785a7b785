import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, FormControl<PERSON>abel, FormGroup, Stack, styled, Switch, Typography } from "@mui/material";
import TableSurfaceComparision from "../tables/TableSurfaceComparision";
import InputSkeleton from "./InputSkeleton";
import Input from "./Input";
import useCollection from "@/hooks/useCollection";
import theme from "@/lib/theme";
import TranslationUtils from "@/utils/translation.utils";

export default function InputRowSurface({ label, required, defaultValue, onChange }) {
  const {
    items: rows,
    setValues,
    hasError,
    create,
    remove,
    values,
  } = useCollection({
    defaultItems: defaultValue,
    defaultValues: {
      label: "",
      surface_habitable: false,
      surface_privative: false,
    },
    onChange,
    requiredFields: ["label"],
  });

  return (
    <InputSkeleton fullWidth label={label} required={required}>
      <Stack gap={1} width="100%" border={"1px solid " + theme.palette.grey.main} p={2} borderRadius={1}>
        <Input
          fullWidth
          label={TranslationUtils.get("blocks.blockSurfaceComparision.form.title")}
          value={values.label || ""}
          onChange={(e) =>
            setValues((values) => ({
              ...values,
              label: e.target.value,
            }))
          }
        />
        <FormGroup>
          <FormControlLabel
            checked={values.surface_habitable}
            control={<Switch />}
            label={TranslationUtils.get("blocks.blockSurfaceComparision.header.surfaceHabitable")}
            onChange={(e) =>
              setValues((values) => ({
                ...values,
                surface_habitable: e.target.checked,
              }))
            }
          />
          <FormControlLabel
            checked={values.surface_privative}
            control={<Switch />}
            label={TranslationUtils.get("blocks.blockSurfaceComparision.header.surfacePrivative")}
            onChange={(e) =>
              setValues((values) => ({
                ...values,
                surface_privative: e.target.checked,
              }))
            }
          />
        </FormGroup>
        {hasError && (
          <Typography variant="body2" color="error">
            Veuillez remplir tous les champs
          </Typography>
        )}
        <Button onClick={create}>{TranslationUtils.get("blocks.blockSurfaceComparision.form.addRow")}</Button>
        {rows?.length > 0 && (
          <>
            <Divider />
            <TableSurfaceComparision rows={rows} remove={remove} />
          </>
        )}
      </Stack>
    </InputSkeleton>
  );
}

import { Stack, Typography } from "@mui/material";

export default function InputSkeleton({ id, label, required, fullWidth, children }) {
  return (
    <Stack width={fullWidth ? "100%" : "fit-content"}>
      {label ? (
        <Typography component="label" htmlFor={id} variant="subtitle2">
          {label} {required && "*"}
        </Typography>
      ) : null}
      {children}
    </Stack>
  );
}

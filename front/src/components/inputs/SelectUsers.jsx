import { useApi } from "@/context/ApiProvider";
import { Autocomplete, debounce, Stack, Typography } from "@mui/material";
import React, { useCallback, useEffect, useState } from "react";
import Input from "./Input";
import { v4 as uuidv4 } from "uuid";

export default function SelectUsers({ defaultValue, onChange, required }) {
  const { get } = useApi();
  const [id, setId] = useState(null);
  const [value, setValue] = useState(null);
  const [options, setOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState(null);

  const getData = async (search) => {
    setLoading(true);
    let params = {};
    if (search) {
      params.q = search;
    }
    const data = await get("/users", params);
    if (defaultValue && !value?.value) {
      const defaultData = await get(defaultValue.replace("/api", ""));
      setValue({
        value: defaultData["@id"],
        label: defaultData.lastname + " " + defaultData.firstname,
      });
    }
    const o = data.member.map((item) => ({
      value: item["@id"],
      label: item.lastname + " " + item.firstname,
    }));
    setOptions(o);
    setLoading(false);
  };

  useEffect(() => {
    if (!id) {
      setId(uuidv4().replace(/-/g, ""));
    }
    getData();
  }, [defaultValue]);

  const handleInput = useCallback(
    debounce((value) => {
      setSearch(value);
    }, 100),
    []
  );

  useEffect(() => {
    if (search?.length) {
      getData(search);
    }
  }, [search]);

  return (
    <Stack width="100%">
      <Typography component="label" htmlFor={id} variant="subtitle2">
        Utilisateur {required && "*"}
      </Typography>
      {defaultValue && !value?.value ? null : (
        <Autocomplete
          options={options}
          value={value}
          id={id}
          loading={loading}
          onInputChange={(e) => handleInput(e?.target?.value || null)}
          onChange={(_, v) => {
            if (v) {
              setValue(v);
              onChange ? onChange(v) : null;
            } else {
              onChange ? onChange(v) : null;
            }
          }}
          renderInput={(params) => <Input variant="outlined" {...params} required={required} />}
        />
      )}
      <input type="hidden" name="user" value={value?.value || ""} />
    </Stack>
  );
}

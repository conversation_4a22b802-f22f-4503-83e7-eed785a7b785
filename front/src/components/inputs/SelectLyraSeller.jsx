import { useApi } from "@/context/ApiProvider";
import { Autocomplete, debounce, Stack, Typography } from "@mui/material";
import React, { useCallback, useEffect, useState } from "react";
import Input from "./Input";
import { v4 as uuidv4 } from "uuid";

export default function SelectLyraSeller({ defaultValue, onChange }) {
  const { get } = useApi();
  const [id, setId] = useState(null);
  const [value, setValue] = useState(null);
  const [options, setOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState(null);

  const getLyraSellers = async (search) => {
    setLoading(true);
    let params = {};
    if (search) {
      params.q = search;
    }
    const data = await get("/lyra-sellers", params);
    if (defaultValue && !value?.value) {
      const defaultData = await get(defaultValue.replace("/api", ""));
      setValue({
        value: defaultData["@id"],
        label: defaultData.description,
      });
    }
    const o = data.member.map((item) => ({
      value: item["@id"],
      label: item.description,
    }));
    setOptions(o);
    setLoading(false);
  };

  useEffect(() => {
    if (!id) {
      setId(uuidv4().replace(/-/g, ""));
    }
    getLyraSellers();
  }, [defaultValue]);

  const handleInput = useCallback(
    debounce((value) => {
      setSearch(value);
    }, 100),
    []
  );

  useEffect(() => {
    if (search?.length) {
      getLyraSellers(search);
    }
  }, [search]);

  return (
    <Stack width="100%">
      <Typography component="label" htmlFor={id} variant="subtitle2">
        Lyra Seller ID
      </Typography>
      {defaultValue && !value?.value ? null : (
        <Autocomplete
          options={options}
          value={value}
          id={id}
          loading={loading}
          onInputChange={(e) => handleInput(e?.target?.value || null)}
          onChange={(_, v) => {
            if (v) {
              setValue(v);
              onChange ? onChange(v) : null;
            } else {
              onChange ? onChange(v) : null;
            }
          }}
          renderInput={(params) => <Input variant="outlined" {...params} />}
        />
      )}
      <input type="hidden" name="lyraSeller" value={value?.value || ""} />
    </Stack>
  );
}

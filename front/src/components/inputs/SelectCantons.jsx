"use client";

import { useCallback, useEffect, useState } from "react";
import { Autocomplete, debounce, Stack } from "@mui/material";
import Input from "./Input";
import { useApi } from "@/context/ApiProvider";

export default function SelectCantons({ defaultValue, onChange, required, multiple }) {
  const { get } = useApi();
  const [options, setOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [value, setValue] = useState(
    multiple
      ? []
      : {
          value: "",
          label: "",
        }
  );

  const getData = async (search) => {
    setLoading(true);
    try {
      let params = {};
      if (search?.length) {
        params.q = search;
      }
      const data = await get("/location-areas", params);
      const newOptions = data.member.map((option) => ({
        value: option["@id"],
        label: option.name,
      }));
      setOptions(newOptions);
      if (defaultValue && !search?.length) {
        if (!multiple && !value?.value?.length) {
          setValue({
            value: defaultValue["@id"],
            label: defaultValue.name,
          });
        }
        if (multiple && !value?.length) {
          setValue(newOptions.filter((option) => defaultValue.includes(option.value)));
        }
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = useCallback(
    debounce((val) => {
      void getData(val);
    }, 500),
    []
  );

  useEffect(() => {
    console.log(defaultValue, value);
    if (defaultValue && !value?.length) {
      if ((multiple && !value?.length) || (!multiple && !value?.value?.length)) {
        void getData();
      }
    }
  }, [defaultValue]);

  return (
    <Stack width="100%">
      <Autocomplete
        onFocus={getData}
        options={options}
        loading={loading}
        value={value}
        id="select-cantons"
        multiple={multiple}
        disableCloseOnSelect={multiple}
        onInputChange={(event, newInputValue) => {
          if (event?.type === "change") {
            handleSearch(newInputValue);
          }
        }}
        onChange={(e, v, r) => {
          if (v) {
            if (multiple) {
              setValue(v);
            } else {
              setValue({ ...v, value: v?.value });
            }
            if (onChange) {
              if (multiple) {
                onChange(v);
              } else {
                onChange({
                  value: v?.value,
                  label: v?.label,
                });
              }
            }
          } else {
            if (onChange) {
              if (multiple) {
                onChange([]);
              } else {
                onChange({
                  value: "",
                  label: "",
                });
              }
            }
          }
        }}
        renderInput={(params) => <Input variant="outlined" label="Canton" {...params} required={required} />}
      />
      <input type="hidden" name={multiple ? "areas" : "area"} value={multiple ? value.map((v) => v.value) : value?.value} />
    </Stack>
  );
}

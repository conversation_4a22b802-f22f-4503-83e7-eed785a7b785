"use client";

import { useState, useEffect } from "react";
import { Autocomplete, Stack, Typography } from "@mui/material";
import Input from "./Input";
import { v4 as uuidv4 } from "uuid";
import { useContent } from "@/context/ContentProvider";
import InputId from "./InputId";

export default function SelectPage({ defaultValue, onChange, anchor = null }) {
  const [id, setId] = useState(null);
  const [showAnchor, setShowAnchor] = useState(false);
  const { contents } = useContent();
  const options = contents
    .filter((option) => option.status)
    .map((option) => ({
      value: option["@id"],
      label: option.name,
      url: option.slug,
    }));
  const [value, setValue] = useState(
    defaultValue
      ? options.find((option) => option.value === defaultValue)
      : {
          value: "",
          label: "",
        }
  );

  useEffect(() => {
    if (defaultValue && defaultValue !== value?.value) {
      setValue(options.find((option) => option.value === defaultValue));
    }
  }, [defaultValue]);

  useEffect(() => {
    if (!id) {
      setId(uuidv4().replace(/-/g, ""));
    }
  }, []);

  return (
    <Stack width="100%">
      <Typography component="label" htmlFor={id} variant="subtitle2">
        Page
      </Typography>
      <Autocomplete
        options={options}
        value={value}
        id={id}
        onChange={(_, v) => {
          if (v) {
            setValue({
              ...v,
              value: v?.value,
              label: v?.label,
              url: v?.url,
            });
            onChange ? onChange({ content: v?.value }) : null;
          } else {
            onChange ? onChange({ content: "" }) : null;
          }
        }}
        renderInput={(params) => <Input variant="outlined" {...params} />}
      />
      {value?.value || anchor ? (
        <Stack alignItems="center" py={1} gap={1}>
          {!showAnchor && !anchor ? (
            <Typography variant="link" onClick={() => setShowAnchor(true)}>
              + Ajouter une ancre
            </Typography>
          ) : null}
          {showAnchor || anchor ? <InputId defaultValue={anchor} /> : null}
        </Stack>
      ) : null}

      <input type="hidden" name="content" value={value?.value || ""} />
      <input type="hidden" name="url" value={value?.url || ""} />
      <input type="hidden" name="label" value={value?.label || ""} />
    </Stack>
  );
}

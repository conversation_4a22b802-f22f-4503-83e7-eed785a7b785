"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Stack, styled, Typography } from "@mui/material";
import useCollection from "@/hooks/useCollection";
import InputSkeleton from "./InputSkeleton";
import Input from "./Input";
import InputWysiwyg from "./wysiwyg/InputWysiwyg";
import InputMedias from "./InputMedias";
import CardTestimonial from "../cards/CardTestimonial";
import Dom from "@/utils/dom.utils";
import { useState } from "react";

const StyledContainer = styled(Stack)(({ theme }) => ({
  border: "1px solid " + theme.palette.grey.main,
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
}));

export default function InputCardTestimonials({ label, required, defaultValue, onChange }) {
  const {
    items: cards,
    setValues,
    hasError,
    create,
    remove,
    values,
  } = useCollection({
    defaultItems: defaultValue,
    requiredFields: ["title"],
    onChang<PERSON>,
  });

  const [reset, setReset] = useState(false);

  const handleCreate = () => {
    create();
    Dom.wysiwygReset("input-card_wysiwyg");
    setReset(true);
  };

  return (
    <InputSkeleton label={label} required={required} fullWidth>
      <StyledContainer gap={1}>
        <Input
          label="Titre"
          value={values.title || ""}
          fullWidth
          onChange={(e) =>
            setValues((values) => ({
              ...values,
              title: e.target.value,
            }))
          }
        />
        <InputWysiwyg
          id="input-card_wysiwyg"
          label="Texte"
          value={values.text || ""}
          onChange={(e) =>
            setValues((values) => ({
              ...values,
              text: e,
            }))
          }
        />
        <InputMedias
          label="Photo"
          reset={reset}
          onChange={(val) => {
            setValues((values) => ({
              ...values,
              file: val,
            }));
            setReset(false);
          }}
        />
        {hasError && (
          <Typography variant="body2" color="error">
            Veuillez remplir tous les champs
          </Typography>
        )}
        <Button onClick={handleCreate}>Ajouter la carte</Button>
        {cards?.length > 0 && (
          <>
            <Divider />
            <Stack gap={1}>
              {cards.map((card) => (
                <CardTestimonial key={card.id} card={card} remove={() => remove(card.id)} />
              ))}
            </Stack>
          </>
        )}
      </StyledContainer>
    </InputSkeleton>
  );
}

import { Autocomplete, Stack, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { v4 as uuidv4 } from "uuid";
import Input from "./Input";
import { EMAIL_TYPES } from "@/enums/EMAIL_TYPE";
import TranslationUtils from "@/utils/translation.utils";

export default function SelectEmailsTypes({ defaultValue, required }) {
  const [id, setId] = useState(null);
  const [value, setValue] = useState(
    defaultValue
      ? {
          value: defaultValue,
          label: TranslationUtils.get(defaultValue),
        }
      : null
  );
  const options = Object.values(EMAIL_TYPES).map((value) => ({
    value: value,
    label: TranslationUtils.get(value),
  }));

  useEffect(() => {
    if (!id) {
      setId(uuidv4().replace(/-/g, ""));
    }
  }, []);

  return (
    <Stack width="100%">
      <Typography component="label" htmlFor={id} variant="subtitle2">
        Type {required && "*"}
      </Typography>
      <Autocomplete
        options={options}
        value={value}
        id={id}
        onChange={(_, v) => {
          setValue(v);
        }}
        renderInput={(params) => <Input variant="outlined" {...params} required={required} />}
      />

      <input type="hidden" name="emailType" value={value?.value || ""} />
    </Stack>
  );
}

import { Stack, TextField } from "@mui/material";
import React from "react";
import InputSkeleton from "./InputSkeleton";

export default function InputMargin({ defaultValue }) {
	return (
		<Stack direction="row" gap={2}>
			<InputSkeleton
				id="input-margin-top"
				label="Espacement supérieur"
				required={false}
				fullWidth={true}
			>
				<TextField
					id="input-margin-top"
					required={false}
					type="number"
					name="mt"
					defaultValue={defaultValue.mt}
				/>
			</InputSkeleton>
			<InputSkeleton
				id="input-margin-bottom"
				label="Espacement inférieur"
				required={false}
				fullWidth={true}
			>
				<TextField
					id="input-margin-bottom"
					required={false}
					type="number"
					name="mb"
					defaultValue={defaultValue.mb}
				/>
			</InputSkeleton>
		</Stack>
	);
}

import ArticleRoundedIcon from "@mui/icons-material/ArticleRounded";
import DeleteRoundedIcon from "@mui/icons-material/DeleteRounded";
import {
  Button,
  Divider,
  IconButton,
  Stack,
  styled,
  Table,
  TableBody,
  TableCell,
  TableRow,
  Typography,
} from "@mui/material";
import Input from "./Input";
import InputMedias from "./InputMedias";
import InputSkeleton from "./InputSkeleton";
import useCollection from "@/hooks/useCollection";

const StyledContainer = styled(Stack)(({ theme }) => ({
  border: "1px solid " + theme.palette.grey.main,
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
}));

export default function InputFileRow({
  label,
  required,
  defaultValue,
  onChange,
}) {
  const {
    items: rows,
    setValues,
    hasError,
    create,
    remove,
    values,
  } = useCollection({
    defaultItems: defaultValue,
    onChange,
    requiredFields: ["label"],
  });

  return (
    <InputSkeleton label={label} required={required} fullWidth>
      <StyledContainer spacing={1}>
        <Stack spacing={1}>
          <Input
            label="Libelle"
            value={values.label || ""}
            fullWidth
            onChange={(e) =>
              setValues((values) => ({
                ...values,
                label: e?.target?.value,
              }))
            }
          />
          <InputMedias
            label="Fichier"
            value={values.file || ""}
            fullWidth
            onChange={(e) =>
              setValues((values) => ({
                ...values,
                file: e.target.value,
              }))
            }
          />
          {hasError && (
            <Typography variant="body2" color="error">
              Veuillez remplir tous les champs
            </Typography>
          )}
          <Button onClick={create}>Ajouter la ligne</Button>
          {rows?.length > 0 && (
            <>
              <Divider />
              <Table>
                <TableBody>
                  {rows.map((row) => (
                    <TableRow key={row.id} direction="row" spacing={1}>
                      <TableCell>
                        <Stack direction="row" spacing={1}>
                          <ArticleRoundedIcon />
                          <Typography>{row.label}</Typography>
                        </Stack>
                      </TableCell>
                      <TableCell align="right">
                        <IconButton onClick={() => remove(row.id)}>
                          <DeleteRoundedIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </>
          )}
        </Stack>
      </StyledContainer>
    </InputSkeleton>
  );
}

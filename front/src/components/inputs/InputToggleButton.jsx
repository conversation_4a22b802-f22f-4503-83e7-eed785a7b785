import { styled } from "@mui/material";
import theme from "@/lib/theme";

const DISCOUNT_TYPE = {
  PERCENT: "percent",
  FIXED: "fixed",
};

const Container = styled("div")({
  position: "relative",
  display: "flex",
  width: "250px",
  backgroundColor: "#fff",
  border: `1px solid ${theme.palette.secondary.main}`,
  borderRadius: "30px",
  overflow: "hidden",
});

const Option = styled("label")({
  flex: 1,
  textAlign: "center",
  cursor: "pointer",
  zIndex: 1,
  padding: "10px 0",
  transition: "color 0.3s ease",
  fontSize: "14px",
  fontWeight: "bold",
});

const Slider = styled("div")({
  position: "absolute",
  top: 2,
  bottom: 2,
  width: "50%",
  backgroundColor: theme.palette.secondary.main,
  borderRadius: "30px",
  transition: "transform 0.3s ease",
});

export default function InputToggleButton({ value, onChange }) {
  return (
    <Container>
      <Option
        sx={{ color: value === DISCOUNT_TYPE.PERCENT ? theme.palette.white : theme.palette.secondary.main }}
        onClick={() => onChange(DISCOUNT_TYPE.PERCENT)}
      >
        Pourcentage
      </Option>
      <Option
        sx={{ color: value === DISCOUNT_TYPE.FIXED ? theme.palette.white : theme.palette.secondary.main }}
        onClick={() => onChange(DISCOUNT_TYPE.FIXED)}
      >
        Valeur fixe
      </Option>
      <Slider
        style={{
          transform: value === DISCOUNT_TYPE.FIXED ? "translateX(100%)" : "translateX(0)",
          left: value === DISCOUNT_TYPE.FIXED ? "-2px" : "2px",
        }}
      ></Slider>
    </Container>
  );
}

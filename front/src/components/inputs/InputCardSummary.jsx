"use client";

import DeleteIcon from "@mui/icons-material/Delete";
import { <PERSON><PERSON>, Divider, IconButton, Paper, Stack, styled, Typography } from "@mui/material";
import Input from "./Input";
import InputSkeleton from "./InputSkeleton";
import useCollection from "@/hooks/useCollection";
import TranslationUtils from "@/utils/translation.utils";
import InputMedias from "./InputMedias";
import Image from "next/image";
import FormatUtils from "@/utils/format.utils";
const StyledContainer = styled(Stack)(({ theme }) => ({
  border: "1px solid " + theme.palette.grey.main,
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
}));

export default function InputCardSummary({ label, required, defaultValue, onChange }) {
  const {
    items: cards,
    setValues,
    hasError,
    create,
    remove,
    values,
  } = useCollection({
    defaultItems: defaultValue,
    requiredFields: ["title", "image"],
    onChange,
  });

  const handleCreate = () => {
    create();
  };

  return (
    <InputSkeleton label={label} required={required} fullWidth>
      <StyledContainer spacing={1}>
        <Stack spacing={1}>
          <InputMedias label="Image" defaultValue={values.image} onChange={(e) => setValues((values) => ({ ...values, image: e }))} />
          <Input
            label={TranslationUtils.get("blocks.blockCards.form.title")}
            value={values.title || ""}
            fullWidth
            onChange={(e) => setValues((values) => ({ ...values, title: e.target.value }))}
          />
          <Input
            label="Lien"
            value={values.link || ""}
            fullWidth
            onChange={(e) => setValues((values) => ({ ...values, link: e.target.value }))}
          />
          {hasError && (
            <Typography variant="body2" color="error">
              Veuillez remplir tous les champs
            </Typography>
          )}
          <Button onClick={handleCreate}>Ajouter la carte</Button>
          {cards?.length > 0 && (
            <>
              <Divider />
              {cards.map((card) => (
                <Stack key={card.id} direction="row" justifyContent="space-between" alignItems="center" spacing={1}>
                  <Stack component={Paper} gap={1} justifyContent="center" alignItems="center" direction="row">
                    <Stack
                      borderRadius={10}
                      overflow="hidden"
                      width={55}
                      height={55}
                      bgcolor="primary.main"
                      justifyContent="center"
                      alignItems="center"
                    >
                      <Image src={FormatUtils.image(card?.image?.["@id"])} alt={card?.image?.alt} width={25} height={25} objectFit="contain" />
                    </Stack>
                    <Typography variant="h3">{card.title}</Typography>
                  </Stack>
                  <IconButton onClick={() => remove(card.id)}>
                    <DeleteIcon />
                  </IconButton>
                </Stack>
              ))}
            </>
          )}
        </Stack>
      </StyledContainer>
    </InputSkeleton>
  );
}

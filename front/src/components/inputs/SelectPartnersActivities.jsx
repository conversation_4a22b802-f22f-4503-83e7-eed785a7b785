"use client";

import { useEffect, useState } from "react";
import { Autocomplete, Stack } from "@mui/material";
import Input from "./Input";
import { PARTNERS_ACTIVITIES } from "@/enums/PARTNERS_ACTIVITIES";

export default function SelectPartnersActivities({ defaultValue, onChange, required, noLabel = null }) {
  const options = Object.values(PARTNERS_ACTIVITIES).map((activity) => ({
    value: activity,
    label: activity,
  }));

  const [value, setValue] = useState({
    value: "",
    label: "",
  });

  useEffect(() => {
    if (defaultValue && !value.value.length) {
      setValue(options.find((option) => option.value === defaultValue));
    }
  }, [defaultValue]);

  return (
    <Stack width="100%">
      <Autocomplete
        options={options}
        value={value}
        id="select-partner-activities"
        onChange={(e, v, r) => {
          if (r === "clear") {
            setValue({ value: "", label: "" });
            onChange
              ? onChange({
                  value: "",
                  label: "",
                })
              : null;
          }
          if (v) {
            setValue({ ...v, value: v?.value });
            onChange
              ? onChange({
                  value: v?.value,
                  label: v?.label,
                })
              : null;
          } else {
            onChange
              ? onChange({
                  value: "",
                  label: "",
                })
              : null;
          }
        }}
        renderInput={(params) => <Input variant="outlined" {...params} required={required} label={noLabel ? null : "Activités"} />}
      />
      <input type="hidden" name="activity" value={value?.value || ""} />
    </Stack>
  );
}

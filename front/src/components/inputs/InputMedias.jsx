"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Typo<PERSON> } from "@mui/material";
import AddPhotoAlternateIcon from "@mui/icons-material/AddPhotoAlternate";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import theme from "@/lib/theme";
import { useEffect, useRef, useState } from "react";
import { useApi } from "@/context/ApiProvider";
import Image from "next/image";
import TranslationUtils from "@/utils/translation.utils";
import Input from "./Input";
import { useSnack } from "@/context/SnackProvider";

export default function InputMedias({ label, onChange, defaultValue, small, name, reset, required, ...props }) {
  const ref = useRef(null);
  const { getToken, put, get } = useApi();
  const { add } = useSnack();
  const [upload, setUpload] = useState(null);

  const getMedia = async () => {
    try {
      if (!defaultValue?.["@id"] && defaultValue) {
        const uuid = defaultValue.split("/").pop();
        const media = await get("/uploads/" + uuid);
        setUpload(media);
      } else {
        setUpload(defaultValue);
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    getMedia();
  }, [defaultValue]);

  useEffect(() => {
    if (reset) {
      setUpload(null);
    }
  }, [reset]);

  const uploadImage = (file) => {
    let req = new XMLHttpRequest();

    return new Promise((resolve, reject) => {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("alt", upload?.alt || file.name);
      req.open("POST", `${process.env.NEXT_PUBLIC_API_URL}/api/uploads`);
      req.setRequestHeader("X-Sinfin-Token", getToken());
      req.onreadystatechange = (e) => {
        if (req.readyState !== 4) {
          return;
        }

        if (req.status === 201) {
          resolve(JSON.parse(req.responseText));
        }
      };

      req.send(formData);
    });
  };

  const handleChange = async (e) => {
    try {
      const image = await uploadImage(e.target.files[0]);
      setUpload(image);
      onChange ? onChange(image) : null;
      ref.current.value = "";
    } catch (error) {
      console.log(error);
    }
  };

  const changeAlt = async () => {
    try {
      await put(`/uploads/${upload.uuid}`, upload);
      add("success", "Le texte alternatif a bien été modifié !");
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <Stack>
      <Typography component="label" variant="subtitle2">
        {label} {required ? "*" : ""}
      </Typography>
      <Stack {...props} border={"1px dashed " + theme.palette.grey[300]} p={2} alignItems="center">
        {upload ? (
          <Stack>
            <Stack bgcolor="grey.light" p={2}>
              <Image
                src={`${process.env.NEXT_PUBLIC_API_URL}${upload["@id"] || upload}/binary`}
                alt={upload?.alt}
                width={small ? 150 : 300}
                height={small ? 150 : 200}
                style={{
                  objectFit: "contain",
                }}
              />
            </Stack>
            <Stack width="100%" height="100%" justifyContent="center" alignItems="center" direction="row" gap={1} mt={2}>
              <IconButton onClick={() => ref.current.click()} sx={{ padding: 1 }}>
                <EditIcon />
              </IconButton>
              <IconButton onClick={() => setUpload(null)} sx={{ padding: 1 }}>
                <DeleteIcon />
              </IconButton>
            </Stack>
            <Stack direction="row" gap={2} alignItems="flex-end">
              <Input
                label={"Texte alternatif"}
                name="alt"
                value={upload?.alt}
                onChange={(e) => setUpload((upload) => ({ ...upload, alt: e.target.value }))}
                fullWidth
                required={required}
              />
              <Button onClick={() => changeAlt()}>{TranslationUtils.get("global.save")}</Button>
            </Stack>
          </Stack>
        ) : (
          <>
            <IconButton sx={{ padding: 1 }} onClick={() => ref.current.click()}>
              <AddPhotoAlternateIcon style={{ cursor: "pointer", fontSize: 40 }} />
            </IconButton>
            <Typography variant="subtitle2">{TranslationUtils.get("inputs.upload.add")}</Typography>
          </>
        )}
        <input type="file" style={{ display: "none" }} ref={ref} onChange={handleChange} {...props} />
        <input type="text" name="upload" style={{ display: "none" }} value={upload?.["@id"] || ""} readOnly />
        {name ? <input required={required} type="text" name={name} style={{ display: "none" }} value={upload?.["@id"] || ""} readOnly /> : null}
      </Stack>
    </Stack>
  );
}

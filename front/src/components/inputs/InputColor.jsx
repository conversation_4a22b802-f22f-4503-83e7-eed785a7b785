"use client";

import theme from "@/lib/theme";
import { <PERSON>, Button, Divider, Stack, styled, TextField, Typography } from "@mui/material";
import { red } from "@mui/material/colors";
import { useEffect, useRef, useState } from "react";
import { v4 as uuidv4 } from "uuid";
import CloseIcon from "@mui/icons-material/Close";

const StyledColorBox = styled(Box, {
  shouldForwardProp: (prop) => prop !== "color",
})(({ theme, color }) => ({
  width: 30,
  height: 30,
  borderRadius: theme.shape.borderRadius,
  cursor: "pointer",
  backgroundColor: color,
  border: `1px solid ${theme.palette.divider}`,
}));

const StyledColorBoxEmpty = styled(Box, {
  shouldForwardProp: (prop) => prop !== "color",
})(({ theme, color }) => ({
  width: 30,
  height: 30,
  borderRadius: theme.shape.borderRadius,
  cursor: "pointer",
  // backgroundColor: "#000",
  border: `1px solid ${theme.palette.divider}`,
  "&::before": {
    content: "",
    position: "absolute",
    backgroundColor: "red",
  },
  "&::after": {
    content: "",
    position: "absolute",
    backgroundColor: "red",
  },
  "&::before": {
    width: "80%",
    height: "10px",
  },
  "&::after": {
    height: "80%",
    width: "10px",
  },
}));

const StyledMainColorBox = styled(StyledColorBox)(() => ({
  width: 40,
  height: 40,
}));

const StyledInputContainer = styled(Stack)(({ theme }) => ({
  border: `1px solid ${theme.palette.grey.main}`,
  padding: theme.spacing(1),
  borderRadius: theme.shape.borderRadius,
  backgroundColor: theme.palette.white,
}));

export default function InputColor({ label, name = "color", defaultValue, onChange, close }) {
  const id = uuidv4();
  const [value, setValue] = useState(defaultValue || theme.palette.primary.main);
  const inputRef = useRef(null);

  const favColors = ["#FFFFFF", "#000000", theme.palette.primary.main, theme.palette.secondary.main, null];

  useEffect(() => {
    onChange ? onChange(value) : null;
  }, [value]);

  return (
    <Stack spacing={1} width="100%">
      <Typography component="label" htmlFor={id} variant="subtitle2">
        {label}
      </Typography>
      <StyledInputContainer spacing={1}>
        <Stack direction="row" spacing={1} alignItems="center">
          <StyledMainColorBox color={value} onClick={() => inputRef.current.click()} />
          <Box width="100%" position="relative">
            <TextField placeholder="Code hex" value={value} onChange={(e) => setValue(e.target?.value)} fullWidth />
            <input
              name={name}
              id={id}
              ref={inputRef}
              type="color"
              value={value || "#FFFFF"}
              style={{
                opacity: 0,
                position: "absolute",
                left: 0
              }}
              onChange={(e) => setValue(e.target?.value)}
            />
          </Box>
        </Stack>
        <Divider />
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Stack direction="row" gap={1} flexGrow={1}>
            {favColors.map((color, i) =>
              !color ? (
                <StyledColorBoxEmpty key={`${color}-${i}`} color={color} onClick={() => setValue("")}>
                  <CloseIcon
                    sx={{
                      fontSize: "29px",
                    }}
                  />
                </StyledColorBoxEmpty>
              ) : (
                <StyledColorBox key={`${color}-${i}`} color={color} onClick={() => setValue(color)} />
              )
            )}
          </Stack>
          {close && <Button onClick={close}>Close</Button>}
        </Stack>
      </StyledInputContainer>
    </Stack>
  );
}

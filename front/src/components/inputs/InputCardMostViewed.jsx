"use client";

import useCollection from "@/hooks/useCollection";
import { <PERSON><PERSON>, <PERSON>vider, Stack, styled, Typography } from "@mui/material";
import CardMostViewed from "@/components/cards/CardMostViewed";
import InputMedias from "@/components/inputs/InputMedias";
import InputSkeleton from "@/components/inputs/InputSkeleton";
import Input from "./Input";
import { useState } from "react";

const StyledContainer = styled(Stack)(({ theme }) => ({
  border: "1px solid " + theme.palette.grey.main,
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
}));

export default function InputCardMostViewed({ label, required, defaultValue, onChange }) {
  const {
    items: cards,
    setValues,
    hasError,
    create,
    remove,
    values,
  } = useCollection({
    defaultItems: defaultValue,
    requiredFields: ["text"],
    onChange,
  });
  const [reset, setReset] = useState(false);

  const handleCreate = () => {
    create();
    setReset(true);
  };

  return (
    <InputSkeleton label={label} required={required} fullWidth>
      <StyledContainer gap={1}>
        <Input
          fullWidth
          label="Texte"
          name="text"
          value={values.text || ""}
          onChange={(e) =>
            setValues((values) => ({
              ...values,
              text: e.target.value,
            }))
          }
        />
        <InputMedias
          label="Fond"
          name="file"
          reset={reset}
          onChange={(val) => {
            setValues((values) => ({
              ...values,
              file: val,
            }));
            setReset(false);
          }}
        />
        {hasError && (
          <Typography variant="body2" color="error">
            Veuillez remplir tous les champs
          </Typography>
        )}
        <Button onClick={handleCreate}>Ajouter la carte</Button>
        {cards?.length > 0 && (
          <>
            <Divider />
            <Stack direction="row" gap={1} flexWrap="wrap">
              {cards.map((card, i) => (
                <CardMostViewed key={i} fullWidth item={card} remove={() => remove(card.id)} />
              ))}
            </Stack>
          </>
        )}
      </StyledContainer>
    </InputSkeleton>
  );
}

import React from "react";
import {
	FormControlLabel,
	Radio,
	RadioGroup,
	Stack,
	Typography,
} from "@mui/material";

export default function InputAlignment({ defaultValue }) {
	return (
		<Stack>
			<Typography
				component="label"
				htmlFor="input-alignment"
				variant="subtitle2"
			>
				Position du média
			</Typography>
			<RadioGroup name="alignment" defaultValue={defaultValue}>
				<Stack direction="row" gap={2}>
					<FormControlLabel
						value="left"
						control={<Radio />}
						label="Gauche"
					/>
					<FormControlLabel
						value="right"
						control={<Radio />}
						label="Droite"
					/>
				</Stack>
			</RadioGroup>
		</Stack>
	);
}

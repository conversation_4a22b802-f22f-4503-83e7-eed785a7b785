"use client";

import InputSkeleton from "./InputSkeleton";
import Input from "./Input";
import { Button, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, Stack, Typography } from "@mui/material";
import theme from "@/lib/theme";
import { useState } from "react";
import InputWysiwyg from "./wysiwyg/InputWysiwyg";

export default function InputTable({ values, setValues }) {
  const [current, setCurrent] = useState({});
  const [selectedRow, setSelectedRow] = useState(null);
  const [isOpen, setIsOpen] = useState(false);

  const submit = () => {
    if (selectedRow !== null) {
      const newValues = [...values];
      newValues[selectedRow] = current;
      setValues(newValues);
      setSelectedRow(null);
      setCurrent({});
      return;
    }

    setValues((values) => [...values, current]);
    setCurrent({});
  };

  const deleteRow = () => {
    setValues((values) => values.filter((_, i) => i !== selectedRow));
    setSelectedRow(null);
    setCurrent({});
  };

  const handleClose = () => {
    setIsOpen(false);
  };

  const Cell = ({ children }) => (
    <Typography
      textAlign="center"
      textOverflow="ellipsis"
      overflow="hidden"
      whiteSpace="nowrap"
      px={1}
      minHeight={theme.spacing(2)}
      sx={{
        borderRight: "1px solid #ccc",
        "&:last-child": {
          borderRight: "none",
        },
      }}
    >
      {children}
    </Typography>
  );

  return (
    <InputSkeleton label="Élément du tableau" fullWidth>
      <Stack border={`1px solid ${theme.palette.grey.main}`} p={2} borderRadius={1} gap={2}>
        <Input
          label="Prestations"
          defaultValue={current?.title || ""}
          fullWidth
          onChange={(e) => setCurrent((current) => ({ ...current, title: e.target.value }))}
          autoFocus
          key={`title-${current?.title}`}
        />
        <Input
          label="T/L"
          defaultValue={current?.transaction || ""}
          fullWidth
          onChange={(e) => setCurrent((current) => ({ ...current, transaction: e.target.value }))}
          key={`transaction-${current?.title}`}
        />

        <InputWysiwyg
          key={`types-${current?.title}`}
          label="Types de locaux"
          defaultValue={current?.types || ""}
          onChange={(value) => setCurrent((current) => ({ ...current, types: value }))}
        />
        <InputWysiwyg
          key={`validity-${current?.title}`}
          label="Validité"
          defaultValue={current?.validity || ""}
          onChange={(value) => setCurrent((current) => ({ ...current, validity: value }))}
        />
        <Stack direction="row" justifyContent="space-between" gap={1}>
          {selectedRow !== null ? (
            <>
              <Button color="error" variant="outlined" onClick={() => setIsOpen(true)} fullWidth>
                Supprimer
              </Button>
              <Button
                variant="outlined"
                onClick={() => {
                  setSelectedRow(null);
                  setCurrent(null);
                }}
                fullWidth
              >
                Annuler
              </Button>
            </>
          ) : null}
          <Button onClick={submit} fullWidth>
            {selectedRow !== null ? "Modifier" : "Ajouter"}
          </Button>
        </Stack>

        <Stack>
          {values.length > 0 && (
            <Stack
              display="grid"
              gridTemplateColumns="1fr 1fr 1fr 1fr"
              bgcolor="red"
              color="white"
              px={0.5}
              sx={{ borderTopLeftRadius: 5, borderTopRightRadius: 5 }}
            >
              <Typography textAlign="center">Prestations</Typography>
              <Typography textAlign="center">T/L</Typography>
              <Typography textAlign="center">Types de locaux</Typography>
              <Typography textAlign="center">Validité</Typography>
            </Stack>
          )}
          {values.map((value, index) => (
            <Stack
              key={index}
              display="grid"
              gridTemplateColumns="1fr 1fr 1fr 1fr"
              border="1px solid #ccc"
              borderBottom={index === values.length - 1 ? "1px solid #ccc" : "none"}
              sx={{
                borderBottomLeftRadius: index === values.length - 1 ? 5 : 0,
                borderBottomRightRadius: index === values.length - 1 ? 5 : 0,
                "&:hover": { backgroundColor: "#eee", cursor: "pointer" },
              }}
              onClick={() => {
                setCurrent(value);
                setSelectedRow(index);
              }}
            >
              <Cell>{value.title}</Cell>
              <Cell>{value.transaction}</Cell>
              <Cell>
                <Typography variant="body1" dangerouslySetInnerHTML={{ __html: value.types }} />
              </Cell>
              <Cell>
                <Typography variant="body1" dangerouslySetInnerHTML={{ __html: value.validity }} />
              </Cell>
            </Stack>
          ))}
        </Stack>
      </Stack>

      <Dialog open={isOpen} onClose={handleClose} aria-labelledby="alert-dialog-title" aria-describedby="alert-dialog-description">
        <DialogTitle id="alert-dialog-title">Êtes-vous sûr de vouloir supprimer cette ligne ?</DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            <Stack direction="row">
              <Stack display="grid" gridTemplateColumns="1fr 1fr 1fr 1fr" border="1px solid #ccc" borderRadius={1}>
                <Cell>{current?.title}</Cell>
                <Cell>{current?.transaction}</Cell>
                <Cell>{current?.types}</Cell>
                <Cell>{current?.validity}</Cell>
              </Stack>
            </Stack>
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button variant="outlined" onClick={handleClose}>
            Non
          </Button>
          <Button
            onClick={() => {
              deleteRow();
              handleClose();
            }}
            autoFocus
          >
            Oui
          </Button>
        </DialogActions>
      </Dialog>
    </InputSkeleton>
  );
}

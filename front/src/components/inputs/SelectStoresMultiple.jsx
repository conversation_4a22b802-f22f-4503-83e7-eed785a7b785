"use client";

import { useCallback, useEffect, useState } from "react";
import { Autocomplete, debounce, Stack } from "@mui/material";
import Input from "./Input";
import { useApi } from "@/context/ApiProvider";
import DIRECTIONS from "@/enums/DIRECTIONS";

export default function SelectStoresMultiple({ defaultValues, values, onChange, required, noLabel = null }) {
  const { get } = useApi();
  const [options, setOptions] = useState(defaultValues || []);
  const [loading, setLoading] = useState(false);

  const getData = async (search) => {
    setLoading(true);
    try {
      const params = {
        "order[name]": DIRECTIONS.ASC,
        pagination: false,
      };
      if (search?.length) {
        params.q = search;
      }
      const data = await get("/agencies", params);
      setOptions(data.member.map((a) => ({ ...a, key: a["@id"] })));
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = useCallback(
    debounce((val) => {
      void getData(val);
    }, 500),
    []
  );

  useEffect(() => {
    if (defaultValues?.length) {
      getData();
    }
  }, [values]);

  return (
    <Stack width="100%">
      <Autocomplete
        multiple
        onFocus={!defaultValues?.length ? () => getData() : null}
        options={options}
        disabled={defaultValues?.length && loading}
        getOptionLabel={(option) => {
          if (typeof option === "string") {
            return options.find((v) => v["@id"] === option)?.name;
          }
          return option?.name || "";
        }}
        getOptionDisabled={(option) => values.map((v) => v).includes(option["@id"])}
        loading={loading}
        value={values}
        id="select-stores"
        onInputChange={(event, newInputValue) => {
          if (event?.type !== "change") {
            handleSearch(null);
          } else {
            handleSearch(newInputValue);
          }
        }}
        onChange={(e, newValues) => {
          if (onChange) onChange(newValues.map((v) => (v?.["@id"] ? v?.["@id"] : v)));
        }}
        renderInput={(params) => <Input label={noLabel ? null : "Cabinet"} variant="outlined" {...params} required={required} />}
      />
    </Stack>
  );
}

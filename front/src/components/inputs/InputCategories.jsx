"use client";
import useCollection from "@/hooks/useCollection";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>rid2, I<PERSON><PERSON>utton, Stack, styled, Typography } from "@mui/material";
import Input from "@/components/inputs/Input";
import InputSkeleton from "@/components/inputs/InputSkeleton";
import CloseIcon from "@mui/icons-material/Close";

const StyledContainer = styled(Stack)(({ theme }) => ({
  border: "1px solid " + theme.palette.grey.main,
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
}));

export default function InputCategories({ label, required, defaultValue, onChange }) {
  const {
    items: categories,
    setValues,
    hasError,
    create,
    update,
    remove,
    values,
  } = useCollection({
    defaultItems: defaultValue,
    onChange,
    requiredFields: ["label"],
  });

  return (
    <InputSkeleton label={label} required={required} fullWidth>
      <StyledContainer spacing={1}>
        <Stack spacing={1}>
          <Grid2 container spacing={1}>
            <Grid2
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Input
                label="Libelle"
                value={values.label || ""}
                onChange={(e) =>
                  setValues((values) => ({
                    ...values,
                    label: e.target.value,
                  }))
                }
                fullWidth
              />
            </Grid2>
            <Grid2
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Input
                label="Lien"
                value={values.link || ""}
                onChange={(e) =>
                  setValues((values) => ({
                    ...values,
                    link: e.target.value,
                  }))
                }
                fullWidth
              />
            </Grid2>
          </Grid2>
          {hasError && (
            <Typography variant="body2" color="error">
              Veuillez remplir tous les champs
            </Typography>
          )}
          <Button
            onClick={
              values?.id
                ? () => {
                    update(values.id);
                    setValues({});
                  }
                : create
            }
          >
            {values?.id ? "Modifier" : "Ajouter"} la catégorie
          </Button>
          {categories?.length > 0 && (
            <>
              <Divider />
              <Stack gap={1} direction="row" flexWrap="wrap">
                {categories.map((c, i) => (
                  <Button key={i} variant="contained" color="primary" component={Stack} direction="row" gap={1} onClick={() => setValues(c)}>
                    {c.label}
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        remove(c.id);
                      }}
                    >
                      <CloseIcon fontSize="small" />
                    </IconButton>
                  </Button>
                ))}
              </Stack>
            </>
          )}
        </Stack>
      </StyledContainer>
    </InputSkeleton>
  );
}

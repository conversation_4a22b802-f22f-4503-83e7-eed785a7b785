import { Node } from "@tiptap/core";
import { ReactNodeViewRenderer } from "@tiptap/react";
import IframeView from "./IframeView";

export default Node.create({
  name: "iframe",
  group: "block",
  atom: true,

  addOptions() {
    return {};
  },

  addAttributes() {
    return {
      width: {
        parseHTML: (element) => {
          const width = element.style.width || element.getAttribute("width") || null;
          return width == null ? null : Number.parseInt(width, 10);
        },
        renderHTML: (attributes) => {
          return {
            width: attributes.width,
          };
        },
      },
      height: {
        parseHTML: (element) => {
          const height = element.style.height || element.getAttribute("height") || null;
          return height == null ? null : Number.parseInt(height, 10);
        },
        renderHTML: (attributes) => {
          return {
            height: attributes.height,
          };
        },
      },
      src: {
        default: null,
      },
      frameborder: {
        default: 0,
      },
      allowfullscreen: {
        default: this.options.allowFullscreen,
        parseHTML: () => this.options.allowFullscreen,
      },
      parentStyle: {
        default: null,
        parseHTML: (element) => element.parentElement.getAttribute("style"),
        renderHTML: (attributes) => ({ "data-parent-style": attributes.parentStyle }),
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: "iframe",
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    const getStyleParent = (iframeSrc) => {
      const parser = new DOMParser();
      const doc = parser.parseFromString(this.editor.options.content, "text/html");

      let parentStyle = null;

      doc.querySelectorAll("div > iframe").forEach((iframe) => {
        if (iframe.src === iframeSrc) {
          const parentDiv = iframe.parentElement;
          parentStyle = parentDiv.getAttribute("style");
          return;
        }
      });

      return parentStyle;
    };

    const iframeSrc = HTMLAttributes.src;

    return [
      "div",
      {
        class: "iframe-wrapper",
        contenteditable: true,
        style: HTMLAttributes["data-parent-style"] || getStyleParent(iframeSrc),
      },
      ["iframe", HTMLAttributes],
    ];
  },

  addNodeView() {
    return ReactNodeViewRenderer(IframeView);
  },

  addCommands() {
    return {
      setIframe:
        (options) =>
        ({ tr, dispatch }) => {
          const { selection } = tr;
          const node = this.type.create(options);

          if (dispatch) {
            tr.replaceRangeWith(selection.from, selection.to, node);
          }

          return true;
        },
      updateIframeStyle:
        (options) =>
        ({ tr, state, dispatch }) => {
          const { selection } = state;
          const { textAlign } = options;

          state.doc.nodesBetween(selection.from, selection.to, (node, pos) => {
            if (node.type.name === "iframe") {
              if (dispatch) {
                tr.setNodeMarkup(pos, null, {
                  ...node.attrs,
                  parentStyle: `text-align: ${textAlign};`,
                });
              }
              return false; // Stop searching
            }
          });

          return dispatch ? tr : true;
        },
    };
  },
});

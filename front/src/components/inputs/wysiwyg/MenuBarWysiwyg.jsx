import useMedia from "@/hooks/useMedia";
import InputColor from "@/components/inputs/InputColor";
import theme from "@/lib/theme";
import FormatColorTextIcon from "@mui/icons-material/FormatColorText";
import LinkIcon from "@mui/icons-material/Link";
import RttIcon from "@mui/icons-material/Rtt";
import {
  Box,
  Button,
  Divider,
  FormControlLabel,
  ListItemIcon,
  ListItemText,
  Menu,
  MenuItem,
  Popover,
  Stack,
  Switch,
  ToggleButton,
  ToggleButtonGroup,
} from "@mui/material";
import { createElement, useCallback, useRef, useState } from "react";
import Input from "../Input";
import { ACTIONS, ALIGNS, HEADINGS, LISTS, MARKS, OTHERS } from "./WYSIWYG_OPTIONS";
import FormatUtils from "@/utils/format.utils";
import { getYouTubeEmbedUrl } from "@/components/ImageOrVideo";

export default function MenuBarWysiwyg({ editor }) {
  const [anchorElPopover, setAnchorElPopover] = useState(null);
  const [anchorEl, setAnchorEl] = useState(null);
  const [anchorElLink, setAnchorElLink] = useState(null);
  const [anchorElIframe, setAnchorElIframe] = useState(null);
  const imageInputRef = useRef(null);
  const { uploadImage } = useMedia();

  const handleClickPopover = (event) => {
    setAnchorElPopover(event.currentTarget);
  };

  const handleClosePopover = () => {
    setAnchorElPopover(null);
  };

  const addImage = useCallback(
    async (e) => {
      try {
        const image = await uploadImage(e.target.files[0]);
        if (image?.["@id"]) {
          const url = FormatUtils.binary(image["@id"]);
          editor.chain().focus().setImage({ src: url }).run();
          e.target.value = "";
        }
      } catch (error) {
        console.log(error);
      }
    },
    [editor]
  );

  return (
    <Stack direction="row" gap={1} flexWrap="wrap" className="button-group">
      <ToggleButtonGroup size="small" color="primary">
        {MARKS.map((mark) => {
          return (
            <ToggleButton
              key={mark.name}
              value={mark.name}
              aria-label={mark.name}
              onClick={() => mark.toggle(editor)}
              selected={editor.isActive(mark.name)}
            >
              {createElement(mark.icon, { fontSize: "small" })}
            </ToggleButton>
          );
        })}
        <ToggleButton
          variant="contained"
          value="textColor"
          onClick={handleClickPopover}
          selected={editor.isActive("textStyle", {
            color: editor.getAttributes("textStyle").color,
          })}
        >
          <FormatColorTextIcon fontSize="small" sx={{ color: editor.getAttributes("textStyle").color }} />
        </ToggleButton>
      </ToggleButtonGroup>
      <Popover
        id="color"
        open={!!anchorElPopover}
        anchorEl={anchorElPopover}
        onClose={handleClosePopover}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        slotProps={{
          paper: { sx: { border: 0 } },
        }}
      >
        <Box sx={{ width: "450px" }}>
          <InputColor
            defaultValue={editor.getAttributes("textStyle").color}
            onChange={(value) => editor.commands.setColor(value)}
            close={handleClosePopover}
          />
        </Box>
      </Popover>
      {/* MENU */}
      <ToggleButtonGroup size="small" color="primary">
        {ALIGNS.map((align) => {
          return (
            <ToggleButton
              key={align.name}
              value={align.name}
              aria-label={align.name}
              onClick={() => align.toggle(editor)}
              selected={editor.isActive({
                textAlign: align.name,
              })}
            >
              {createElement(align.icon, { fontSize: "small" })}
            </ToggleButton>
          );
        })}
        <ToggleButton value="menu" aria-label="menu" sx={{ width: "45px" }} onClick={(e) => setAnchorEl(e.currentTarget)}>
          <RttIcon />
        </ToggleButton>
      </ToggleButtonGroup>
      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={!!anchorEl}
        onClose={() => setAnchorEl(null)}
        MenuListProps={{
          "aria-labelledby": "basic-button",
          dense: true,
          style: { padding: 0 },
        }}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
      >
        {LISTS.map((list) => {
          return (
            <MenuItem
              key={list.name}
              onClick={() => {
                list.toggle(editor);
              }}
              selected={editor.isActive(list.name)}
              sx={{
                backgroundColor: editor.isActive(list.name) ? `${theme.palette.primary.lighter} !important` : "",
              }}
            >
              <ListItemIcon
                sx={{
                  color: editor.isActive(list.name) ? theme.palette.primary.main : "",
                }}
              >
                {createElement(list.icon, {
                  fontSize: "small",
                })}
              </ListItemIcon>
              <ListItemText>{list.label}</ListItemText>
            </MenuItem>
          );
        })}
        <Divider />
        {HEADINGS.map((heading) => {
          return (
            <MenuItem
              key={heading.name}
              onClick={() => heading.toggle(editor)}
              selected={editor.isActive("heading", {
                level: Number(heading.name[7]),
              })}
              sx={{
                backgroundColor: editor.isActive("heading", {
                  level: Number(heading.name[7]),
                })
                  ? `${theme.palette.primary.lighter} !important`
                  : "",
              }}
            >
              <ListItemIcon
                sx={{
                  color: editor.isActive("heading", {
                    level: Number(heading.name[7]),
                  })
                    ? theme.palette.primary.main
                    : "",
                }}
              >
                {heading.icon}
              </ListItemIcon>
              <ListItemText>{heading.label}</ListItemText>
            </MenuItem>
          );
        })}
      </Menu>
      {/* MENU */}
      <ToggleButtonGroup size="small" color="primary">
        {OTHERS.map((other) => {
          return (
            <ToggleButton
              key={other.name}
              value={other.name}
              aria-label={other.name}
              onClick={(e) => {
                if (other.name === "image") {
                  imageInputRef.current.click();
                } else if (other.name === "link") {
                  setAnchorElLink(e.currentTarget);
                } else if (other.name === "iframe") {
                  setAnchorElIframe(e.currentTarget);
                } else {
                  other.action(editor);
                }
              }}
            >
              {createElement(other.icon, {
                fontSize: "small",
              })}
              {other.name === "image" && <input type="file" hidden ref={imageInputRef} onChange={addImage} />}
            </ToggleButton>
          );
        })}
      </ToggleButtonGroup>
      <Menu
        id="basic-menu2"
        anchorEl={anchorElLink}
        open={!!anchorElLink}
        onClose={() => setAnchorElLink(null)}
        MenuListProps={{
          "aria-labelledby": "basic-button2",
          dense: true,
          style: { padding: 0 },
        }}
      >
        <Stack
          p={1}
          gap={1}
          component="form"
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            editor.commands.setLink({
              href: e.target?.link?.value || "https://example.com",
              target: e.target?.isBlank?.checked ? "_blank" : "_self",
              rel: "noopener noreferrer",
            });
            setAnchorElLink(null);
          }}
        >
          <Input autoFocus placeholder="URL" name="link" fullWidth />
          <Stack direction="row" justifyContent="space-between">
            <FormControlLabel
              name="isBlank"
              control={<Switch size="small" />}
              label="Open in new tab"
              labelPlacement="start"
              sx={{ transform: "translateX(-10px)" }}
            />
            <Button type="submit">Ajouter</Button>
          </Stack>
        </Stack>
      </Menu>
      <Menu
        id="basic-menu3"
        anchorEl={anchorElIframe}
        open={!!anchorElIframe}
        onClose={() => setAnchorElIframe(null)}
        MenuListProps={{
          "aria-labelledby": "basic-button3",
          dense: true,
          style: { padding: 0 },
        }}
      >
        <Stack
          p={1}
          gap={1}
          component="form"
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            if (e.target?.link?.value) {
              editor
                .chain()
                .focus()
                .setIframe({ src: getYouTubeEmbedUrl(e.target?.link?.value), width: "560", height: "315" })
                .run();
            }
            setAnchorElLink(null);
          }}
        >
          <Input autoFocus placeholder="Youtube Link" name="link" fullWidth />
          <Stack direction="row" justifyContent="flex-end">
            <Button type="submit">Ajouter</Button>
          </Stack>
        </Stack>
      </Menu>
      {/* MENU */}
      <ToggleButtonGroup size="small" color="primary">
        {ACTIONS.map((action) => {
          return (
            <ToggleButton key={action.name} value={action.name} aria-label={action.name} onClick={() => action.action(editor)}>
              {createElement(action.icon, { fontSize: "small" })}
            </ToggleButton>
          );
        })}
      </ToggleButtonGroup>
    </Stack>
  );
}

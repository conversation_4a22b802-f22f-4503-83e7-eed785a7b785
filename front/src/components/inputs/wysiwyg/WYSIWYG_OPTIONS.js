import FormatBoldIcon from "@mui/icons-material/FormatBold";
import FormatItalicIcon from "@mui/icons-material/FormatItalic";
import FormatUnderlinedIcon from "@mui/icons-material/FormatUnderlined";
import FormatAlignLeftIcon from "@mui/icons-material/FormatAlignLeft";
import FormatAlignRightIcon from "@mui/icons-material/FormatAlignRight";
import FormatAlignCenterIcon from "@mui/icons-material/FormatAlignCenter";
import FormatListBulletedIcon from "@mui/icons-material/FormatListBulleted";
import FormatListNumberedIcon from "@mui/icons-material/FormatListNumbered";
import UndoIcon from "@mui/icons-material/Undo";
import RedoIcon from "@mui/icons-material/Redo";
import SubdirectoryArrowLeftIcon from "@mui/icons-material/SubdirectoryArrowLeft";
import HorizontalRuleIcon from "@mui/icons-material/HorizontalRule";
import ImageIcon from "@mui/icons-material/Image";
import LinkIcon from "@mui/icons-material/Link";
import StrikethroughSIcon from "@mui/icons-material/StrikethroughS";
import SuperscriptIcon from "@mui/icons-material/Superscript";
import OndemandVideoIcon from '@mui/icons-material/OndemandVideo';

export const MARKS = [
  {
    name: "bold",
    icon: FormatBoldIcon,
    toggle: (editor) => editor.chain().focus().toggleBold().run(),
  },
  {
    name: "italic",
    icon: FormatItalicIcon,
    toggle: (editor) => editor.chain().focus().toggleItalic().run(),
  },
  {
    name: "underline",
    icon: FormatUnderlinedIcon,
    toggle: (editor) => editor.chain().focus().toggleUnderline().run(),
  },
  {
    name: "superscript",
    icon: SuperscriptIcon,
    toggle: (editor) => editor.chain().focus().toggleSuperscript().run(),
  },
  {
    name: "strike",
    icon: StrikethroughSIcon,
    toggle: (editor) => editor.chain().focus().toggleStrike().run(),
  },
];

export const ALIGNS = [
  {
    name: "left",
    icon: FormatAlignLeftIcon,
    toggle: (editor) => editor.chain().focus().setTextAlign("left").run(),
  },
  {
    name: "center",
    icon: FormatAlignCenterIcon,
    toggle: (editor) => editor.chain().focus().setTextAlign("center").run(),
  },
  {
    name: "right",
    icon: FormatAlignRightIcon,
    toggle: (editor) => editor.chain().focus().setTextAlign("right").run(),
  },
];

export const HEADINGS = [
  {
    label: "Heading 1",
    name: "heading1",
    icon: "H1",
    toggle: (editor) => editor.chain().focus().toggleHeading({ level: 1 }).run(),
  },
  {
    label: "Heading 2",
    name: "heading2",
    icon: "H2",
    toggle: (editor) => editor.chain().focus().toggleHeading({ level: 2 }).run(),
  },
  {
    label: "Heading 3",
    name: "heading3",
    icon: "H3",
    toggle: (editor) => editor.chain().focus().toggleHeading({ level: 3 }).run(),
  },
  {
    label: "Heading 4",
    name: "heading4",
    icon: "H4",
    toggle: (editor) => editor.chain().focus().toggleHeading({ level: 4 }).run(),
  },
  {
    label: "Heading 5",
    name: "heading5",
    icon: "H5",
    toggle: (editor) => editor.chain().focus().toggleHeading({ level: 5 }).run(),
  },
  {
    label: "Heading 6",
    name: "heading6",
    icon: "H6",
    toggle: (editor) => editor.chain().focus().toggleHeading({ level: 6 }).run(),
  },
  {
    label: "Paragraphe",
    name: "paragraph",
    icon: "P",
    toggle: (editor) => editor.chain().focus().setParagraph().run(),
  },
];

export const LISTS = [
  {
    label: "Bullet List",
    name: "bulletList",
    icon: FormatListBulletedIcon,
    toggle: (editor) => editor.chain().focus().toggleBulletList().run(),
  },
  {
    label: "Ordered List",
    name: "orderedList",
    icon: FormatListNumberedIcon,
    toggle: (editor) => editor.chain().focus().toggleOrderedList().run(),
  },
];

export const ACTIONS = [
  {
    name: "undo",
    icon: UndoIcon,
    action: (editor) => editor.chain().focus().undo().run(),
  },
  {
    name: "redo",
    icon: RedoIcon,
    action: (editor) => editor.chain().focus().redo().run(),
  },
];

export const OTHERS = [
  {
    name: "image",
    icon: ImageIcon,
  },
  {
    name: "link",
    icon: LinkIcon,
  },
  {
    name: "iframe",
    icon: OndemandVideoIcon,
  },
  {
    name: "horizontalRule",
    icon: HorizontalRuleIcon,
    action: (editor) => editor.chain().focus().setHorizontalRule().run(),
  },
  {
    name: "hardBreak",
    icon: SubdirectoryArrowLeftIcon,
    action: (editor) => editor.chain().focus().setHardBreak().run(),
  },
];

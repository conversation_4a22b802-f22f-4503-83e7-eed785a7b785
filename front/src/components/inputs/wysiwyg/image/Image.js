import { mergeAttributes } from "@tiptap/core";
import { ReactNodeViewRenderer } from "@tiptap/react";
import TiptapImage from "@tiptap/extension-image";
import ImageNodeComponent from "./ImageView";

const Image = TiptapImage.extend({
  group: "block",
  defining: true,
  isolating: true,

  addAttributes() {
    return {
      ...this.parent?.(),
      flipX: {
        default: false,
      },
      flipY: {
        default: false,
      },
      originWidth: {
        default: null,
      },
      originHeight: {
        default: null,
      },
      width: {
        default: "100%",
        parseHTML: (element) => {
          const width = element.style.width || element.getAttribute("width") || null;
          if (width?.endsWith("%")) {
            return width;
          }
          return width == null ? null : Number.parseInt(width, 10);
        },
        renderHTML: (attributes) => {
          return {
            width: attributes.width,
          };
        },
      },
      display: {
        default: "block",
        parseHTML: (element) => element.style.display || null,
        renderHTML: (attributes) => {
          return {
            style: `display: ${attributes.display};`,
          };
        },
      },
      marginLeft: {
        default: "0",
        parseHTML: (element) => element.style.marginLeft || null,
        renderHTML: (attributes) => {
          return {
            style: `margin-left: ${attributes.marginLeft};`,
          };
        },
      },
      marginRight: {
        default: "0",
        parseHTML: (element) => element.style.marginRight || null,
        renderHTML: (attributes) => {
          return {
            style: `margin-right: ${attributes.marginRight};`,
          };
        },
      },
      link: null,
      target: false,
    };
  },

  addOptions() {
    return {
      ...this.parent?.(),
    };
  },

  addNodeView() {
    return ReactNodeViewRenderer(ImageNodeComponent);
  },

  addCommands() {
    return {
      ...this.parent?.(),
      updateImage:
        (options) =>
        ({ chain }) => {
          return chain()
            .updateAttributes("image", { ...options })
            .run();
        },
      updateLink:
        (options) =>
        ({ chain }) => {
          return chain()
            .updateAttributes("image", { ...options })
            .run();
        },
    };
  },

  renderHTML({ node, HTMLAttributes }) {
    const { alignment } = node.attrs;
    // const { textAlign, flipX, flipY, alignment } = node.attrs;
    // const textAlignStyle =
    //   {
    //     left: "margin-right: auto;",
    //     right: "margin-left: auto;",
    //     center: "margin-left: auto; margin-right: auto;",
    //   }[textAlign] || "";
    // const transformStyle = flipX || flipY ? `transform: rotateX(${flipX ? "180" : "0"}deg) rotateY(${flipY ? "180" : "0"}deg);` : "";

    let style = "";
    if (alignment === "left") style = "margin-right: auto; margin-left: 0;";
    else if (alignment === "center") style = "margin-left: auto; margin-right: auto;";
    else if (alignment === "right") style = "margin-left: auto; margin-right: 0;";

    if (node.attrs.link) {
      return [
        "a",
        { href: node.attrs.link, target: node.attrs.target ? "_blank" : "_self", rel: node.attrs.target ? "noreferrer" : undefined },
        [
          "img",
          mergeAttributes(
            {
              height: "auto",
              style,
            },
            this.options.HTMLAttributes,
            HTMLAttributes
          ),
        ],
      ];
    }

    // const style = `${textAlignStyle} ${transformStyle}`;
    return [
      "img",
      mergeAttributes(
        {
          height: "auto",
          style,
        },
        this.options.HTMLAttributes,
        HTMLAttributes
      ),
    ];
  },

  parseHTML() {
    return [
      {
        tag: "img[src]",
      },
    ];
  },
});

export default Image;

export function clamp(val, min, max) {
  if (val < min) return min;
  if (val > max) return max;
  return val;
}

export const isNumber = (value) => typeof value === "number";

export function throttle(func, waitTime) {
  let timeout = null;
  let lastExec = 0;

  return function (...args) {
    const context = this;
    const now = Date.now();

    if (lastExec && now < lastExec + waitTime) {
      clearTimeout(timeout);
      timeout = setTimeout(() => {
        lastExec = now;
        func.apply(context, args);
      }, waitTime);
    } else {
      lastExec = now;
      func.apply(context, args);
    }
  };
}

export const IMAGE_MIN_SIZE = 20;
/** Maximum size for image adjustments */
export const IMAGE_MAX_SIZE = 100000;
/** Throttle time during adjustments for images (milliseconds) */
export const IMAGE_THROTTLE_WAIT_TIME = 16;

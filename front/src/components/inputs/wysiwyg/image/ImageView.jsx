import { useState, useRef, useEffect, useMemo, useCallback } from "react";
import { NodeViewWrapper } from "@tiptap/react";
import { IMAGE_MIN_SIZE, IMAGE_MAX_SIZE, IMAGE_THROTTLE_WAIT_TIME, clamp, isNumber, throttle } from "./Image";
import "./imageView.css";

const ResizeDirection = {
  TOP_LEFT: "tl",
  TOP_RIGHT: "tr",
  BOTTOM_LEFT: "bl",
  BOTTOM_RIGHT: "br",
};

export default function ImageView(props) {
  const { node, updateAttributes, editor, getPos, selected } = props;

  const [maxSize, setMaxSize] = useState({ width: IMAGE_MAX_SIZE, height: IMAGE_MAX_SIZE });
  const [originalSize, setOriginalSize] = useState({ width: 0, height: 0 });
  const [resizing, setResizing] = useState(false);
  const [resizerState, setResizerState] = useState({ x: 0, y: 0, w: 0, h: 0, dir: "" });

  const imageRef = useRef(null);

  const imgAttrs = useMemo(() => {
    const { src, alt, width, height, flipX, flipY, marginLeft, marginRight } = node.attrs;
    const transformStyles = [];
    if (flipX) transformStyles.push("rotateX(180deg)");
    if (flipY) transformStyles.push("rotateY(180deg)");
    const transform = transformStyles.join(" ");

    return {
      src: src || undefined,
      alt: alt || undefined,
      style: {
        width: isNumber(width) ? `${width}px` : width,
        height: isNumber(height) ? `${height}px` : height,
        transform: transform || "none",
        marginLeft: marginLeft || undefined,
        marginRight: marginRight || undefined,
      },
    };
  }, [node.attrs]);

  const imageMaxStyle = useMemo(
    () => ({
      width: imgAttrs.style.width === "100%" ? "100%" : undefined,
    }),
    [imgAttrs.style.width]
  );

  const blockAlignStyle = useMemo(() => {
    const { textAlign } = node.attrs;
    return (
      {
        left: "margin-right: auto;",
        right: "margin-left: auto;",
        center: "margin-left: auto; margin-right: auto;",
      }[textAlign] || ""
    );
  }, [node.attrs.textAlign]);

  const selectImage = useCallback(() => {
    editor.commands.setNodeSelection(getPos());
  }, [editor, getPos]);

  const getMaxSize = useCallback(
    throttle(() => {
      setMaxSize((prev) => ({
        ...prev,
        width: parseInt(getComputedStyle(editor.view.dom).width, 10),
      }));
    }, IMAGE_THROTTLE_WAIT_TIME),
    [editor]
  );

  const onMouseDown = useCallback(
    (e, dir) => {
      e.preventDefault();
      e.stopPropagation();

      const { width: originalWidth, height: originalHeight } = originalSize;
      const aspectRatio = originalWidth / originalHeight;

      let width = Number(node.attrs.width) || originalWidth;
      let height = Number(node.attrs.height) || Math.round(width / aspectRatio);
      const maxWidth = maxSize.width;

      width = Math.min(width > maxWidth ? maxWidth : width, maxWidth);
      height = Math.round(width / aspectRatio);

      setResizerState({ x: e.clientX, y: e.clientY, w: width, h: height, dir });
      setResizing(true);
    },
    [node.attrs, originalSize, maxSize]
  );

  const onMouseMove = useCallback(
    throttle((e) => {
      if (!resizing) return;

      const { x, w, dir } = resizerState;
      const dx = (e.clientX - x) * (/l/.test(dir) ? -1 : 1);
      const width = clamp(w + dx, IMAGE_MIN_SIZE, maxSize.width);

      updateAttributes({ width });
    }, IMAGE_THROTTLE_WAIT_TIME),
    [resizing, resizerState, updateAttributes, maxSize]
  );

  const onMouseUp = useCallback(
    (e) => {
      e.preventDefault();
      e.stopPropagation();
      if (!resizing) return;

      setResizing(false);
      setResizerState({ x: 0, y: 0, w: 0, h: 0, dir: "" });
      selectImage();
    },
    [resizing, selectImage]
  );

  useEffect(() => {
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        setOriginalSize({ width, height });
        updateAttributes({ originWidth: width, originHeight: height });
      }
    });

    const resizeObserverDom = new ResizeObserver(getMaxSize);

    if (imageRef.current) resizeObserver.observe(imageRef.current);
    resizeObserverDom.observe(editor.view.dom);

    return () => {
      resizeObserver.disconnect();
      resizeObserverDom.disconnect();
    };
  }, [editor, getMaxSize, updateAttributes]);

  useEffect(() => {
    if (resizing) {
      document.addEventListener("mousemove", onMouseMove);
      document.addEventListener("mouseup", onMouseUp);
    }

    return () => {
      document.removeEventListener("mousemove", onMouseMove);
      document.removeEventListener("mouseup", onMouseUp);
    };
  }, [resizing, onMouseMove, onMouseUp]);

  return (
    <NodeViewWrapper className="node-image">
      <div className="image-view" style={{ ...imgAttrs.style, ...blockAlignStyle }}>
        <div
          draggable="true"
          data-drag-handle
          className={`image-view__body ${selected ? "image-view__body--focused" : ""} ${resizing ? "image-view__body--resizing" : ""}`}
          style={imageMaxStyle}
        >
          <img
            src={imgAttrs.src}
            alt={imgAttrs.alt}
            style={imgAttrs.style}
            ref={imageRef}
            className="image-view__body__image block"
            onClick={selectImage}
          />
          {editor.view.editable && (selected || resizing) ? (
            <div className="image-resizer">
              {Object.values(ResizeDirection).map((direction) => (
                <span
                  key={direction}
                  className={`image-resizer__handler image-resizer__handler--${direction}`}
                  onMouseDown={(e) => onMouseDown(e, direction)}
                />
              ))}
            </div>
          ) : null}
        </div>
      </div>
    </NodeViewWrapper>
  );
}

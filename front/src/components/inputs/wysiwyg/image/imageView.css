.image-view {
    max-width: 100%;
}

.image-view__body {
    position: relative;
    display: inline-block;
    max-width: 100%;
}

.image-view__body__image {
    margin: 0;
    cursor: pointer !important;
}

.image-resizer {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: calc(100% - 4px);
    border: 1px solid #fff;
    outline: var(--primary-color) solid 2px;
    transition: all 0.1s ease-in;
}

.image-resizer__handler {
    position: absolute;
    z-index: 2;
    box-sizing: border-box;
    display: block;
    width: 12px;
    height: 12px;
    border-radius: 2px;
    background-color: var(--primary-color);
}

.image-resizer__handler--tl {
    top: -6px;
    left: -6px;
    cursor: nw-resize;
}

.image-resizer__handler--tr {
    top: -6px;
    right: -6px;
    cursor: ne-resize;
}

.image-resizer__handler--bl {
    bottom: -6px;
    left: -6px;
    cursor: sw-resize;
}

.image-resizer__handler--br {
    right: -6px;
    bottom: -6px;
    cursor: se-resize;
}

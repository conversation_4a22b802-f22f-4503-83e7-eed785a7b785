import { Button, <PERSON>u, Stack } from "@mui/material";
import Input from "../../Input";

export default function MenuAltForm({ editor, anchorEl, handleClose }) {
  const currentNode = editor?.state?.selection?.node;
  const existingAlt = currentNode?.attrs?.alt;

  return (
    <Menu
      id="alt-menu"
      anchorEl={anchorEl}
      open={!!anchorEl}
      onClose={handleClose}
      MenuListProps={{
        "aria-labelledby": "alt-button",
      }}
    >
      <Stack
        p={1}
        gap={1}
        direction="row"
        justifyContent="space-between"
        component="form"
        onSubmit={(e) => {
          e.preventDefault();
          e.stopPropagation();
          editor.chain().focus().updateImage({ alt: e.target.alt.value }).run();
          handleClose();
        }}
      >
        <Input name="alt" placeholder="Alternative text" defaultValue={existingAlt} autoFocus />
        <Button size="small" type="submit">
          Ajouter
        </Button>
      </Stack>
    </Menu>
  );
}

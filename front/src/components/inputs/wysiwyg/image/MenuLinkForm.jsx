import { <PERSON><PERSON>, FormControl<PERSON><PERSON>l, <PERSON><PERSON>, <PERSON><PERSON>, Switch } from "@mui/material";
import Input from "../../Input";

export default function MenuLinkForm({ editor, anchorEl, handleClose }) {
  const currentNode = editor?.state?.selection?.node;
  const existingLink = currentNode?.attrs?.link;
  const existingTarget = currentNode?.attrs?.target;

  return (
    <Menu
      id="link-menu"
      anchorEl={anchorEl}
      open={!!anchorEl}
      onClose={handleClose}
      MenuListProps={{
        "aria-labelledby": "link-button",
      }}
    >
      <Stack
        p={1}
        gap={1}
        component="form"
        onSubmit={(e) => {
          e.preventDefault();
          e.stopPropagation();
          const formData = new FormData(e.target);
          const isBlank = formData.get("isBlank") === "on";
          editor.chain().focus().updateLink({ link: e.target.link.value, target: isBlank }).run();
          handleClose();
        }}
      >
        <Input defaultValue={existingLink} autoFocus placeholder="URL" name="link" fullWidth />
        <Stack direction="row" justifyContent="space-between">
          <FormControlLabel
            control={<Switch name="isBlank" size="small" defaultChecked={existingTarget} />}
            label="Open in new tab"
            labelPlacement="start"
            sx={{ transform: "translateX(-10px)" }}
          />
          <Button type="submit" size="small">
            Ajouter
          </Button>
        </Stack>
      </Stack>
    </Menu>
  );
}

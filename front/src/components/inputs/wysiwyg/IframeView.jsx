import { useState, useRef, useEffect, useMemo, useCallback } from "react";
import { NodeViewWrapper } from "@tiptap/react";
import { IMAGE_MIN_SIZE, IMAGE_MAX_SIZE, IMAGE_THROTTLE_WAIT_TIME, clamp, isNumber, throttle } from "./image/Image";
import "./image/imageView.css";

const ResizeDirection = {
  TOP_LEFT: "tl",
  TOP_RIGHT: "tr",
  BOTTOM_LEFT: "bl",
  BOTTOM_RIGHT: "br",
};

export default function IframeView(props) {
  const { node, updateAttributes, editor, getPos, selected } = props;

  const [maxSize, setMaxSize] = useState({ width: IMAGE_MAX_SIZE, height: IMAGE_MAX_SIZE });
  const [originalSize, setOriginalSize] = useState({ width: 0, height: 0 });
  const [resizing, setResizing] = useState(false);
  const [resizerState, setResizerState] = useState({ x: 0, y: 0, w: 0, h: 0, dir: "" });

  const imageRef = useRef(null);

  const iframeAttrs = useMemo(() => {
    const { src, alt, width, height, flipX, flipY, marginLeft, marginRight } = node.attrs;
    const transformStyles = [];
    if (flipX) transformStyles.push("rotateX(180deg)");
    if (flipY) transformStyles.push("rotateY(180deg)");
    const transform = transformStyles.join(" ");

    return {
      src: src || undefined,
      alt: alt || undefined,
      style: {
        width: isNumber(width) ? `${width}px` : width,
        height: isNumber(height) ? `${height}px` : height,
        transform: transform || "none",
        marginLeft: marginLeft || undefined,
        marginRight: marginRight || undefined,
      },
    };
  }, [node.attrs]);

  const iframeMaxStyle = useMemo(
    () => ({
      width: iframeAttrs.style.width === "100%" ? "100%" : undefined,
    }),
    [iframeAttrs.style.width]
  );

  const blockAlignStyle = useMemo(() => {
    const { parentStyle } = node.attrs;

    if (parentStyle) {
      if (parentStyle === "text-align: left;") {
        return "left";
      }
      if (parentStyle === "text-align: center;") {
        return "center";
      }
      if (parentStyle === "text-align: right;") {
        return "right";
      }
    }

    return "";
  }, [node.attrs]);

  const selectImage = useCallback(() => {
    editor.commands.setNodeSelection(getPos());
  }, [editor, getPos]);

  const getMaxSize = useCallback(
    throttle(() => {
      setMaxSize((prev) => ({
        ...prev,
        width: Number.parseInt(getComputedStyle(editor.view.dom).width, 10),
      }));
    }, IMAGE_THROTTLE_WAIT_TIME),
    [editor]
  );

  const onMouseDown = useCallback(
    (e, dir) => {
      e.preventDefault();
      e.stopPropagation();

      const { width: originalWidth, height: originalHeight } = originalSize;

      let width = Number(node.attrs.width) || originalWidth;
      let height = Number(node.attrs.height) || originalHeight;
      const maxWidth = maxSize.width;
      const maxHeight = maxSize.height;

      width = Math.min(width > maxWidth ? maxWidth : width, maxWidth);
      height = Math.min(height > maxHeight ? maxHeight : height, maxHeight);

      setResizerState({ x: e.clientX, y: e.clientY, w: width, h: height, dir });
      setResizing(true);
    },
    [node.attrs, originalSize, maxSize]
  );

  const onMouseMove = useCallback(
    throttle((e) => {
      if (!resizing) return;

      const { x, y, w, h, dir } = resizerState;
      const dx = (e.clientX - x) * (/l/.test(dir) ? -1 : 1);
      const dy = (e.clientY - y) * (/l/.test(dir) ? -1 : 1);
      const width = clamp(w + dx, IMAGE_MIN_SIZE, maxSize.width);
      const height = clamp(h + dy, IMAGE_MIN_SIZE, maxSize.height);

      updateAttributes({ width, height });
    }, IMAGE_THROTTLE_WAIT_TIME),
    [resizing, resizerState, updateAttributes, maxSize]
  );

  const onMouseUp = useCallback(
    (e) => {
      e.preventDefault();
      e.stopPropagation();
      if (!resizing) return;

      setResizing(false);
      setResizerState({ x: 0, y: 0, w: 0, h: 0, dir: "" });
      selectImage();
    },
    [resizing, selectImage]
  );

  useEffect(() => {
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        setOriginalSize({ width, height });
        updateAttributes({ originWidth: width, originHeight: height });
      }
    });

    const resizeObserverDom = new ResizeObserver(getMaxSize);

    if (imageRef.current) resizeObserver.observe(imageRef.current);
    resizeObserverDom.observe(editor.view.dom);

    return () => {
      resizeObserver.disconnect();
      resizeObserverDom.disconnect();
    };
  }, [editor, getMaxSize, updateAttributes]);

  useEffect(() => {
    if (resizing) {
      document.addEventListener("mousemove", onMouseMove);
      document.addEventListener("mouseup", onMouseUp);
    }

    return () => {
      document.removeEventListener("mousemove", onMouseMove);
      document.removeEventListener("mouseup", onMouseUp);
    };
  }, [resizing, onMouseMove, onMouseUp]);

  return (
    <NodeViewWrapper className="node-image">
      <div className="image-view" style={{ textAlign: blockAlignStyle }}>
        <div
          draggable="true"
          data-drag-handle
          className={`image-view__body ${selected ? "image-view__body--focused" : ""} ${resizing ? "image-view__body--resizing" : ""}`}
          style={iframeMaxStyle}
        >
          <iframe
            src={iframeAttrs.src}
            alt={iframeAttrs.alt}
            width={iframeAttrs.style.width}
            height={iframeAttrs.style.height}
            style={iframeAttrs.style}
            ref={imageRef}
            frameBorder={0}
            className="image-view__body__image block"
            onClick={selectImage}
          />
          {editor.view.editable && (selected || resizing) ? (
            <div className="image-resizer">
              {Object.values(ResizeDirection).map((direction) => (
                <span
                  key={direction}
                  className={`image-resizer__handler image-resizer__handler--${direction}`}
                  onMouseDown={(e) => onMouseDown(e, direction)}
                />
              ))}
            </div>
          ) : null}
        </div>
      </div>
    </NodeViewWrapper>
  );
}

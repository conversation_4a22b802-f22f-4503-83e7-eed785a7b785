"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EditorContent, useEditor } from "@tiptap/react";
import { Color } from "@tiptap/extension-color";
import HardBreak from "@tiptap/extension-hard-break";
import Link from "@tiptap/extension-link";
import Placeholder from "@tiptap/extension-placeholder";
import TextAlign from "@tiptap/extension-text-align";
import TextStyle from "@tiptap/extension-text-style";
import Underline from "@tiptap/extension-underline";
import Superscript from "@tiptap/extension-superscript";
import StarterKit from "@tiptap/starter-kit";
import { Card, Divider, Stack, ToggleButton, ToggleButtonGroup, Typography } from "@mui/material";
import FormatAlignLeftIcon from "@mui/icons-material/FormatAlignLeft";
import LinkIcon from "@mui/icons-material/Link";
import FormatAlignRightIcon from "@mui/icons-material/FormatAlignRight";
import FormatAlignCenterIcon from "@mui/icons-material/FormatAlignCenter";
import TextFieldsIcon from "@mui/icons-material/TextFields";
import MenuAltForm from "./image/MenuAltForm";
import { useEffect, useState } from "react";
import theme from "@/lib/theme";
import MenuBarWysiwyg from "./MenuBarWysiwyg";
import InputHidden from "../InputHidden";
import Image from "./image/Image";
import Iframe from "./iframe.js";
import "./iframe.css";
import MenuLinkForm from "./image/MenuLinkForm";

export default function InputWysiwyg({ id, defaultValue = "", label, name, defaultRef, onChange, required = false }) {
  const [anchorEl, setAnchorEl] = useState(null);
  const [anchorElLink, setAnchorElLink] = useState(null);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const handleClickLink = (event) => {
    setAnchorElLink(event.currentTarget);
  };
  const handleCloseLink = () => {
    setAnchorElLink(null);
  };

  const editor = useEditor({
    extensions: [
      Image,
      StarterKit.configure({
        hardBreak: false, // Disable the default hard break
      }),
      Color,
      TextStyle,
      Superscript,
      Link.configure({
        openOnClick: true,
        autolink: true,
        defaultProtocol: "https",
        rel: "noopener noreferrer",
      }),
      Underline,
      TextAlign.configure({
        types: ["heading", "paragraph"],
      }),
      // Placeholder.configure({
      //   placeholder: "Saissisez votre texte ici",
      // }),
      Iframe,
      HardBreak.extend({
        addKeyboardShortcuts() {
          return {
            Enter: () => {
              this.editor?.chain().focus().createParagraphNear().run();
              setTimeout(() => {
                this.editor?.chain().focus().setHardBreak().run();
              }, 10);
            },
          };
        },
      }),
    ],
    content: updateIframeParent(defaultValue),
    onUpdate: ({ editor }) => {
      onChange ? onChange(editor.getHTML()) : null;
    },
  });

  const wordCount = (words) => {
    return words.split(" ").filter((word) => word !== "").length;
  };

  useEffect(() => {
    if (defaultValue && editor) {
      editor?.commands?.setContent(updateIframeParent(defaultValue));
    }
  }, [defaultValue]);

  if (!editor) return null;

  const currentNode = editor?.state?.selection?.node;
  const existingAlt = currentNode?.attrs?.alt;

  return (
    <Stack ref={defaultRef}>
      <Typography component="span" onClick={() => editor.commands.focus()} variant="subtitle2">
        {label}
        {required ? <span>*</span> : null}
      </Typography>
      <Card
        sx={{
          border: `1px solid ${theme.palette.grey.main}`,
          p: 1,
          position: "relative",
        }}
      >
        <Stack gap={1}>
          <MenuBarWysiwyg editor={editor} />
          <Divider />
          <Stack
            minHeight={100}
            maxHeight={350}
            overflow="auto"
            py={1}
            sx={{
              backgroundColor: "rgba(0, 0, 0, 0.2)",
            }}
          >
            <EditorContent
              id={id}
              editor={editor}
              style={{
                minHeight: "inherit",
                // v To prevent outline of img and iframe resizer to disappear
                padding: "0 5px",
              }}
            />
            {editor ? (
              <BubbleMenu editor={editor} updateDelay={0} shouldShow={({ editor }) => editor.isActive("image")}>
                <div>
                  <ToggleButtonGroup size="small" color="primary">
                    <ToggleButton
                      value="left"
                      aria-label="left"
                      onClick={() => editor.chain().focus().updateImage({ marginLeft: "0", marginRight: "auto" }).run()}
                    >
                      <FormatAlignLeftIcon fontSize="small" />
                    </ToggleButton>
                    <ToggleButton
                      value="center"
                      aria-label="center"
                      onClick={() => editor.chain().focus().updateImage({ marginLeft: "auto", marginRight: "auto" }).run()}
                    >
                      <FormatAlignCenterIcon fontSize="small" />
                    </ToggleButton>
                    <ToggleButton
                      value="right"
                      aria-label="right"
                      onClick={() => editor.chain().focus().updateImage({ marginLeft: "auto", marginRight: "0" }).run()}
                    >
                      <FormatAlignRightIcon fontSize="small" />
                    </ToggleButton>
                    <ToggleButton value="link" aria-label="link" onClick={handleClickLink}>
                      <LinkIcon fontSize="small" />
                    </ToggleButton>
                    <ToggleButton value="alt" aria-label="alt" onClick={handleClick}>
                      <TextFieldsIcon fontSize="small" />
                      <span style={{ color: existingAlt ? theme.palette.green.main : theme.palette.error.main }}>{existingAlt ? "✓" : "!"}</span>
                    </ToggleButton>
                  </ToggleButtonGroup>
                  <MenuAltForm editor={editor} anchorEl={anchorEl} handleClose={handleClose} />
                  <MenuLinkForm editor={editor} anchorEl={anchorElLink} handleClose={handleCloseLink} />
                </div>
              </BubbleMenu>
            ) : null}
            {editor ? (
              <BubbleMenu editor={editor} updateDelay={0} shouldShow={({ editor }) => editor.isActive("iframe")}>
                <div>
                  <ToggleButtonGroup size="small" color="primary">
                    <ToggleButton
                      value="left"
                      aria-label="left"
                      onClick={() => editor.chain().focus().updateIframeStyle({ textAlign: "left" }).run()}
                    >
                      <FormatAlignLeftIcon fontSize="small" />
                    </ToggleButton>
                    <ToggleButton
                      value="center"
                      aria-label="center"
                      onClick={() => editor.chain().focus().updateIframeStyle({ textAlign: "center" }).run()}
                    >
                      <FormatAlignCenterIcon fontSize="small" />
                    </ToggleButton>
                    <ToggleButton
                      value="right"
                      aria-label="right"
                      onClick={() => editor.chain().focus().updateIframeStyle({ textAlign: "right" }).run()}
                    >
                      <FormatAlignRightIcon fontSize="small" />
                    </ToggleButton>
                  </ToggleButtonGroup>
                </div>
              </BubbleMenu>
            ) : null}
            <InputHidden name={name} value={editor.getHTML()} required={required} />
          </Stack>
        </Stack>
        <Divider />
        <Stack direction="row" gap={1} justifyContent="flex-end" mt={1}>
          <Typography variant="subtitle1">{wordCount(editor.getText())} mots</Typography>
          <Typography variant="subtitle1">/</Typography>
          <Typography variant="subtitle1">{editor.getText().length} caractères</Typography>
        </Stack>
      </Card>
    </Stack>
  );
}

const updateIframeParent = (content) => {
  if (typeof window !== "undefined") {
    const parser = new DOMParser();
    const doc = parser.parseFromString(content, "text/html");

    doc.querySelectorAll("p > iframe").forEach((iframe) => {
      const parentP = iframe.parentElement;
      const newDiv = doc.createElement("div");

      Array.from(parentP.attributes).forEach((attr) => {
        newDiv.setAttribute(attr.name, attr.value);
      });

      newDiv.appendChild(iframe);
      parentP.parentNode.replaceChild(newDiv, parentP);
    });
    return doc.body.innerHTML;
  }
};

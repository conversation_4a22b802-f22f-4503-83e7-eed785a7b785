"use client";

import { FormControlLabel, Radio, RadioGroup, Stack } from "@mui/material";
import InputMedias from "./InputMedias";
import InputVideo from "./InputVideo";
import { useState } from "react";

export default function InputMediaChoice({ label, defaultValue, required, nameImage, nameVideo, ...props }) {
  const [isImage, setIsImage] = useState(defaultValue?.includes("/uploads/"));

  const valueImage = defaultValue?.includes("/uploads/") ? defaultValue : null;
  const valueVideo = defaultValue?.includes("/uploads/") ? null : defaultValue;

  return (
    <Stack>
      <RadioGroup defaultValue={isImage}>
        <Stack direction="row" gap={2}>
          <FormControlLabel value={true} control={<Radio />} label="Image" onChange={() => setIsImage(true)} />
          <FormControlLabel value={false} control={<Radio />} label="Vidéo" onChange={() => setIsImage(false)} />
        </Stack>
      </RadioGroup>
      {isImage ? (
        <>
          <InputMedias label={label} defaultValue={valueImage} name={nameImage} {...props} />
          <input type="hidden" name={nameVideo} value={null} />
        </>
      ) : (
        <>
          <InputVideo label={label} defaultValue={valueVideo} name={nameVideo} fullWidth {...props} />
          <input type="hidden" name={nameImage} value={null} />
        </>
      )}
    </Stack>
  );
}

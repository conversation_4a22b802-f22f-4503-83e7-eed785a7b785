"use client";

import Input from "@/components/inputs/Input";
import Loader from "@/components/Loader";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import theme from "@/lib/theme";
import TranslationUtils from "@/utils/translation.utils";
import { Button, MenuItem, Stack, Typography } from "@mui/material";
import { useRouter } from "next/navigation";
import { useState } from "react";

export default function DetailJob({ job }) {
  const { add } = useSnack();
  const { post, getToken } = useApi();
  const router = useRouter();
  const [coverLetter, setCoverLetter] = useState(null);
  const [cv, setCv] = useState(null);

  const civilities = ["MRS", "MISTER", "MISS", "DOCTOR", "PROFESSOR", "MASTER"];
  const situations = ["IN_OFFICE", "UNEMPLOYED", "IN_TRAING"];

  const submit = async (e) => {
    try {
      e.preventDefault();
      const formData = new FormData(e.target);

      await post("/job-applications", {
        civility: formData.get("civility") || null,
        lastname: formData.get("lastname"),
        firstname: formData.get("firstname"),
        address: formData.get("address"),
        zipCode: formData.get("zipCode"),
        city: formData.get("city"),
        phone: formData.get("phone"),
        email: formData.get("email"),
        birthdate: formData.get("birthdate"),
        training: formData.get("training"),
        professionalExperience: formData.get("professionalExperience"),
        computerLevel: formData.get("computerLevel") || null,
        situation: formData.get("situation") || null,
        workplace: formData.get("workplace") || null,
        availability: formData.get("availability") || null,
        salary: formData.get("salary") ? parseFloat(formData.get("salary")) : null,
        coverLetter: coverLetter ? coverLetter?.["@id"] : null,
        cv: cv ? cv?.["@id"] : null,
        jobOffer: job["@id"],
        wantedJob: null,
      });
      add("success", "Votre candidature a bien été envoyée !");
      e.target.reset();
      router.back();
      router.refresh();
    } catch (error) {
      add("error", error?.description);
      console.error(error);
    }
  };

  const uploadImage = (file) => {
    let req = new XMLHttpRequest();

    return new Promise((resolve, reject) => {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("alt", file.name);
      req.open("POST", `${process.env.NEXT_PUBLIC_API_URL}/api/uploads`);
      req.onreadystatechange = (e) => {
        if (req.readyState !== 4) {
          return;
        }

        if (req.status === 201) {
          resolve(JSON.parse(req.responseText));
        } else {
          add('error', 'impossible de téléverser votre fichier');
          resolve(false)
        }
      };

      req.send(formData);
    });
  };

  const handleMedia = async (e, callback) => {
    const file = e.target.files[0];
    const media = await uploadImage(file);
    callback(media);
  };

  if (!job) {
    return (
      <Stack my={2}>
        <Loader />
      </Stack>
    );
  }

  return (
    <Stack gap={4} py={5}>
      <Typography variant="h1" textAlign="center">
        {job.title}
      </Typography>
      <Stack border={`1px solid ${theme.palette.grey.light}`}>
        <Stack bgcolor="grey.main" color="white" p={1}>
          <Typography variant="body1">Détail de l&apos;offre</Typography>
        </Stack>
        <Stack bgcolor="white" display="grid" gridTemplateColumns="1fr 2fr" borderBottom={`1px solid ${theme.palette.grey.light}`}>
          <Typography p={1}>Référence :</Typography>
          <Typography p={1} borderLeft={`1px solid ${theme.palette.grey.light}`}>
            {job.reference}
          </Typography>
        </Stack>
        <Stack bgcolor="white" display="grid" gridTemplateColumns="1fr 2fr" borderBottom={`1px solid ${theme.palette.grey.light}`}>
          <Typography p={1}>Lieu :</Typography>
          <Typography p={1} borderLeft={`1px solid ${theme.palette.grey.light}`}>
            {job.location}
          </Typography>
        </Stack>
        <Stack bgcolor="white" display="grid" gridTemplateColumns="1fr 2fr">
          <Typography p={1}>Cabinet concerné :</Typography>
          <Typography p={1} borderLeft={`1px solid ${theme.palette.grey.light}`}>
            {job.agency?.name ?? 'Offre globale'}
          </Typography>
        </Stack>
      </Stack>
      <Stack component={Typography} dangerouslySetInnerHTML={{ __html: job.description }} />
      <Stack component="form" bgcolor="white" p={2} borderRadius={1} gap={2} onSubmit={submit}>
        <Stack display="grid" gridTemplateColumns="1fr 1fr" gap={2}>
          <Input name="civility" label="Civilité" select fullWidth>
            <MenuItem value={null}>
              <em>Non défini</em>
            </MenuItem>
            {civilities.map((civility) => (
              <MenuItem key={civility} value={civility}>
                {TranslationUtils.get(`civility.${civility}`)}
              </MenuItem>
            ))}
          </Input>
          <Input name="lastname" required label="Nom" fullWidth />
          <Input name="firstname" required label="Prénom" fullWidth />
          <Input name="address" required label="Adresse" fullWidth />
          <Input name="zipCode" required label="Code postal" fullWidth />
          <Input name="city" required label="Ville" fullWidth />
          <Input name="phone" required label="Téléphone" fullWidth />
          <Input name="email" required label="Email" fullWidth type="email" />
          <Input name="birthdate" required label="Date de naissance" type="date" fullWidth />
        </Stack>
        <Input name="training" multiline rows={3} required label="Vos formations et certifications" fullWidth />
        <Input name="professionalExperience" multiline rows={3} required label="Expériences professionnelles" fullWidth />
        <Input name="computerLevel" multiline rows={3} label="Niveau de connaissance informatique" fullWidth />
        <Input name="situation" label="Situation actuelle" select fullWidth>
          <MenuItem value={null}>
            <em>Non défini</em>
          </MenuItem>
          {situations.map((situation) => (
            <MenuItem key={situation} value={situation}>
              {TranslationUtils.get(`situation.${situation}`)}
            </MenuItem>
          ))}
        </Input>
        <Input name="workplace" label="Lieu de travail souhaité" fullWidth />
        <Input name="availability" label="Disponibilité" fullWidth />
        <Input name="salary" type="number" label="Salaire mensuel souhaité (brut)" fullWidth />
        <Input name="coverLetter" type="file" label="Lettre de motivation" fullWidth required onChange={(e) => handleMedia(e, setCoverLetter)} />
        <Input name="cv" type="file" label="CV" fullWidth required onChange={(e) => handleMedia(e, setCv)} />
        <Stack direction="row" justifyContent="flex-end">
          <Button type="submit">{TranslationUtils.get("global.validate")}</Button>
        </Stack>
      </Stack>
    </Stack>
  );
}

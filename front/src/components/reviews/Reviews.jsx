"use client";

import { useEffect } from "react";

export default function Reviews({ js, html, id }) {
  function parseScriptFromString(scriptString) {
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = scriptString.trim();
    return tempDiv.querySelector("script");
  }

  useEffect(() => {
    if (!html) return;

    const initScript = parseScriptFromString(js);

    const script = document.createElement("script");
    script.src = initScript.src;
    script.async = true;
    script.setAttribute("data-client-id", initScript.getAttribute("data-client-id"));
    script.id = initScript.id;

    document.head.appendChild(script);

    return () => {
      const existingScript = document.getElementById("widget-LR");
      if (existingScript) {
        existingScript.remove();
      }
    };
  }, []);

  return html ? <div dangerouslySetInnerHTML={{ __html: html }} id={id} /> : null;
}

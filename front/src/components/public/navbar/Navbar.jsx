"use client";

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Button, TextField, IconButton, Stack, Box } from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import { styled } from "@mui/material/styles";
import LayoutContainer from "../../LayoutContainer";
import NavLink from "./NavLink";
import NavbarDrawer from "./NavbarDrawer";
import { useEffect, useRef, useState } from "react";
import menuIcon from "@public/assets/pictos/menu.svg";
import Link from "next/link";
import Image from "next/image";
import FormatUtils from "@/utils/format.utils";
import { generatePath } from "@/utils/link.utils";
import { useRouter } from "next/navigation";
import { useSettings } from "@/context/SettingsProvider";

export default function Navbar({ data }) {
  const { navs, logo, links } = data;
  const { settings } = useSettings();

  const router = useRouter();
  const ref = useRef(null);

  const [itemSelected, setItemSelected] = useState(null);
  const [mobileOpen, setMobileOpen] = useState(false);
  const [height, setHeight] = useState(100);

  const handleDrawerToggle = () => {
    setMobileOpen((prevState) => !prevState);
    setItemSelected(null);
  };

  useEffect(() => {
    setHeight(ref.current.offsetHeight);
  }, [ref.current]);

  return (
    <>
      <StyledAppBar ref={ref}>
        <LayoutContainer>
          <MobileNav>
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ placeSelf: "start", p: "10px" }}
              disableRipple
            >
              <Image width={28} src={menuIcon} alt="MenuIcon" />
            </IconButton>
            <Box sx={{ placeSelf: "center", paddingTop: "5px" }}>
              {logo?.upload ? (
                <Link href={generatePath(logo.link)}>
                  <Image src={FormatUtils.binary(logo.upload?.["@id"])} alt={logo?.upload?.alt} height={40} width={40} />
                </Link>
              ) : null}
            </Box>
          </MobileNav>
          <Toolbar
            component={Stack}
            direction="row"
            display={{ xs: "none", md: "grid" }}
            sx={{
              alignItems: "stretch !important",
              position: "unset",
            }}
            gridTemplateColumns="max-content 1fr max-content"
            gap={4}
            pt={2}
          >
            <Stack direction="row" alignItems="flex-start">
              {logo?.upload ? (
                <Link href={generatePath(logo.link)}>
                  <Image src={FormatUtils.binary(logo.upload?.["@id"])} alt={logo?.upload?.alt} height={64} width={64} />
                </Link>
              ) : null}
            </Stack>
            <Stack justifyContent="space-between" gap={2}>
              <Stack
                direction="row"
                alignItems="center"
                component="form"
                onSubmit={(e) => {
                  e.preventDefault();
                  router.push(`${settings.searchLink}?search=${e.target.search.value}`);
                }}
              >
                <TextField
                  placeholder="Rechercher votre diagnostiqueur (ville, code postal...)"
                  variant="outlined"
                  size="small"
                  name="search"
                  fullWidth
                  slotProps={{
                    input: {
                      startAdornment: <SearchIcon color="action" sx={{ mr: 0.5 }} />,
                    },
                  }}
                />
              </Stack>
              <Stack direction="row" alignItems="center" gap={4} whiteSpace="nowrap" color="black">
                {navs
                  ?.filter((item) => item.link?.url !== "/")
                  ?.map((item, index) => (
                    <NavLink key={index} item={item} />
                  ))}
              </Stack>
            </Stack>
            <Stack direction="row" pb={2} gap={1}>
              <Stack alignItems="flex-end" direction="column" gap={1}>
                {links?.length
                  ? links.slice(0, 2).map((link, index) => (
                      <ActionButton
                        key={index}
                        whitespace="nowrap"
                        href={generatePath(link.link)}
                        startIcon={
                          link.link.upload?.["@id"] ? (
                            <Image src={FormatUtils.binary(link.link.upload?.["@id"])} alt={link.link.upload?.alt} height={20} width={20} />
                          ) : null
                        }
                      >
                        {link?.link?.label || link?.link?.content?.name}
                      </ActionButton>
                    ))
                  : null}
              </Stack>
              <Stack height="auto">
                {links?.length === 3
                  ? links.slice(2, 3).map((link, index) => (
                      <ActionButton
                        key={index}
                        color="secondary"
                        href={generatePath(link.link)}
                        startIcon={
                          link.link.upload?.["@id"] ? (
                            <Image src={FormatUtils.binary(link.link.upload?.["@id"])} alt={link.link.upload?.alt} height={20} width={20} />
                          ) : null
                        }
                      >
                        {link?.link?.label || link?.link?.content?.name}
                      </ActionButton>
                    ))
                  : null}
              </Stack>
            </Stack>
          </Toolbar>
        </LayoutContainer>
        <NavbarDrawer
          nav={navs}
          logo={logo}
          links={links}
          mobileOpen={mobileOpen}
          handleDrawerToggle={handleDrawerToggle}
          itemSelected={itemSelected}
          setItemSelected={setItemSelected}
        />
      </StyledAppBar>
      <div style={{ height, position: "relative" }} />
    </>
  );
}

const StyledAppBar = styled(AppBar)({
  backgroundColor: "white",
  width: "100%",
  boxShadow: "none",
  borderBottom: "1px solid #e0e0e0",
  position: "fixed",
  top: 0,
  zIndex: 1000,
});

export const ActionButton = styled(Button)(({ whitespace }) => ({
  whiteSpace: whitespace,
  height: "100%",
  maxWidth: 220,
  fontSize: "12px",
}));

const MobileNav = styled("div")(({ theme }) => ({
  display: "none",
  padding: "10px 0",
  [theme.breakpoints.down("md")]: {
    display: "grid",
    alignItems: "center",
    gridTemplateColumns: "1fr 1fr 1fr",
  },
}));

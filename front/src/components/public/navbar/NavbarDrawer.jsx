"use client";

import { Box, Drawer, IconButton, Stack, styled, Typography } from "@mui/material";
import Link from "next/link";
import closeIcon from "@public/assets/pictos/closeIcon.svg";
import chevronLeft from "@public/assets/pictos/picto-chevronLeft.svg";
import chevronRight from "@public/assets/pictos/picto-chevronRight.svg";
import { ActionButton } from "./Navbar";
import Image from "next/image";
import FormatUtils from "@/utils/format.utils";
import { generatePath } from "@/utils/link.utils";
import { usePathname } from "next/navigation";

export default function NavbarDrawer({ nav, logo, links, mobileOpen, handleDrawerToggle, itemSelected, setItemSelected }) {
  const pathname = usePathname();

  const getParent = (nav, itemSelected) => {
    for (const navItem of nav) {
      if (navItem.uuid === itemSelected.uuid) {
        return null;
      }

      if (navItem.children) {
        const childMatch = navItem.children.find((child) => child.uuid === itemSelected.uuid);
        if (childMatch) {
          return navItem;
        }
      }
    }

    return null;
  };

  return (
    <nav>
      <Drawer
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{ keepMounted: true }}
        sx={{
          "& .MuiDrawer-paper": {
            boxSizing: "border-box",
            borderRadius: 0,
            width: "100%",
          },
        }}
      >
        <Box height="100dvh" display="flex" flexDirection="column" justifyContent="space-between">
          <Box display="flex" p="1rem">
            <IconButton color="inherit" aria-label="open drawer" edge="start" onClick={handleDrawerToggle} disableRipple>
              <Image width={28} src={closeIcon} alt="CloseIcon" />
            </IconButton>
            <Box display="flex" justifyContent="center" gap={1} width="100%">
              {logo?.upload ? (
                <Link href={generatePath(logo?.link)}>
                  <Image src={FormatUtils.binary(logo.upload?.["@id"])} alt={logo.upload?.alt} height={40} width={40} />
                </Link>
              ) : null}
            </Box>
          </Box>
          <List
            sx={{
              padding: "2rem",
              display: "flex",
              flexDirection: "column",
              gap: "1rem",
              listStyle: "none",
              overflowY: "auto",
              height: "100%",
            }}
          >
            {itemSelected?.children?.length > 0 ? (
              <>
                <NavItem>
                  <IconButton
                    color="inherit"
                    aria-label="open drawer"
                    edge="start"
                    onClick={() => setItemSelected(getParent(nav, itemSelected))}
                    sx={{
                      transform: "translateX(-1rem)",
                      p: 0,
                    }}
                    disableRipple
                  >
                    <Image src={chevronLeft} alt="chevron-left" />
                    <Typography variant="bold" ml={0.5}>
                      {itemSelected?.link?.label || itemSelected?.link?.content?.name}
                    </Typography>
                  </IconButton>
                </NavItem>
                {itemSelected.children.map((sub, index) => (
                  <NavItem key={index}>
                    {sub.children?.length ? (
                      <Box display="flex" alignItems="center" gap={1} onClick={() => setItemSelected(sub)}>
                        <Typography
                          variant={
                            pathname === generatePath(sub?.link) || sub?.children?.some((child) => pathname === generatePath(child?.link))
                              ? "bold"
                              : "body1"
                          }
                        >
                          {sub?.link?.label || sub?.link?.content?.name}
                        </Typography>
                        <Image src={chevronRight} alt="chevron-right" width={20} />
                      </Box>
                    ) : (
                      <Link
                        href={generatePath(sub?.link)}
                        onClick={handleDrawerToggle}
                        style={{
                          display: "flex",
                          alignItems: "center",
                          gap: 5,
                        }}
                      >
                        <Typography variant={pathname === generatePath(sub?.link) ? "bold" : "body1"}>
                          {sub?.link?.label || sub?.link?.content?.name}
                        </Typography>
                      </Link>
                    )}
                  </NavItem>
                ))}
              </>
            ) : (
              nav?.map((item, index) => (
                <NavItem key={index}>
                  {item?.children?.length > 0 ? (
                    <Box display="flex" alignItems="center" gap={1} onClick={() => setItemSelected(item)}>
                      <Typography
                        variant={
                          pathname === generatePath(item?.link) ||
                          item?.children?.some((child) => pathname === generatePath(child?.link)) ||
                          item?.children.find((e) => e.children?.some((child) => pathname === generatePath(child?.link)))
                            ? "bold"
                            : "body1"
                        }
                      >
                        {item?.link?.label || item?.link?.content?.name}
                      </Typography>
                      <Image src={chevronRight} alt="chevron-right" width={20} />
                    </Box>
                  ) : (
                    <Link
                      href={generatePath(item?.link)}
                      onClick={() => {
                        handleDrawerToggle();
                      }}
                      style={{
                        display: "flex",
                        alignItems: "center",
                        gap: 5,
                      }}
                    >
                      <Typography
                        variant={
                          pathname === generatePath(item?.link) || item?.children?.some((child) => pathname === generatePath(child?.link))
                            ? "bold"
                            : "body1"
                        }
                      >
                        {item?.link?.label || item?.link?.content?.name}
                      </Typography>
                      {item?.children?.length > 0 && <Image src={chevronRight} alt="chevron-right" width={20} />}
                    </Link>
                  )}
                </NavItem>
              ))
            )}
            <Stack pt={5} gap={1}>
              {links?.length
                ? links.map((link, index) => (
                    <ActionButton
                      key={index}
                      sx={{
                        height: "40px",
                        width: "max-content",
                      }}
                      href={generatePath(link?.link)}
                      whitespace="nowrap"
                      color={index === 2 ? "secondary" : "primary"}
                      startIcon={
                        link.link.upload?.["@id"] ? (
                          <Image src={FormatUtils.binary(link.link.upload?.["@id"])} alt={link.link.upload?.alt} height={20} width={20} />
                        ) : null
                      }
                    >
                      {link?.link?.label || link?.link?.content?.name}
                    </ActionButton>
                  ))
                : null}
            </Stack>
          </List>
        </Box>
      </Drawer>
    </nav>
  );
}

const List = styled("ul")(() => ({
  padding: "2rem",
  listStyle: "none",
  display: "flex",
  flexDirection: "column",
  gap: "1rem",
  height: "100%",
  overflowY: "auto",
}));

const NavItem = styled("li")`
  position: relative;
  &:hover > div {
    display: flex;
    transform: translateY(0);
    opacity: 1;
  }
`;

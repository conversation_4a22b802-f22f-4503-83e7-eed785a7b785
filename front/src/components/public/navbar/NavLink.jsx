import { Stack, styled, Typography } from "@mui/material";
import LayoutContainer from "../../LayoutContainer";
import { palette } from "@/lib/theme";
import { usePathname } from "next/navigation";
import { generatePath, generateTag } from "@/utils/link.utils";

const StyledStack = styled(Stack)({
  ".subitems": {
    opacity: 0,
    transition: "opacity .1s",
  },
  "&:hover": {
    ".sub": {
      transform: "scaleY(1)",
    },
    ".subitems": {
      opacity: 1,
      transition: "opacity .2s ease-out .2s",
    },
  },
});

const NavLinkItem = styled(Typography)({
  color: palette.black,
  height: 36,
  "&:hover": {
    fontWeight: "bold",
  },
  "&::before": {
    content: "attr(title)",
    display: "block",
    height: 0,
    fontWeight: "bold",
    visibility: "hidden",
    overflow: "hidden",
    userSelect: "none",
    pointerEvents: "none",

    "@media speech": {
      display: "none",
    },
  },
});

const SubMenu = styled(Stack)({
  position: "absolute",
  gap: "20px",
  backgroundColor: "white",
  zIndex: 1000,
  transform: "scaleY(0)",
  transition: "transform .3s ease-out",
  transformOrigin: "top",
  overflow: "hidden",
  width: "100%",
  top: "100%",
  left: "0px",
  flexDirection: "column",
  borderBottom: "1px solid #e0e0e0",
});

export default function NavLink({ item }) {
  const location = usePathname();
  return item?.children?.length ? (
    <StyledStack>
      <NavLinkItem
        className="item"
        variant="body1"
        fontWeight={location === (item?.link?.url || item?.link?.content?.breadcrumb.slugTrail + item?.link?.content?.slug) ? "bold" : "light"}
        component={generateTag(item?.link)}
        href={generatePath(item?.link)}
        title={item?.link?.label || item?.link?.content?.name}
      >
        {item?.link?.label || item?.link?.content?.name}
      </NavLinkItem>
      <SubMenu className="sub">
        <LayoutContainer py={5} gap={2}>
          <Typography
            variant="h2"
            sx={{
              columnCount: item?.children?.length >= 3 ? 3 : item?.children?.length,
              opacity: 0,
            }}
            className="subitems"
          >
            {item?.link?.label || item?.link?.content?.name}
          </Typography>
          <Stack
            display="block"
            sx={{
              columnCount: item?.children?.length >= 3 ? 3 : item?.children?.length,
              opacity: 0,
            }}
            className="subitems"
          >
            {item?.children?.map((subItem, subIndex) => (
              <Stack key={subIndex} gap={1} mb={2}>
                <Typography
                  fontFamily={subItem?.children?.length ? "var(--font-montserrat-bold)" : "var(--font-montserrat-regular)"}
                  component={generateTag(subItem?.link)}
                  href={generatePath(subItem?.link)}
                  sx={{
                    "&:hover": {
                      textDecoration: "underline",
                    },
                  }}
                >
                  {subItem?.link?.label || subItem?.link?.content?.name}
                </Typography>
                {subItem?.children?.map((subItemChild, subIndexChild) => (
                  <Typography
                    key={subIndexChild}
                    fontWeight="light"
                    component={generateTag(subItemChild?.link)}
                    href={generatePath(subItemChild?.link)}
                    sx={{
                      "&:hover": {
                        textDecoration: "underline",
                      },
                    }}
                  >
                    {subItemChild?.link?.label || subItemChild?.link?.content?.name}
                  </Typography>
                ))}
              </Stack>
            ))}
          </Stack>
        </LayoutContainer>
      </SubMenu>
    </StyledStack>
  ) : (
    <NavLinkItem
      variant="body1"
      fontWeight="light"
      component={generateTag(item?.link)}
      href={generatePath(item?.link)}
      title={item?.link?.label || item?.link?.content?.name}
    >
      {item?.link?.label || item?.link?.content?.name}
    </NavLinkItem>
  );
}

"use client";
import { <PERSON>, <PERSON>po<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Stack } from "@mui/material";
import { styled } from "@mui/material/styles";
import LayoutContainer from "../../LayoutContainer";
import Link from "next/link";
import Image from "next/image";
import FormatUtils from "@/utils/format.utils";
import { generatePath } from "@/utils/link.utils";
import banner from "@public/assets/images/banniere-degrade.jpg";

export default function Footer({ data }) {
  const date = new Date();
  const year = date.getFullYear();

  return (
    <FooterContainer style={{ backgroundImage: `url(${banner.src})` }}>
      <LayoutContainer py={6} gap={6}>
        {data.logo?.upload ? (
          <Link href={generatePath(data.logo?.link)}>
            <Image src={FormatUtils.binary(data.logo?.upload?.["@id"])} alt={data.logo?.upload?.alt} height="81" width="81" />
          </Link>
        ) : null}
        <Stack
          direction="row"
          flexWrap={{
            xs: "wrap",
            lg: "nowrap",
          }}
          gap={2}
          justifyContent="flex-start"
        >
          {data?.navs?.map((section, index) => (
            <Stack key={index} flexGrow={1}>
              <Typography
                variant="body1"
                fontWeight="bold"
                pb={1}
                href={generatePath(section.link)}
                component={section.link?.url || section.link?.content ? Link : "p"}
              >
                {section?.link?.label || section?.link?.content?.name}
              </Typography>
              {section.children.map((item, itemIndex) => (
                <Typography variant="body2" key={itemIndex} fontWeight="light" py={0.4}>
                  <FooterLink href={generatePath(item.link)} target={!item.link?.content ? "_blank" : "_self"}>
                    {item?.link?.label || item?.link?.content?.name}
                  </FooterLink>
                </Typography>
              ))}
            </Stack>
          ))}
        </Stack>
      </LayoutContainer>
      <Divider />
      <LayoutContainer
        py={4}
        direction={{
          xs: "column",
          md: "row",
        }}
        gap={{
          xs: 2,
          md: 0,
        }}
        justifyContent="space-between"
        alignItems="center"
      >
        <Stack
          direction={{
            xs: "column",
            md: "row",
          }}
          alignItems="center"
          gap={{
            xs: 1,
            md: 8,
          }}
        >
          <Typography variant="body1">© {year} Agenda Diagnostics</Typography>
          <Stack
            direction={{
              xs: "column",
              md: "row",
            }}
            alignItems="center"
            gap={{
              xs: 1,
              md: 3,
            }}
          >
            {data?.links?.map((sublink, i) => {
              return (
                <FooterLink key={i} href={generatePath(sublink.link)} target={!sublink.link?.content ? "_blank" : "_self"}>
                  {sublink?.link?.label || sublink?.link?.content?.name}
                </FooterLink>
              );
            })}
          </Stack>
        </Stack>
        <Stack direction="row" alignItems="center" gap={1.5}>
          {data?.socials?.map((social, i) => {
            return (
              <IconButton key={i} href={generatePath(social.link)} target="_blank" aria-label="Facebook">
                <Image src={FormatUtils.binary(social.link?.upload?.["@id"])} alt={social.link?.upload?.alt || ""} width={24} height={24} />
              </IconButton>
            );
          })}
        </Stack>
      </LayoutContainer>
    </FooterContainer>
  );
}

const FooterContainer = styled(Box)({
  backgroundSize: "cover",
  backgroundPosition: "center",
  color: "white",
});

const FooterLink = styled(Link)({
  color: "white",
  textDecoration: "none",
  "&:hover": {
    textDecoration: "underline",
  },
});

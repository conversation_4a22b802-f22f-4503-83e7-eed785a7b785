"use client";

import { Stack } from "@mui/material";
import { useCallback } from "react";
import { useDropzone } from "react-dropzone";
import AddPhotoAlternateOutlinedIcon from "@mui/icons-material/AddPhotoAlternateOutlined";
import { useApi } from "@/context/ApiProvider";

export default function Dropzone({ children, onDrop }) {
  const { getRootProps, isDragActive } = useDropzone({
    onDrop,
    noClick: true,
    noKeyboard: true,
    accept: {
      "application/pdf": [".pdf"],
    },
  });

  return (
    <Stack {...getRootProps()} sx={{ position: "relative" }}>
      <Stack
        justifyContent="center"
        alignItems="center"
        sx={(theme) => ({
          position: "fixed",
          display: isDragActive ? "flex" : "none",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          maxHeight: "100dvh",
          zIndex: 100,
          border: isDragActive ? `2px dashed ${theme.palette.primary.main}` : "none",
          borderRadius: 1,
          backgroundColor: `${theme.palette.primary.main}20`,
        })}
      >
        <AddPhotoAlternateOutlinedIcon sx={{ fontSize: 80 }} color="primary" />
      </Stack>
      {children}
    </Stack>
  );
}

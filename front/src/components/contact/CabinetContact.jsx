import { Grid2 } from "@mui/material";
import CardAvatar from "../cards/CardAvatar";
import CardAddress from "../cards/CardAddress";

export default function CabinetContact({ cabinet }) {
  return (
    <Grid2 container spacing={2}>
      <Grid2
        size={{
          xs: 12,
          lg: 3,
        }}
      >
        <CardAvatar contact={cabinet.contact} cabinet={cabinet} />
      </Grid2>
      <Grid2
        size={{
          xs: 12,
          lg: 9,
        }}
      >
        <CardAddress cabinet={cabinet} />
      </Grid2>
    </Grid2>
  );
}

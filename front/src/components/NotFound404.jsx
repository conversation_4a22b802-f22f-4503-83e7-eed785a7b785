import { Button, Stack, Typography } from "@mui/material";
import Link from "next/link";

export default function NotFound404() {
  return (
    <Stack justifyContent="center" alignItems="center" height="60dvh" width="100%" py={10}>
      <Typography
        variant="h1"
        textAlign="center"
        color="primary"
        lineHeight={0.6}
        sx={{
          fontSize: {
            xs: "80px",
            sm: "100px",
            md: "150px",
            lg: "200px",
          },
        }}
      >
        404
      </Typography>
      <Stack direction="row" bgcolor="#0094D3" p={{ xs: 2, md: 4 }} borderRadius={2} color="#FFF" justifyContent="center" alignItems="center" gap={5}>
        <Typography variant="h2" textTransform="uppercase">
          Page introuvable
        </Typography>
        <Button LinkComponent={Link} href="/" color="white">
          Revenir à l&apos;accueil
        </Button>
      </Stack>
    </Stack>
  );
}

import FormatUtils from "@/utils/format.utils";
import { Stack } from "@mui/material";

// TODO: Call footer for this ? Copy pasted objects from footer call
const facebookUpload = {
  "@type": "Upload",
  "@id": "/api/uploads/2fee6f5d-2d57-43b0-bcb1-b1ba8144e5cb",
  name: "facebook-logo.png",
  alt: null,
  filename: "facebook-logo.png",
  type: "image/png",
  size: 3208,
  isPublic: false,
  uuid: "2fee6f5d-2d57-43b0-bcb1-b1ba8144e5cb",
  createdAt: "2024-11-25T09:54:38+00:00",
  updatedAt: null,
};

const linkedinUpload = {
  "@type": "Upload",
  "@id": "/api/uploads/f9b4b30b-86a6-478d-8cc2-8e23726b4a45",
  name: "linkedin-logo.png",
  alt: null,
  filename: "linkedin-logo.png",
  type: "image/png",
  size: 3549,
  isPublic: false,
  uuid: "f9b4b30b-86a6-478d-8cc2-8e23726b4a45",
  createdAt: "2024-11-25T09:55:29+00:00",
  updatedAt: null,
};

export default function AgencySocials({ agency }) {
  const socials = ["facebookLink", "linkedinLink"];

  if (!agency?.facebookLink && !agency?.linkedinLink) {
    return null;
  }

  return (
    <Stack direction="row" gap={1}>
      {socials.map((social, index) => {
        if (!agency?.[social]) return null;

        return (
          <a key={index} href={agency[social]} target="_blank" rel="noreferrer">
            {social === "facebookLink" ? (
              <img src={FormatUtils.binary(facebookUpload["@id"])} alt="Facebook" width="32" height="32" />
            ) : (
              <img src={FormatUtils.binary(linkedinUpload["@id"])} alt="Linkedin" width="32" height="32" />
            )}
          </a>
        );
      })}
    </Stack>
  );
}

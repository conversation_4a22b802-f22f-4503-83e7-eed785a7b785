"use client";
import { ArrowBack } from "@mui/icons-material";
import { Button } from "@mui/material";
import { useRouter } from "next/navigation";

export default function BackButton({ url, children, withIcon = true, sx, ...rest }) {
  const router = useRouter();
  return (
    <Button
      variant="text"
      startIcon={withIcon ? <ArrowBack sx={{ color: "black" }} /> : null}
      onClick={() => (url ? router.push(url) : router.back())}
      sx={{
        alignSelf: "flex-start",
        color: "black",
        ...sx,
      }}
      {...rest}
    >
      {children}
    </Button>
  );
}

import FormatUtils from "@/utils/format.utils";
import { Stack } from "@mui/material";
import Image from "next/image";

export const getYouTubeEmbedUrl = (url) => {
  if (!url) return "";

  const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
  const match = url.match(regExp);

  const regExpShorts = /^.*(youtu.*be.*)\/(watch\?v=|embed\/|v|shorts|)(.*?((?=[&#?])|$)).*/;
  const matchShorts = url.match(regExpShorts);

  if (match && match[2].length === 11) {
    return `https://www.youtube.com/embed/${match[2]}`;
  }
  if (matchShorts && matchShorts[3].length === 11) {
    return `https://www.youtube.com/embed/${matchShorts[3]}`;
  }

  return null;
};

export default function ImageOrVideo({ media, video }) {
  const embedUrl = getYouTubeEmbedUrl(video);

  if (media) {
    return (
      <Stack>
        <Image
          src={FormatUtils.binary(media?.["@id"])}
          alt={media?.alt}
          width={0}
          height={0}
          sizes="100vw"
          style={{
            width: "100%",
            height: "auto",
            maxWidth: "100%",
          }}
        />
      </Stack>
    );
  }

  return (
    <Stack>
      {embedUrl ? (
        <iframe
          id="player"
          type="text/html"
          src={embedUrl}
          style={{ minHeight: "250px", aspectRatio: "16/9", maxWidth: "100%" }}
          allowFullScreen
          title="YouTube Video Player"
        />
      ) : (
        "Lien invalide"
      )}
    </Stack>
  );
}

"use client";

import FormatUtils from "@/utils/format.utils";
import { But<PERSON>, Stack } from "@mui/material";
import { useRouter, useSearchParams } from "next/navigation";
import Input from "./inputs/Input";
import TranslationUtils from "@/utils/translation.utils";

export default function QSearch() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const submit = (e) => {
    e?.preventDefault();
    const params = {
      ...searchParams,
    };
    if (e?.target?.search?.value) {
      params.q = e.target.search.value;
    }
    router.push(FormatUtils.formatUrl(params));
  };

  const clear = (e) => {
    const value = e.target.value;
    if (value?.length === 0 && !("data" in e.nativeEvent)) {
      submit();
    }
  };

  return (
    <Stack component="form" direction="row" gap={2} width="50%" alignItems="flex-end" onSubmit={submit}>
      <Input
        onInput={clear}
        label={TranslationUtils.get("global.search")}
        fullWidth
        name="search"
        type="search"
        defaultValue={searchParams.get("q")}
      />
      <Button type="submit">{TranslationUtils.get("global.search")}</Button>
    </Stack>
  );
}

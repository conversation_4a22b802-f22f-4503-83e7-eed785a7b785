import { BLOC_TYPES } from "@/enums/BLOC_TYPES";
import BlocBanner from "@/components/blocks/BlocBanner";
import BlocImageBanner from "@/components/blocks/BlocImageBanner";
import BlockCards from "@/components/blocks/BlockCards";
import BlockDoubleBlock from "@/components/blocks/BlockDoubleBlock";
import BlockDoubleText from "@/components/blocks/BlockDoubleText";
import BlockFiles from "@/components/blocks/BlockFiles";
import BlockFindMap from "@/components/blocks/BlockFindMap";
import BlockFormNewsLetter from "@/components/blocks/BlockFormNewsLetter";
import BlockQuote from "@/components/blocks/BlockQuote/BlockQuote";
import BlockSurfaceComparision from "@/components/blocks/BlockSurfaceComparision";
import BlockTestimonials from "@/components/blocks/BlockTestimonials";
import BlockTextMedia from "@/components/blocks/BlockTextMedia";
import BlockCategories from "@/components/blocks/BlockCategories";
import BlockMap from "@/components/blocks/BlockMap/BlockMap";
import BlockNewsPreview from "@/components/blocks/BlockNewsPreview";
import BlockNews from "@/components/blocks/BlockNews";
import BlockFindPartners from "@/components/blocks/BlockFindPartners";
import BlockMostViewed from "@/components/blocks/BlockMostViewed";
import BlockCarousel from "@/components/blocks/BlockCarousel/BlockCarousel";
import { Stack } from "@mui/material";
import { createElement } from "react";
import BlockText from "@/components/blocks/BlockText";
import BlockJob from "../blocks/BlockJob";
import BlockCardSummary from "../blocks/BlockCardSummary";
import BlockQuoteSingle from "../blocks/BlockQuoteSingle";
import BlockDiagnostic from "../blocks/BlockDiagnostic";
import BlockReview from "../blocks/BlockReview";
import BlockProQuote from "../blocks/BlockProQuote";
import BlockFormJob from "../blocks/BlockFormJob";
import BlockFormInfos from "../blocks/BlockFormInfos";
import BlockFormGrandCompte from "../blocks/BlockFormGrandCompte";
import BlockNewsletter from "../blocks/BlockNewsletter";
import BlockFormDiagMag from "../blocks/BlockFormDiagMag";
import BlockFormDiagAssist from "../blocks/BlockFormDiagAssist";

export default function BlocRender({ block, isBuilder }) {
  const componentMap = {
    [BLOC_TYPES.TEXT]: BlockText,
    [BLOC_TYPES.IMAGE_BANNER]: BlocImageBanner,
    [BLOC_TYPES.BANNER]: BlocBanner,
    [BLOC_TYPES.TEXT_MEDIA]: BlockTextMedia,
    [BLOC_TYPES.BLOCK_DOUBLE_TEXT]: BlockDoubleText,
    [BLOC_TYPES.BLOCK_FILES]: BlockFiles,
    [BLOC_TYPES.BLOCK_CARDS]: BlockCards,
    [BLOC_TYPES.BLOCK_CARD_SUMMARY]: BlockCardSummary,
    [BLOC_TYPES.BLOCK_SURFACE_COMPARISION]: BlockSurfaceComparision,
    [BLOC_TYPES.BLOCK_DOUBLE_BLOCK]: BlockDoubleBlock,
    [BLOC_TYPES.BLOCK_TESTIMONIALS]: BlockTestimonials,
    [BLOC_TYPES.BLOCK_QUOTE]: BlockQuote,
    [BLOC_TYPES.BLOCK_QUOTE_SINGLE]: BlockQuoteSingle,
    [BLOC_TYPES.BLOCK_CATEGORIES]: BlockCategories,
    [BLOC_TYPES.BLOCK_FIND_MAP]: BlockFindMap,
    [BLOC_TYPES.BLOCK_MAP]: BlockMap,
    [BLOC_TYPES.BLOCK_NEWS_PREVIEW]: BlockNewsPreview,
    [BLOC_TYPES.BLOCK_NEWS]: BlockNews,
    [BLOC_TYPES.BLOCK_FIND_PARTNERS]: BlockFindPartners,
    [BLOC_TYPES.BLOCK_MOST_VIEWED]: BlockMostViewed,
    [BLOC_TYPES.BLOCK_CAROUSEL]: BlockCarousel,
    [BLOC_TYPES.BLOCK_JOB]: BlockJob,
    [BLOC_TYPES.BLOCK_DIAGNOSTIC]: BlockDiagnostic,
    [BLOC_TYPES.BLOCK_REVIEW]: BlockReview,

    [BLOC_TYPES.BLOCK_PRO_QUOTE]: BlockProQuote,
    [BLOC_TYPES.BLOCK_FORM_NEWSLETTER]: BlockFormNewsLetter,
    [BLOC_TYPES.BLOCK_FORM_JOB]: BlockFormJob,
    [BLOC_TYPES.BLOCK_FORM_INFOS]: BlockFormInfos,
    [BLOC_TYPES.BLOCK_FORM_GRAND_COMPTE]: BlockFormGrandCompte,
    [BLOC_TYPES.BLOCK_NEWSLETTER]: BlockNewsletter,
    [BLOC_TYPES.BLOCK_FORM_DIAG_MAG]: BlockFormDiagMag,
    [BLOC_TYPES.BLOCK_FORM_DIAG_ASSIST]: BlockFormDiagAssist,
  };

  const component = componentMap[block.type] || null;

  const mt = block.parameters.find((e) => e.type === "mt")?.value || 0;
  const mb = block.parameters.find((e) => e.type === "mb")?.value || 0;

  return (
    <Stack mt={mt + "px"} mb={mb + "px"} position="relative">
      {component ? createElement(component, { block: block, isBuilder }) : null}
    </Stack>
  );
}

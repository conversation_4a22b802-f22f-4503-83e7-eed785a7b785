"use client";

import React from "react";
import Input from "@/components/inputs/Input";
import TranslationUtils from "@/utils/translation.utils";
import { Button, Paper, Stack, Typography } from "@mui/material";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import { useRouter } from "next/navigation";

export default function FormSettings({ data }) {
  const { put } = useApi();
  const { add } = useSnack();
  const router = useRouter();

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await put("/setting", {
        tagManager: e.target.tagManager.value,
        searchLink: e.target.searchLink.value,
        quoteLink: e.target.quoteLink.value,
        html: e.target.html.value,
        js: e.target.js.value,
      });
      add("success", "Les paramètres ont bien été modifiés !");
      router.refresh();
    } catch (error) {
      console.error("Settings update failed:", error);
      add("error", "Une erreur est survenue lors de la modification des paramètres");
    }
  };

  return (
    <Stack component={Paper} p={4}>
      <Typography variant="h1">{TranslationUtils.get("navs.settings")}</Typography>
      <Stack component="form" gap={2} mt={2} onSubmit={handleSubmit}>
        <Input defaultValue={data.tagManager} name="tagManager" label="Google tag manager ID" fullWidth />
        <Input defaultValue={data.searchLink} name="searchLink" label="Redirection de la recherche" fullWidth />
        <Input defaultValue={data.quoteLink} name="quoteLink" label="Lien de la page devis" fullWidth />
        <Input defaultValue={data.html} name="html" label="Balise HTML" fullWidth multiline rows={4} />
        <Input defaultValue={data.js} name="js" label="Balise JS" fullWidth multiline rows={4} />
        <Stack direction="row" justifyContent="flex-end">
          <Button type="submit">{TranslationUtils.get("global.save")}</Button>
        </Stack>
      </Stack>
    </Stack>
  );
}

import { Divider, Paper, Stack } from "@mui/material";
import Input from "@/components/inputs/Input";
import { useEffect, useState } from "react";
import TranslationUtils from "@/utils/translation.utils";

export default function PagesForm({ content, onChange }) {
  const [item, setItem] = useState(content);

  useEffect(() => {
    setItem(content);
  }, [content]);

  const handleChange = (name, value) => {
    setItem((prev) => ({ ...prev, [name]: value }));
    onChange(name, value);
  };
  return (
    <Stack component={Paper} p={3}>
      <Stack gap={1}>
        <Input label={TranslationUtils.get("contents.name")} fullWidth name="name" value={item?.name || ""} onChange={(e) => handleChange("name", e.target.value)} />
        <Input label={TranslationUtils.get("contents.slug")} fullWidth name="slug" value={item?.slug || ""} onChange={(e) => handleChange("slug", e.target.value)} />
        <Divider>{TranslationUtils.get("contents.seo")}</Divider>
        <Input
          label={TranslationUtils.get("contents.seo_title")}
          fullWidth
          name="seo_title"
          value={item?.seo_title || ""}
          onChange={(e) => handleChange("seo_title", e.target.value)}
        />
        <Input
          label={TranslationUtils.get("contents.seo_description")}
          fullWidth
          name="seo_desciption"
          value={item?.seo_description || ""}
          multiline
          rows={4}
          onChange={(e) => handleChange("seo_description", e.target.value)}
        />
      </Stack>
    </Stack>
  );
}

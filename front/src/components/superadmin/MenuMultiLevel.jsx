import { useState } from "react";
import { Button, <PERSON>u, <PERSON>uI<PERSON>, Stack } from "@mui/material";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import { usePathname } from "next/navigation";
import Link from "next/link";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";

export default function MenuMultiLevel({ link }) {
  const pathname = usePathname();
  const [anchorEl, setAnchorEl] = useState(null);
  const [subMenuAnchorEl, setSubMenuAnchorEl] = useState(null);
  const [subMenuId, setSubMenuId] = useState(null);

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleSubMenuOpen = (event, id) => {
    setSubMenuId(id);
    setSubMenuAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
    setSubMenuAnchorEl(null);
    setSubMenuId(null);
  };

  const handleSubMenuClose = () => {
    setSubMenuAnchorEl(null);
    setSubMenuId(null);
  };

  return (
    <div>
      <Stack
        direction="row"
        onClick={handleMenuOpen}
        component={Button}
        variant="text"
        color="white"
        endIcon={<KeyboardArrowDownIcon />}
        sx={{
          textDecoration:
            link.items?.some((item) => item.link === pathname) || link?.items.some((item) => item.children?.some((child) => child.link === pathname))
              ? "underline"
              : "none",
        }}
      >
        {link.label}
      </Stack>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        MenuListProps={{
          onMouseLeave: handleClose,
        }}
        slotProps={{
          paper: {
            square: true,
          },
        }}
      >
        {link.items.map((item) => (
          <Stack key={item.label}>
            <MenuItem
              component={item.link ? Link : "div"}
              href={item.link}
              onClick={item.children ? (e) => handleSubMenuOpen(e, item.label) : null}
              sx={{
                width: "100%",
                justifyContent: "space-between",
                fontFamily:
                  item.link === pathname || item.children?.some((child) => child.link === pathname)
                    ? "var(--font-montserrat-bold), sans-serif"
                    : "var(--font-montserrat-regular), sans-serif",
              }}
            >
              {item.label} {item.children && <ChevronRightIcon />}
            </MenuItem>
            {item.children ? (
              <Menu
                anchorEl={subMenuAnchorEl}
                open={Boolean(subMenuAnchorEl) && subMenuId === item.label}
                onClose={handleSubMenuClose}
                MenuListProps={{
                  onMouseLeave: handleSubMenuClose,
                }}
                anchorOrigin={{
                  vertical: "top",
                  horizontal: "right",
                }}
                transformOrigin={{
                  vertical: "top",
                  horizontal: "left",
                }}
                slotProps={{
                  paper: {
                    square: true,
                  },
                }}
              >
                {item.children.map((child) => (
                  <MenuItem
                    key={child.label}
                    component={Link}
                    href={child.link}
                    onClick={handleClose}
                    sx={{
                      fontFamily: child.link === pathname ? "var(--font-montserrat-bold), sans-serif" : "var(--font-montserrat-regular), sans-serif",
                    }}
                  >
                    {child.label}
                  </MenuItem>
                ))}
              </Menu>
            ) : null}
          </Stack>
        ))}
      </Menu>
    </div>
  );
}

import { Stack, styled, Typography } from "@mui/material";
import BlocMenu from "./BlocMenu";
import { blockIsEditable } from "@/utils/builder.utils";
import { BLOC_LABEL } from "@/enums/BLOC_LABEL";

const MainContainer = styled(Stack, {
  shouldForwardProp: (prop) => prop !== "dangerZone",
})(({ theme, dangerZone }) => ({
  position: "relative",
  "&:hover": {
    ".menu": {
      display: "flex",
    },
    outline: "1px solid " + theme.palette[dangerZone ? "error" : "primary"].light,
  },
  outline: "1px solid transparent",
  width: "100%",
  overflow: "hidden",
}));

export default function BlockContainer({ name, children, onClick, onDelete, block, blocks, index, onMove }) {
  return (
    <MainContainer direction="row">
      <Stack width={1}>
        <Typography variant="h5" color="grey">
          {name}
        </Typography>
        {children}
      </Stack>
      <BlocMenu
        label={block.name || BLOC_LABEL[block.type]}
        onEdit={blockIsEditable(block.type) ? onClick : null}
        onDelete={() => onDelete(block.uuid)}
        block={block}
        index={index}
        blocks={blocks}
        onMove={onMove}
      />
    </MainContainer>
  );
}

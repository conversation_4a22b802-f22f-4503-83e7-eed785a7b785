"use client";

import { useSnack } from "@/context/SnackProvider";
import usePositions from "@/hooks/usePositions";
import { useApi } from "@/context/ApiProvider";
import { Add } from "@mui/icons-material";
import { IconButton, Stack, Typography } from "@mui/material";
import { createTree, getFlatTree } from "@/utils/tree.utils";
import { SortableTree } from "dnd-kit-sortable-tree";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import React, { useEffect, useState } from "react";
import ModalName from "./ModalName";
import TreeContent from "./TreeContent";
import TranslationUtils from "@/utils/translation.utils";
import Input from "../inputs/Input";
import { useContent } from "@/context/ContentProvider";

export default function PagesTree() {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();
  const { contents, fetchContents } = useContent();
  const [openModalName, setOpenModalName] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [items, setItems] = useState(createTree(contents));
  const [search, setSearch] = useState("");
  const { put } = useApi();
  const { handleChangePosition } = usePositions();
  const { add } = useSnack();

  const onRefresh = (uuid) => {
    if (uuid) {
      handleClickItem(uuid);
    }
    void fetchContents();
  };

  useEffect(() => {
    setItems(createTree(contents));
  }, [contents]);

  useEffect(() => {
    setItems(createTree(contents.filter((content) => content.name.toLowerCase().includes(search.toLowerCase()))));
  }, [search]);

  const onChange = async (it, event) => {
    try {
      if (event.type !== "dropped") {
        return;
      }

      handleChangePosition(getFlatTree(it));

      if (event.draggedItem === event.droppedOnItem) {
        return;
      }

      if (event.droppedToParent) {
        await put(`/contents/${event.draggedItem.uuid}`, {
          ...event.draggedItem,
          parent: event.droppedToParent["@id"],
        });
      }

      if (event.draggedFromParent && !event.droppedToParent) {
        await put(`/contents/${event.draggedItem.uuid}`, {
          ...event.draggedItem,
          parent: null,
        });
      }
      setItems(it);
    } catch (error) {
      add("error", "Une erreur est survenue pendant l'organisation des pages");
      console.log(error);
    }
  };

  const handleClickItem = (id) => {
    const current = new URLSearchParams(Array.from(searchParams.entries()));
    current.set("page", id);
    router.push(`${pathname}?${current.toString()}`);
  };

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <Stack gap={1} height="100%">
      <Stack direction="row" justifyContent="space-between" alignItems="center">
        <Typography variant="h6">{TranslationUtils.get("contents._plural")}</Typography>
        <IconButton onClick={() => setOpenModalName(true)}>
          <Add />
        </IconButton>
      </Stack>
      <Input fullWidth placeholder="Rechercher" name="search" onChange={(e) => setSearch(e.target.value)} />
      <Stack height="100%" overflow="auto">
        <SortableTree
          items={items}
          onItemsChanged={onChange}
          TreeItemComponent={React.forwardRef(function TreeItem(props, ref) {
            return <TreeContent {...props} defaultRef={ref} onClick={handleClickItem} />;
          })}
          sortableProps={{
            animateLayoutChanges: function noRefCheck() {},
            ghost: false,
          }}
        />
      </Stack>
      <ModalName open={openModalName} onClose={() => setOpenModalName(false)} onRefresh={onRefresh} position={items.length} />
    </Stack>
  );
}

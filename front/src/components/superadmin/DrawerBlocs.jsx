import { useEffect, useState } from "react";
import { DrawerBlocsStyled, DrawerHeader, Preview } from "./styles";
import ListBloc from "./ListBloc";
import FormBlocContainer from "@/components/forms/formBlocks/FormBlocContainer";
import { Paper, Stack, Typography } from "@mui/material";
import { BLOC_LABEL } from "@/enums/BLOC_LABEL";
import { BLOC_ICONS } from "@/enums/BLOC_ICONS";
import { BLOC_PREVIEW } from "@/enums/BLOC_PREVIEW";
import PreviewBlock from "../previews/PreviewBlock";
import Image from "next/image";
import theme from "@/lib/theme";

export default function DrawerBlocs({ open, onClose, addStack, defaultSelected }) {
  const [selected, setSelected] = useState(defaultSelected);
  const [hover, setHover] = useState(null);

  const addBloc = (bloc) => {
    addStack(bloc);
    onClose();
  };

  useEffect(() => {
    if (!open) {
      setSelected(null);
      setHover(null);
    }
  }, [open]);

  if (defaultSelected && !selected) {
    setSelected(defaultSelected);
  }

  return (
    <>
      {hover && !selected ? (
        <Preview component={Paper} p={2} gap={2} overflow="hidden">
          <Stack width="100%" height={150} bgcolor="grey.light" p={1} zIndex={10000}>
            {BLOC_PREVIEW?.[hover] ? <PreviewBlock image={BLOC_PREVIEW?.[hover]} /> : null}
          </Stack>
          <Stack direction="row" gap={1} alignItems="center">
            <Image src={BLOC_ICONS[hover].src} alt={BLOC_LABEL[hover]} width={20} height={20} />
            <Typography variant="body1">{BLOC_LABEL[hover]}</Typography>
          </Stack>
        </Preview>
      ) : null}
      <DrawerBlocsStyled
        open={open}
        anchor="right"
        slotProps={{
          backdrop: {
            invisible: true,
          },
        }}
        onClose={onClose}
        width={"50%"}
      >
        <DrawerHeader />
        <Stack borderLeft={`1px solid ${theme.palette.grey.main}`} overflow="auto" height="100%">
          {!selected ? (
            <ListBloc onSuccess={(bloc) => setSelected(bloc)} onClose={onClose} addBloc={addBloc} setHover={setHover} />
          ) : (
            <FormBlocContainer
              addBloc={addBloc}
              bloc={selected}
              onClose={() => {
                defaultSelected ? onClose() : setSelected(null);
                setHover(null);
              }}
            />
          )}
        </Stack>
      </DrawerBlocsStyled>
    </>
  );
}

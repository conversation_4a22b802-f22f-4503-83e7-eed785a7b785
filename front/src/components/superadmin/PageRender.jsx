import { Box } from "@mui/material";
import BlocRender from "./BlocRender";
import Breads from "../Breads";

export default function PageRender({ blocks }) {
  return (
    <Box position="relative">
      <Box>
        {blocks?.map((block) => (
          <BlocRender key={block.uuid} block={block} />
        ))}
      </Box>
      <Box position="absolute" top={0} left={0} width="100%" color="white">
        <Breads />
      </Box>
    </Box>
  );
}

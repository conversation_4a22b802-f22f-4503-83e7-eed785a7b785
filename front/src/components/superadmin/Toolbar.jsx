import TranslationUtils from "@/utils/translation.utils";
import { Button, FormLabel, Paper, Stack, Switch, Typography } from "@mui/material";
import ButtonCopy from "../ButtonCopy";

export default function Toolbar({ title, duplicate, defaultActivate, disabled, disabledSave, onValidate, onSave, onDelete, onPreview, children }) {
  return (
    <Stack direction="row" gap={1} justifyContent="space-between" alignItems="center" component={Paper} p={2}>
      <Stack>
        <Typography variant="h2">{title}</Typography>
      </Stack>
      <Stack direction="row" gap={1}>
        {children}
        {onValidate ? (
          <Stack direction="row" alignItems="center">
            <FormLabel disabled={disabled} htmlFor="status-switch">
              Publier
            </FormLabel>
            <Switch id={"status-switch"} checked={defaultActivate} color="success" onChange={onValidate} disabled={disabled} />
          </Stack>
        ) : null}
        {duplicate ? (
          <ButtonCopy  variant="outlined" color="secondary" onClick={duplicate} disabled={disabled}>
            Dupliquer
          </ButtonCopy>
        ) : null}
        {onPreview ? (
          <Button onClick={onPreview} color="primary" variant="outlined" disabled={disabled}>
            {TranslationUtils.get("global.preview")}
          </Button>
        ) : null}
        {onDelete ? (
          <Button disabled={disabled} color="secondary" onClick={onDelete}>
            {TranslationUtils.get("contents._delete")}
          </Button>
        ) : null}
        {onSave ? (
          <Button onClick={onSave} color="primary" disabled={disabled || disabledSave}>
            {TranslationUtils.get("global.save")}
          </Button>
        ) : null}
      </Stack>
    </Stack>
  );
}

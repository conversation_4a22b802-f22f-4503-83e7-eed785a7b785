import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Slide, <PERSON><PERSON>, Tab, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import { CardBloc } from "./styles";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import { BLOC_TYPES } from "@/enums/BLOC_TYPES";
import { BLOC_LABEL } from "@/enums/BLOC_LABEL";
import { BLOC_FORMS } from "@/enums/BLOC_FORMS";
import { BLOC_ICONS } from "@/enums/BLOC_ICONS";
import { useApi } from "@/context/ApiProvider";
import Image from "next/image";
import TranslationUtils from "@/utils/translation.utils";
import { v4 as uuidv4 } from "uuid";
import { BLOC_SUBTYPES, BLOC_SUBTYPES_LABEL, BLOC_TYPES_SUBTYPES } from "@/enums/BLOC_SUBTYPES";

export default function ListBloc({ onSuccess, onClose, addBloc, setHover }) {
  const [search, setSearch] = useState("");
  const [tabValue, setTabValue] = useState(0);
  const [favoriteBlocks, setFavoriteBlocks] = useState([]);
  const { get } = useApi();

  const getDefaultBloc = (type) => ({
    name: null,
    type,
    parameters: [],
    uuid: uuidv4(),
  });

  const getFavoriteBlocks = async () => {
    try {
      const response = await get("/contents/blocks/favorite");
      setFavoriteBlocks(response.member);
    } catch (error) {
      console.error(error);
      return [];
    }
  };

  useEffect(() => {
    void getFavoriteBlocks();
  }, []);

  return (
    <Stack height="100%">
      <Slide in direction="right">
        <Stack p={2}>
          <Stack direction="row" gap={1} alignItems="center">
            <IconButton onClick={onClose}>
              <ChevronLeftIcon />
            </IconButton>
            <TextField placeholder={TranslationUtils.get("contents.blocks.search")} onChange={(e) => setSearch(e.target.value)} fullWidth />
          </Stack>
          <Tabs value={tabValue} onChange={(e, value) => setTabValue(value)} variant="fullWidth">
            <Tab label={TranslationUtils.get("contents.blocks._")} value={0} />
            <Tab label={TranslationUtils.get("contents.blocks._favorite")} value={1} />
          </Tabs>
          {Object.values(BLOC_SUBTYPES).map((e) => {
            return (
              <Stack key={e} pt={1}>
                <Typography variant="bold">{BLOC_SUBTYPES_LABEL[e]}</Typography>
                <Stack
                  display={tabValue !== 0 ? "none" : "grid"}
                  gridTemplateColumns="repeat(auto-fill,minmax(150px,1fr))"
                  pt={1}
                  gap={1}
                  overflow="auto"
                >
                  {Object.values(BLOC_TYPES)
                    .filter((key) => BLOC_LABEL[key]?.toLowerCase().includes(search.toLowerCase()))
                    .filter((key) => BLOC_TYPES_SUBTYPES[key] === e)
                    .map((key, index) => (
                      <CardBloc
                        key={index}
                        onMouseEnter={() => setHover(key)}
                        onMouseLeave={() => setHover(null)}
                        onClick={() => (BLOC_FORMS[key] ? onSuccess(getDefaultBloc(key)) : addBloc(getDefaultBloc(key)))}
                      >
                        <Image src={BLOC_ICONS[key]?.src} alt={BLOC_LABEL?.[key]} width={20} height={20} />
                        <Typography variant="body2">{BLOC_LABEL[key]}</Typography>
                      </CardBloc>
                    ))}
                </Stack>
              </Stack>
            );
          })}
          <Stack display={tabValue !== 1 ? "none" : "grid"} gridTemplateColumns="repeat(auto-fill,minmax(150px,1fr))" pt={1} gap={1} overflow="auto">
            {favoriteBlocks
              .filter((bloc) => bloc.name.toLowerCase().includes(search.toLowerCase()))
              .map((bloc, index) => (
                <CardBloc key={index} onClick={() => addBloc(bloc)}>
                  <Image src={BLOC_ICONS[bloc.type].src} alt={BLOC_LABEL[bloc.type]} width={20} height={20} />
                  <Typography variant="body2">{bloc.name}</Typography>
                </CardBloc>
              ))}
          </Stack>
        </Stack>
      </Slide>
    </Stack>
  );
}

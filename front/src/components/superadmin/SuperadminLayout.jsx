"use client";

import { useUser } from "@/context/UserProvider";
import ROUTES from "@/enums/ROUTES";
import TranslationUtils from "@/utils/translation.utils";
import { AppBar, Avatar, Button, Menu, MenuItem, Stack } from "@mui/material";
import Toolbar from "@mui/material/Toolbar";
import Typography from "@mui/material/Typography";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import MenuMultiLevel from "./MenuMultiLevel";

export default function AdminLayout({ children }) {
  const [anchorEl, setAnchorEl] = useState(null);
  const router = useRouter();
  const { user } = useUser();

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const CMS_LINKS = {
    label: "CMS",
    items: [
      { label: TranslationUtils.get("navs.header"), link: ROUTES.HEADER },
      { label: TranslationUtils.get("navs.footer"), link: ROUTES.FOOTER },
      {
        label: TranslationUtils.get("navs.builder"),
        link: ROUTES.BUILDER,
      },
    ],
  };

  const STORE_LINKS = {
    label: TranslationUtils.get("navs.manage"),
    items: [
      {
        label: TranslationUtils.get("agencies._plural"),
        children: [
          {
            label: TranslationUtils.get("agencies._plural"),
            link: ROUTES.SUPERADMIN_STORES,
          },
          {
            label: TranslationUtils.get("franchises._plural"),
            link: ROUTES.SUPERADMIN_FRANCHISES,
          },
          {
            label: TranslationUtils.get("agencies.posts._plural"),
            link: ROUTES.SUPERADMIN_STORES_ACTUS,
          },
          {
            label: TranslationUtils.get("areas._plural"),
            link: ROUTES.CANTONS,
          },
          {
            label: TranslationUtils.get("departments._plural"),
            link: ROUTES.DEPARTEMENTS,
          },
          {
            label: TranslationUtils.get("cities._plural"),
            link: ROUTES.COMMUNES,
          },
        ],
      },
      {
        label: TranslationUtils.get("estimates._plural"),
        children: [
          {
            label: TranslationUtils.get("pro_estimates._plural"),
            link: ROUTES.SUPERADMIN_PRO_ESTIMATES,
          },
          {
            label: TranslationUtils.get("particulier_estimates._plural"),
            link: ROUTES.SUPERADMIN_PARTICULIER_ESTIMATES,
          },
        ],
      },
      {
        label: TranslationUtils.get("jobs._plural"),
        children: [
          {
            label: TranslationUtils.get("jobs._plural"),
            link: ROUTES.SUPERADMIN_JOBS,
          },
          {
            label: TranslationUtils.get("jobApplications._plural"),
            link: ROUTES.SUPERADMIN_JOB_APPLICATIONS,
          },
          {
            label: TranslationUtils.get("jobApplications._free"),
            link: ROUTES.SUPERADMIN_JOB_APPLICATIONS_FREE,
          },
        ],
      },
    ],
  };

  const MANAGEMENT_LINKS = {
    label: "Administration",
    items: [
      {
        label: TranslationUtils.get("posts._plural"),
        link: ROUTES.SUPERADMIN_ACTUS,
      },
      {
        label: TranslationUtils.get("partners._plural"),
        link: ROUTES.SUPERADMIN_PARTNERS,
      },
      {
        label: TranslationUtils.get("users._plural"),
        link: ROUTES.SUPERADMIN_USERS,
      },
      {
        label: TranslationUtils.get("emails._plural"),
        link: ROUTES.SUPERADMIN_EMAILS,
      },
      {
        label: TranslationUtils.get("medias._plural"),
        link: ROUTES.SUPERADMIN_MEDIAS,
      },
      {
        label: TranslationUtils.get("navs.settings"),
        link: ROUTES.SUPERADMIN_SETTINGS,
      },
    ],
  };

  const LINKS = [CMS_LINKS, STORE_LINKS, MANAGEMENT_LINKS];

  return (
    <Stack pt="64px" height="100dvh" overflow="hidden">
      <AppBar position="fixed" sx={{ zIndex: 9 }}>
        <Toolbar sx={{ px: 2 }}>
          <Stack width="100%" direction="row" justifyContent="space-between" alignItems="center">
            <Stack direction="row" gap={1} alignItems="center" spacing={4}>
              <Typography variant="h6">Administration</Typography>
              <Stack direction="row" gap={2}>
                {LINKS.map((link) => (
                  <MenuMultiLevel key={link.label} link={link} />
                ))}
              </Stack>
            </Stack>
            <Stack direction="row" gap={1} alignItems="center" component={Button} variant="text" onClick={handleClick}>
              <Avatar sx={{ width: 34, height: 34 }} />
              <Typography color="white" variant="body1">
                {user?.firstname} {user?.lastname}
              </Typography>
            </Stack>
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleClose}
              slotProps={{
                paper: {
                  square: true,
                },
              }}
            >
              <MenuItem component={Link} href="/" target="_blank">
                {TranslationUtils.get("global.openSite")}
              </MenuItem>
              <MenuItem component={Link} href={ROUTES.ACCOUNT}>
                {TranslationUtils.get("navs.account")}
              </MenuItem>
              <MenuItem onClick={() => router.push(ROUTES.LOGOUT)}>{TranslationUtils.get("global.logout")}</MenuItem>
            </Menu>
          </Stack>
        </Toolbar>
      </AppBar>
      <Stack p={2} overflow="auto">
        {children}
      </Stack>
    </Stack>
  );
}

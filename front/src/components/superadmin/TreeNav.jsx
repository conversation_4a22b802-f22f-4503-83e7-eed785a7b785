"use client";

import { Accordion, AccordionDetails, AccordionSummary, Button, Stack, Typography } from "@mui/material";
import { ExpandMore } from "@mui/icons-material";
import { SimpleTreeItemWrapper } from "dnd-kit-sortable-tree";
import Input from "@/components/inputs/Input";
import InputMedias from "@/components/inputs/InputMedias";
import SelectPage from "@/components/inputs/SelectPage";
import Image from "next/image";
import FormatUtils from "@/utils/format.utils";

export default function TreeNav({ onChange, defaultRef, withImage = false, removeLink, ...props }) {
  const item = props.item;
  return (
    <SimpleTreeItemWrapper collapsed={true} {...props} ref={defaultRef} ghost={false}>
      <Accordion
        onChange={(e) => {
          e.stopPropagation();
        }}
        sx={{
          width: "100%",
          minWidth: 400,
        }}
      >
        <AccordionSummary expandIcon={<ExpandMore />}>
          <Stack width="100%" direction="row" justifyContent="space-between" alignItems="center" gap={5}>
            <Typography>{item.label || item.link.label || item?.link?.content?.name}</Typography>
            <Typography variant="caption">{item?.link?.content ? "Page" : "Lien personnalisé"}</Typography>
          </Stack>
        </AccordionSummary>
        <AccordionDetails onClick={(e) => e.stopPropagation()}>
          <Stack
            gap={2}
            component="form"
            onSubmit={(e) => {
              e.preventDefault();
              onChange(e, item);
            }}
          >
            {withImage && item?.link?.upload ? (
              <Stack bgcolor="grey.light" justifyContent="center" alignItems="center" py={1}>
                <Image src={FormatUtils.image(item?.link?.upload?.["@id"] || item?.link?.upload)} alt={item?.link?.upload?.alt || "image"} width={100} height={100} />
              </Stack>
            ) : null}
            {item?.link?.content ? (
              <SelectPage defaultValue={item?.link?.content?.["@id"] || item?.link?.content} anchor={item?.link?.anchor} />
            ) : (
              <>
                <Input label="Nom" name="label" defaultValue={item.label || item.link.label} fullWidth />
                <Input label="URL" name="url" defaultValue={item.url || item.link.url} fullWidth />
              </>
            )}
            <Stack direction="row" gap={2} justifyContent="flex-end">
              <Button type="submit">Modifier</Button>
              <Button color="secondary" onClick={() => removeLink(item.uuid)}>
                Retirer
              </Button>
            </Stack>
          </Stack>
        </AccordionDetails>
      </Accordion>
    </SimpleTreeItemWrapper>
  );
}

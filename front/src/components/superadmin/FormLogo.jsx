import { debounce, Divider, Paper, Stack, Typography } from "@mui/material";
import { useCallback } from "react";
import InputMedias from "../inputs/InputMedias";
import Input from "../inputs/Input";
import SelectPage from "../inputs/SelectPage";

export default function FormLogo({ logo, onChange }) {
  const handleChange = useCallback(
    debounce((f) => {
      f();
    }, 100),
    []
  );
  return (
    <Stack component={Paper} p={3} gap={2}>
      <Typography variant="h2">Logo</Typography>
      <InputMedias
        defaultValue={logo?.upload}
        onChange={(file) => {
          onChange({ ...logo, upload: file });
        }}
      />
      <Stack direction="row" gap={2} width="100%">
        <Input
          fullWidth
          label="Nom"
          name="label"
          defaultValue={!logo?.link?.content ? logo?.link?.label : ""}
          onChange={(e) => {
            handleChange(() => {
              onChange({
                ...logo,
                link: {
                  ...logo.link,
                  label: e.target.value,
                  content: null,
                },
              });
            });
          }}
        />
        <Input
          fullWidth
          label="URL"
          name="url"
          defaultValue={!logo?.link?.content ? logo?.link?.url : ""}
          onChange={(e) => {
            handleChange(() => {
              onChange({
                ...logo,
                link: {
                  ...logo.link,
                  url: e.target.value,
                  content: null,
                },
              });
            });
          }}
        />
      </Stack>
      <Divider>Ou</Divider>
      <SelectPage
        defaultValue={logo?.link?.content?.["@id"]}
        onChange={(value) => {
          onChange({
            ...logo,
            link: {
              ...logo.link,
              label: null,
              url: null,
              ...value,
            },
          });
        }}
      />
    </Stack>
  );
}

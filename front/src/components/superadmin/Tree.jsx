"use client";

import React, { useEffect, useState } from "react";
import { Accordion, AccordionDetails, AccordionSummary, Divider, Paper, Stack, Button, Typography } from "@mui/material";
import { ExpandMore } from "@mui/icons-material";
import { SortableTree } from "dnd-kit-sortable-tree";
import TreeNav from "./TreeNav";
import { v4 as uuidv4 } from "uuid";
import SelectPage from "@/components/inputs/SelectPage";
import Input from "@/components/inputs/Input";
import InputMedias from "@/components/inputs/InputMedias";
import { addId, getFlatTree, removeItem } from "@/utils/tree.utils";
import { useSnack } from "@/context/SnackProvider";

export default function Tree({ label, items, withImage, disallowContents, max, canHaveChildren = true, onChange }) {
  const [mounted, setMounted] = useState(false);
  const { add } = useSnack();
  items = addId(items);
  if (!canHaveChildren) {
    items = items.map((e) => ({
      ...e,
      canHaveChildren: false,
    }));
  }
  const [expanded, setExpanded] = useState(withImage ? "panel-custom" : "panel-content");
  const handleChange = (panel) => (_, newExpanded) => {
    setExpanded(newExpanded ? panel : false);
  };

  const replace = (array = [], id, value) => {
    const v = [...array];
    return v.map((item) => {
      if (item.uuid === id) {
        return { ...item, ...value };
      }
      item.children = replace(item.children, id, value);
      return item;
    });
  };

  const changeValue = (e, item) => {
    const value = e.target?.content?.value
      ? {
          link: {
            ...item.link,
            content: e.target?.content?.value,
            label: e.target?.label?.value,
            url: e.target?.url?.value,
            anchor: e.target?.anchor?.value,
          },
        }
      : {
          link: {
            ...item.link,
            label: e.target.label.value,
            url: e.target.url.value,
          },
        };
    const final = replace(items, item.uuid, value);
    onChange(final);
  };

  const addLink = (e) => {
    e.preventDefault();
    if (!e.target?.label?.value && !e.target?.url?.value && !e.target?.content?.value) {
      add("error", "Veuillez renseigner le nom et l'url");
      return;
    }
    const id = uuidv4();
    const newLink = {
      uuid: id,
      link: {
        label: e.target?.label?.value,
        url: e.target?.url?.value,
        content: e.target?.content?.value,
        anchor: e.target?.anchor?.value,
        upload: e.target?.upload?.value,
      },
      children: [],
    };
    const links = [...items, newLink];
    e.target.reset();
    onChange(links);
  };

  const removeLink = (id) => {
    onChange(removeItem(items, id));
  };

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <Stack direction="row" gap={2}>
      <Stack component={Paper} height="fit-content" width={400}>
        {!disallowContents ? (
          <>
            <Accordion disableGutters expanded={expanded === "panel-content"} onChange={handleChange("panel-content")}>
              <AccordionSummary expandIcon={<ExpandMore />}>Page</AccordionSummary>
              <AccordionDetails>
                <Stack gap={1} component="form" onSubmit={addLink}>
                  {withImage ? <InputMedias small /> : null}
                  <SelectPage />
                  <Stack alignItems="flex-end">
                    <Button disabled={max && items.length >= max} type="submit">
                      Ajouter
                    </Button>
                  </Stack>
                </Stack>
              </AccordionDetails>
            </Accordion>
            <Divider />
          </>
        ) : null}
        <Accordion disableGutters expanded={expanded === "panel-custom"} onChange={handleChange("panel-custom")}>
          <AccordionSummary expandIcon={<ExpandMore />}>Lien personnalisé</AccordionSummary>
          <AccordionDetails>
            <Stack gap={1} component="form" onSubmit={addLink}>
              {withImage ? <InputMedias small /> : null}
              <Input label="Nom" name="label" fullWidth />
              <Input label="URL" name="url" fullWidth />
              <Stack alignItems="flex-end">
                <Button disabled={max && items.length >= max} type="submit">
                  Ajouter
                </Button>
              </Stack>
            </Stack>
          </AccordionDetails>
        </Accordion>
      </Stack>
      <Stack component={Paper} p={3} width="100%" gap={2}>
        <Typography variant="h2">{label}</Typography>
        {max ? <Typography variant="subtitle1">{max} éléments maximum .</Typography> : null}
        <Stack width="fit-content">
          <SortableTree
            items={items}
            onItemsChanged={(items) => {
              onChange(items);
            }}
            TreeItemComponent={React.forwardRef(function TreeItem(props, ref) {
              return <TreeNav {...props} defaultRef={ref} onChange={changeValue} withImage={withImage} removeLink={removeLink} />;
            })}
            sortableProps={{
              animateLayoutChanges: function noRefCheck() {},
              ghost: false,
            }}
          />
        </Stack>
      </Stack>
    </Stack>
  );
}

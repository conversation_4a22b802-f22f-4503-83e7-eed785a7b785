import { Drawer as <PERSON><PERSON><PERSON><PERSON><PERSON>, Stack } from "@mui/material";
import { styled } from "@mui/material/styles";

export const DrawerHeader = styled("div")(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "flex-end",
  padding: theme.spacing(0, 1),
  backgroundColor: theme.palette.secondary.main,
  zIndex: 1000,
  ...theme.mixins.toolbar,
  color: theme.palette.white,
}));

export const DrawerBlocsStyled = styled(MuiDrawer)(({ width }) => ({
  width: width ?? 450,
  flexShrink: 0,
  boxSizing: "border-box",
  "& .MuiDrawer-paper": {
    boxShadow: "none",
    width: width ?? 450,
  },
}));

export const CardBloc = styled("span")(({ theme }) => ({
  cursor: "pointer",
  height: 100,
  borderRadius: theme.shape.borderRadius,
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  justifyContent: "center",
  gap: 10,
  "&:hover": {
    backgroundColor: theme.palette.primary.opacity,
  },
}));

export const Preview = styled(Stack)(({ theme }) => ({
  position: "fixed",
  right: "51%",
  top: 200,
  width: 300,
  border: "1px solid grey",
}));

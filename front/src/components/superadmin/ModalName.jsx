import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Title, Stack } from "@mui/material";
import React from "react";
import Input from "@/components/inputs/Input";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import TranslationUtils from "@/utils/translation.utils";
import { useContent } from "@/context/ContentProvider";

export default function ModalName({ open, onClose, onRefresh, position }) {
  const { post } = useApi();
  const { add } = useSnack();
  const { fetchContents } = useContent();

  const addContent = async (e) => {
    e.preventDefault();
    try {
      const response = await post("/contents", {
        name: e.target.name.value,
        position,
      });
      e.target.reset();
      onRefresh(response.uuid);
      onClose();
      fetchContents();
      add("success", "Page ajoutée");
    } catch (error) {
      console.log(error);
    }
  };
  return (
    <Dialog disableRestoreFocus open={open} onClose={onClose}>
      <DialogTitle>{TranslationUtils.get("contents._add")}</DialogTitle>
      <DialogContent>
        <Stack component="form" onSubmit={addContent} width={300}>
          <Stack gap={2}>
            <Input autoFocus={true} name="name" label={TranslationUtils.get("global.name")} fullWidth />
          </Stack>
          <Stack gap={2} direction="row" justifyContent="flex-end" mt={2}>
            <Button color="secondary" onClick={onClose}>
              {TranslationUtils.get("global.cancel")}
            </Button>
            <Button type="submit">{TranslationUtils.get("global.add")}</Button>
          </Stack>
        </Stack>
      </DialogContent>
    </Dialog>
  );
}

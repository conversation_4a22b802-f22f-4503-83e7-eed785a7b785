"use client";

import { <PERSON><PERSON>, <PERSON>, Stack, Typography } from "@mui/material";
import SelectEmailsTypes from "../inputs/SelectEmailsTypes";
import SelectUsers from "../inputs/SelectUsers";
import ROUTES from "@/enums/ROUTES";
import FormatUtils from "@/utils/format.utils";
import { useRouter, useSearchParams } from "next/navigation";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import Input from "../inputs/Input";

export default function FormEmail({ data }) {
  const { post, put } = useApi();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { add } = useSnack();

  const goBack = () => {
    router.push(ROUTES.SUPERADMIN_EMAILS + FormatUtils.formatUrl(searchParams));
    router.refresh();
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const fetch = data ? put : post;
    const url = data ? `/mailer-types/${data?.uuid}` : "/mailer-types";
    fetch(url, {
      email: e.target.email.value,
      code: e.target.emailType.value,
    });
    goBack();
    add("success", "Email ajouté avec succès");
  };

  return (
    <Stack component={Paper} p={4} gap={3}>
      <Typography variant="h4">{data ? "Modifier un type d'email" : "Ajouter un type d'email"}</Typography>
      <Stack component="form" gap={2} onSubmit={handleSubmit}>
        <Stack direction="row" gap={2}>
          <SelectEmailsTypes required defaultValue={data?.code} />
          <Input label="Email" name="email" defaultValue={data?.email} fullWidth required type="email" />
        </Stack>
        <Stack direction="row" justifyContent="flex-end" gap={2}>
          <Button color="secondary" onClick={goBack}>
            Retour
          </Button>
          <Button type="submit">Ajouter</Button>
        </Stack>
      </Stack>
    </Stack>
  );
}

import { Card, Stack, Typography } from "@mui/material";
import { CheckCircle } from "@mui/icons-material";

export default function CardBuilder({ card }) {
  return (
    <Card variant="outlined" component={Stack} p={2} height="100%">
      <Stack direction="row" spacing={1} alignItems="center">
        <CheckCircle color="success" />
        <Typography variant="h6">{card.title}</Typography>
      </Stack>
      <Typography
        component="div"
        sx={{
          whiteSpace: "normal",
        }}
        dangerouslySetInnerHTML={{
          __html: card.text,
        }}
      />
    </Card>
  );
}

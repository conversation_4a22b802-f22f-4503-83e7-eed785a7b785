import { Button, <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Title, Stack } from "@mui/material";
import Input from "@/components/inputs/Input";
import TranslationUtils from "@/utils/translation.utils";

export default function ModalFavorite({ bloc, onClose, addBloc, onSuccess }) {
  const addContent = (e) => {
    e.preventDefault();
    e.stopPropagation();
    const newBloc = { ...bloc, name: e.target.name.value };
    addBloc(newBloc);
    onSuccess();
    onClose();
  };
  return (
    <Dialog disableRestoreFocus open={!!bloc} onClose={onClose}>
      <DialogTitle>Ajouter en favoris</DialogTitle>
      <DialogContent>
        <Stack component="form" onSubmit={addContent} width={300}>
          <Stack gap={2}>
            <Input autoFocus={true} name="name" label={"Nom du bloc"} fullWidth required />
          </Stack>
          <Stack gap={2} direction="row" justifyContent="flex-end" mt={2}>
            <Button color="secondary" onClick={onClose}>
              {TranslationUtils.get("global.cancel")}
            </Button>
            <Button type="submit">{TranslationUtils.get("global.save")}</Button>
          </Stack>
        </Stack>
      </DialogContent>
    </Dialog>
  );
}

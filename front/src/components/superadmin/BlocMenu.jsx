import { Button, IconButton, styled, Tooltip, Typography } from "@mui/material";

import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import { useSnack } from "@/context/SnackProvider";

const Layout = styled("div")(({ theme }) => ({
  position: "absolute",
  left: 0,
  top: 0,
  backgroundColor: theme.palette.primary.main,
  color: theme.palette.white,
  paddingRight: theme.spacing(0.5),
  paddingLeft: theme.spacing(0.5),
  justifyContent: "space-between",
  alignItems: "center",
  gap: theme.spacing(1),
  display: "none",
}));

export default function BlocMenu({ label, onEdit, onDelete, index, blocks, onMove, block }) {
  const { add } = useSnack();
  const copyId = () => {
    navigator.clipboard.writeText(block?.uuid);
    add("success", "ID copié");
  };

  return (
    <Layout className="menu">
      <Typography variant="body2">{label}</Typography>
      {onEdit ? (
        <Tooltip title="Modifier" placement="top">
          <IconButton onClick={onEdit} sx={{ p: "3px", color: "white" }}>
            <EditIcon />
          </IconButton>
        </Tooltip>
      ) : null}
      <Tooltip title="Supprimer" placement="top">
        <IconButton onClick={onDelete} sx={{ p: "3px", color: "white" }}>
          <DeleteIcon />
        </IconButton>
      </Tooltip>
      <Tooltip title="Copier l'ID" placement="top">
        <IconButton onClick={copyId} sx={{ p: "3px", color: "white" }}>
          <ContentCopyIcon />
        </IconButton>
      </Tooltip>
      <IconButton disabled={index === 0} onClick={() => onMove(blocks[index].uuid, "up")} sx={{ p: "3px", color: "white" }}>
        <ExpandLessIcon />
      </IconButton>
      <IconButton disabled={index === blocks.length - 1} onClick={() => onMove(blocks[index].uuid, "down")} sx={{ p: "3px", color: "white" }}>
        <ExpandMoreIcon />
      </IconButton>
    </Layout>
  );
}

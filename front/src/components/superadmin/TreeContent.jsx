import { <PERSON><PERSON><PERSON><PERSON>on, Stack, Typography } from "@mui/material";
import React from "react";
import { SimpleTreeItemWrapper } from "dnd-kit-sortable-tree";
import { Edit } from "@mui/icons-material";

export default function TreeContent({ defaultRef, onClick, ...props }) {
  const item = props.item;
  return (
    <SimpleTreeItemWrapper {...props} ref={defaultRef} ghost={false} className="tree-content">
      <Stack direction="row" justifyContent="space-between" width="100%" gap={2} alignItems="center">
        <Typography variant={item.status ? "body1" : "disabled"}>{item.name}</Typography>
        <Stack>
          <IconButton
            onClick={(e) => {
              onClick(item.uuid);
              e.stopPropagation();
            }}
          >
            <Edit />
          </IconButton>
        </Stack>
      </Stack>
    </SimpleTreeItemWrapper>
  );
}

"use client";

import Input from "@/components/inputs/Input";
import InputMedias from "@/components/inputs/InputMedias";
import SelectStores from "@/components/inputs/SelectStores";
import InputWysiwyg from "@/components/inputs/wysiwyg/InputWysiwyg";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import ROUTES from "@/enums/ROUTES";
import FormatUtils from "@/utils/format.utils";
import { Button, Paper, Stack, Typography } from "@mui/material";
import { useRouter, useSearchParams } from "next/navigation";
import React from "react";

export default function FormAgencyPost({ item }) {
  const { put, post } = useApi();
  const { add } = useSnack();
  const router = useRouter();
  const searchParams = useSearchParams();

  const handleSubmit = async (e) => {
    try {
      e.preventDefault();
      const data = Object.fromEntries(new FormData(e.target));
      data.upload = data.upload?.length > 0 ? data.upload : null;
      data.agency = data.agency?.length > 0 ? data.agency : null;
      const fecth = item ? put : post;
      const url = item ? `/agency-posts/${item?.uuid}` : "/agency-posts";
      await fecth(url, data);
      add("success", "L'actualité a bien été modifiée !");
      router.push(ROUTES.SUPERADMIN_STORES_ACTUS + FormatUtils.formatUrl(searchParams));
      router.refresh();
    } catch (error) {
      add("error", error?.description);
      console.log(error);
    }
  };
  return (
    <Stack component={Paper} p={5}>
      <Typography variant="h4" mb={3}>
        {item ? "Modifier l'actualité" : "Ajouter une actualité"}
      </Typography>
      <Stack gap={2} component="form" onSubmit={handleSubmit}>
        <InputMedias label="Image" name="image" fullWidth defaultValue={item?.upload} />
        <Stack direction="row" gap={2}>
          <Input label="Titre" name="title" fullWidth defaultValue={item?.title} />
          <Input
            label="Slug"
            name="url"
            fullWidth
            defaultValue={item?.url}
            helperText="Lien pour le page détail de l'actualités, doit commencer par un '/' et ne doit pas contenir d'espace ni de capitales"
          />
          <SelectStores defaultValue={item?.agency} required />
        </Stack>
        <InputWysiwyg label="Contenu" name="content" fullWidth defaultValue={item?.content} />
        <Stack direction="row" justifyContent="flex-end" gap={2}>
          <Button color="secondary" onClick={() => router.push(ROUTES.SUPERADMIN_STORES_ACTUS + FormatUtils.formatUrl(searchParams))}>
            Retour
          </Button>
          <Button type="submit">Enregistrer</Button>
        </Stack>
      </Stack>
    </Stack>
  );
}

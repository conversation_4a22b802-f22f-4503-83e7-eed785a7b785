"use client";

import InputAccordionCantons from "@/components/InputAccordionCantons";
import Input from "@/components/inputs/Input";
import InputMedias from "@/components/inputs/InputMedias";
import SelectLyraSeller from "@/components/inputs/SelectLyraSeller";
import InputWysiwyg from "@/components/inputs/wysiwyg/InputWysiwyg";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import ROUTES from "@/enums/ROUTES";
import { mapStore } from "@/mappers/stores.mapper";
import FormatUtils from "@/utils/format.utils";
import { Button, FormControlLabel, Paper, Stack, Switch, Typography } from "@mui/material";
import { useRouter, useSearchParams } from "next/navigation";
import { useState } from "react";

export default function FormAgency({ item }) {
  const { post, put } = useApi();
  const { add } = useSnack();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [areas, setAreas] = useState([]);

  const handleSubmit = async (e) => {
    try {
      e.preventDefault();
      const data = Object.fromEntries(new FormData(e.target));
      const newData = { ...data, legacyId: e.target.legacyId.value };
      const fetch = item ? put : post;
      const url = item ? `/agencies/${item.uuid}` : "/agencies";
      const ag = await fetch(url, mapStore(newData));
      await post("/agencies/update-areas", { agency: ag["@id"], areas: areas.map((area) => area["@id"]) });
      add("success", "Le cabinet a bien été créé !");
      router.push(ROUTES.SUPERADMIN_STORES + FormatUtils.formatUrl(searchParams));
      router.refresh();
    } catch (error) {
      const err = await error.json();
      add("error", err?.description);
    }
  };

  return (
    <Stack component={Paper} p={5} gap={3}>
      <Typography variant="h4">{item ? "Modifier le cabinet" : "Créer un cabinet"}</Typography>
      <Stack gap={2} component="form" onSubmit={handleSubmit}>
        <Stack display="grid" gridTemplateColumns="1fr 1fr" gap={2}>
          <Input label="Nom" name="name" fullWidth required defaultValue={item?.name} />
          <Input label="Ville" name="city" fullWidth required defaultValue={item?.location?.city} />
          <Input label="Code postal" name="zip" fullWidth required defaultValue={item?.location?.postcode} />
          <Input label="Adresse" name="address" fullWidth required defaultValue={item?.location?.address1} />
          <Input label="Nom du contact" name="contactName" fullWidth required defaultValue={item?.contact?.name} />
          <Input label="Téléphone du contact" name="contactPhone" fullWidth required defaultValue={item?.contact?.phone} />
          <Input label="Email du contact" name="contactEmail" fullWidth required defaultValue={item?.contact?.email} />
          <Input label="Certifications" name="certifications" fullWidth required defaultValue={item?.certifications} />
          <Input label="Latitude" name="latitude" fullWidth defaultValue={item?.location?.latitude} />
          <Input label="Longitude" name="longitude" fullWidth defaultValue={item?.location?.longitude} />
          <Input label="ID du cabinet" name="legacyId" fullWidth defaultValue={item?.legacyId} disabled={!!item} />
          <Input label="Meta title" name="metaTitle" fullWidth defaultValue={item?.meta?.title} />
          <Input label={"Lien vers la plateforme"} name="linkToOpen" fullWidth defaultValue={item.linkToOpen} />
        </Stack>
        <Input
          minRows={3}
          multiline
          label="Meta description"
          name="metaDescription"
          fullWidth
          type="textarea"
          defaultValue={item?.meta?.description}
        />
        <InputWysiwyg label="Description" name="description" defaultValue={item?.description} />
        <Input label="Lien Facebook" name="facebookLink" fullWidth defaultValue={item?.facebookLink} />
        <Input label="Lien LinkedIn" name="linkedinLink" fullWidth defaultValue={item?.linkedinLink} />
        <InputMedias label="Photo du cabinet" name="image" defaultValue={item?.upload} />
        <InputMedias label="Photo du contact" name="contactImage" defaultValue={item?.contact?.upload} />
        <Stack gap={3}>
          <Typography variant="h2" mb={3}>
            Horaires
          </Typography>
          <Stack display="grid" gridTemplateColumns="1fr 2fr 2fr" columnGap={5} rowGap={2} width="40%" alignItems="center">
            <Stack />
            <Typography variant="bold" textAlign="center">
              Matin
            </Typography>
            <Typography variant="bold" textAlign="center">
              Après-midi
            </Typography>
            <Typography variant="bold">Lundi</Typography>
            <InputTime
              name="MondayMorning"
              defaultValue={{
                start: item?.schedule?.monday?.morning?.openAt,
                end: item?.schedule?.monday?.morning?.closeAt,
              }}
            />
            <InputTime
              name="MondayAfternoon"
              defaultValue={{
                start: item?.schedule?.monday?.afternoon?.openAt,
                end: item?.schedule?.monday?.afternoon?.closeAt,
              }}
            />
            <Typography variant="bold">Mardi</Typography>
            <InputTime
              name="TuesdayMorning"
              defaultValue={{
                start: item?.schedule?.tuesday?.morning?.openAt,
                end: item?.schedule?.tuesday?.morning?.closeAt,
              }}
            />
            <InputTime
              name="TuesdayAfternoon"
              defaultValue={{
                start: item?.schedule?.tuesday?.afternoon?.openAt,
                end: item?.schedule?.tuesday?.afternoon?.closeAt,
              }}
            />
            <Typography variant="bold">Mercredi</Typography>
            <InputTime
              name="WednesdayMorning"
              defaultValue={{
                start: item?.schedule?.wednesday?.morning?.openAt,
                end: item?.schedule?.wednesday?.morning?.closeAt,
              }}
            />
            <InputTime
              name="WednesdayAfternoon"
              defaultValue={{
                start: item?.schedule?.wednesday?.afternoon?.openAt,
                end: item?.schedule?.wednesday?.afternoon?.closeAt,
              }}
            />
            <Typography variant="bold">Jeudi</Typography>
            <InputTime
              name="ThursdayMorning"
              defaultValue={{
                start: item?.schedule?.thursday?.morning?.openAt,
                end: item?.schedule?.thursday?.morning?.closeAt,
              }}
            />
            <InputTime
              name="ThursdayAfternoon"
              defaultValue={{
                start: item?.schedule?.thursday?.afternoon?.openAt,
                end: item?.schedule?.thursday?.afternoon?.closeAt,
              }}
            />
            <Typography variant="bold">Vendredi</Typography>
            <InputTime
              name="FridayMorning"
              defaultValue={{
                start: item?.schedule?.friday?.morning?.openAt,
                end: item?.schedule?.friday?.morning?.closeAt,
              }}
            />
            <InputTime
              name="FridayAfternoon"
              defaultValue={{
                start: item?.schedule?.friday?.afternoon?.openAt,
                end: item?.schedule?.friday?.afternoon?.closeAt,
              }}
            />
            <Typography variant="bold">Samedi</Typography>
            <InputTime
              name="SaturdayMorning"
              defaultValue={{
                start: item?.schedule?.saturday?.morning?.openAt,
                end: item?.schedule?.saturday?.morning?.closeAt,
              }}
            />
            <InputTime
              name="SaturdayAfternoon"
              defaultValue={{
                start: item?.schedule?.saturday?.afternoon?.openAt,
                end: item?.schedule?.saturday?.afternoon?.closeAt,
              }}
            />
            <Typography variant="bold">Dimanche</Typography>
            <InputTime
              name="SundayMorning"
              defaultValue={{
                start: item?.schedule?.sunday?.morning?.openAt,
                end: item?.schedule?.sunday?.morning?.closeAt,
              }}
            />
            <InputTime
              name="SundayAfternoon"
              defaultValue={{
                start: item?.schedule?.sunday?.afternoon?.openAt,
                end: item?.schedule?.sunday?.afternoon?.closeAt,
              }}
            />
          </Stack>
          <FormControlLabel
            control={<Switch defaultChecked={item?.enablePaymentThreeTimeNoFee} />}
            label="Activer le paiement en 3 fois sans frais"
            name="paymentEnabled"
          />
          <Input label="Balise JS" name="js" fullWidth multiline minRows={5} defaultValue={item?.ratingWidget?.js} />
          <Input label="Balise HTML" name="html" fullWidth multiline minRows={5} defaultValue={item?.ratingWidget?.html} />
          <SelectLyraSeller defaultValue={item?.lyraSeller} />
          <InputAccordionCantons item={item} setAreas={setAreas} />
          <FormControlLabel
            control={<Switch defaultChecked={item?.displayAppointment} />}
            label="Afficher l'encart rendez-vous"
            name="displayAppointment"
          />
        </Stack>
        <Stack direction="row" justifyContent="flex-end" gap={2}>
          <Button color="secondary" onClick={() => router.push(ROUTES.SUPERADMIN_STORES + FormatUtils.formatUrl(searchParams))}>
            Retour
          </Button>
          <Button type="submit">{item ? "Enregistrer" : "Ajouter"}</Button>
        </Stack>
      </Stack>
    </Stack>
  );
}

const InputTime = ({ name, defaultValue }) => (
  <Stack direction="row" gap={1} alignItems="center">
    <Input name={`start${name}`} type="time" defaultValue={defaultValue.start} />
    -
    <Input name={`end${name}`} type="time" defaultValue={defaultValue.end} />
  </Stack>
);

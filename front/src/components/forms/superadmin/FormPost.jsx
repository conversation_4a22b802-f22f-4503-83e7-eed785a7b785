"use client";

import { <PERSON><PERSON>, FormControlLabel, Paper, Stack, Switch, Typography } from "@mui/material";
import Input from "@/components/inputs/Input";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import { useRouter, useSearchParams } from "next/navigation";
import FormatUtils from "@/utils/format.utils";
import InputMedias from "@/components/inputs/InputMedias";
import InputWysiwyg from "@/components/inputs/wysiwyg/InputWysiwyg";
import ROUTES from "@/enums/ROUTES";
import SelectPostCategories from "@/components/inputs/SelectPostCategories";

export default function FormPost({ item }) {
  const { post, put } = useApi();
  const { add } = useSnack();
  const router = useRouter();
  const searchParams = useSearchParams();

  const handleSubmit = async (e) => {
    try {
      e.preventDefault();
      const data = Object.fromEntries(new FormData(e.target));
      data.isPublished = data.isPublished === false || data.isPublished === true ? data.isPublished : data.isPublished === "on";
      data.upload = data.upload?.length > 0 ? data.upload : null;
      const url = item ? `/posts/${item.uuid}` : "/posts";
      const fetch = item ? put : post;
      await fetch(url, data);
      add("success", `L'actualité a bien été ${item ? "modifiée" : "créée"} !`);
      router.push(ROUTES.SUPERADMIN_ACTUS);
      router.refresh();
    } catch (error) {
      add("error", error.description);
      console.log(error);
    }
  };

  return (
    <Stack component={Paper} p={5}>
      <Stack component="form" onSubmit={handleSubmit}>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography variant="h4" mb={3}>
            {item ? "Modifier une actualité" : "Ajouter une actualité"}
          </Typography>
          <FormControlLabel control={<Switch defaultChecked={item ? item?.isPublished : true} />} label="Publié" name="isPublished" />
        </Stack>
        <Stack gap={2}>
          <InputMedias label="Image" name="upload" fullWidth defaultValue={item?.upload} required />
          <Stack display="grid" gridTemplateColumns="1fr 1fr" gap={2}>
            <Input label="Titre" name="title" fullWidth autoFocus required defaultValue={item?.title} />
            <Input label="Titre long" name="longTitle" fullWidth required defaultValue={item?.longTitle} />
            <Input label="Slug" name="url" fullWidth required defaultValue={item?.url} helperText={"Ajouter un '/' au début du slug"} />
            <SelectPostCategories fullWidth required defaultValue={item?.category} />
            <Input label="Titre SEO" name="seoTitle" fullWidth required defaultValue={item?.seoTitle} />
            <Input label="Description SEO" name="seoDescription" fullWidth required defaultValue={item?.seoDescription} multiline minRows={5} />
            <Input label="Description" name="description" fullWidth required defaultValue={item?.description} multiline minRows={5} />
          </Stack>
          <InputWysiwyg label="Contenu" name="content" fullWidth defaultValue={item?.content} />
          <Stack direction="row" justifyContent="flex-end" gap={2}>
            <Button color="secondary" onClick={() => router.push(ROUTES.SUPERADMIN_ACTUS + FormatUtils.formatUrl(searchParams))}>
              Retour
            </Button>
            <Button type="submit">Ajouter</Button>
          </Stack>
        </Stack>
      </Stack>
    </Stack>
  );
}

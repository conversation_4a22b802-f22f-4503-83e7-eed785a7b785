"use client";
import Input from "@/components/inputs/Input";
import InputMedias from "@/components/inputs/InputMedias";
import SelectStores from "@/components/inputs/SelectStores";
import SelectStoresMultiple from "@/components/inputs/SelectStoresMultiple";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import ROUTES from "@/enums/ROUTES";
import USER_ROLES from "@/enums/USER_ROLES";
import { Button, Paper, Stack, Typography } from "@mui/material";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";

export default function FormFranchise({ item }) {
  const { post, put } = useApi();
  const { add } = useSnack();
  const router = useRouter();

  console.log(item)

  const defaultStores = Array.isArray(item?.agencies) ? item.agencies.map((a) => a.agency["@id"]) : item?.agencies ? [item?.agencies] : [];
  console.log(defaultStores)
  const [stores, setStores] = useState(defaultStores);

  const handleSubmit = async (e) => {
    try {
      e.preventDefault();

      const obj = {
        lastname: e.target.lastname.value,
        firstname: e.target.firstname.value,
        email: e.target.email.value,
        upload: e.target.upload.value === "" ? null : e.target.upload.value,
        role: USER_ROLES.ADMIN,
      };
      const url = item ? `/users/${item.uuid}` : "/users";
      const fetch = item ? put : post;

      const user = await fetch(url, {
        ...item,
        ...obj,
      });

      await put("/agency-users", {
        agencies: stores,
        user: item ? item["@id"] : user["@id"],
      });

      add("success", `Le franchisé a bien été ${item ? "modifié" : "ajouté"} !`);
      router.push(ROUTES.SUPERADMIN_FRANCHISES);
      router.refresh();
    } catch (error) {
      console.log(error);
      add("error", "Une erreur est survenue !");
    }
  };

  return (
    <Stack component={Paper} p={5}>
      <Typography variant="h4" mb={3}>
        {item ? "Modifier le franchisé" : "Ajouter un franchisé"}
      </Typography>
      <Stack gap={2} component="form" onSubmit={handleSubmit}>
        <Stack gap={2}>
          <InputMedias label="Photo de profil" defaultValue={item?.upload} />
          <Input label="Nom" name="lastname" fullWidth defaultValue={item?.lastname || ""} required />
          <Input label="Prénom" name="firstname" fullWidth defaultValue={item?.firstname || ""} required />
          <Input label="E-mail" name="email" fullWidth defaultValue={item?.email || ""} required />
          <SelectStoresMultiple
            label="Cabinet"
            name="agency"
            defaultValues={defaultStores}
            values={stores}
            onChange={setStores}
            required={!stores.length}
          />
        </Stack>
        <Stack direction="row" justifyContent="flex-end" gap={2}>
          <Button color="secondary" component={Link} href={ROUTES.SUPERADMIN_FRANCHISES}>
            Retour
          </Button>
          <Button type="submit">Enregistrer</Button>
        </Stack>
      </Stack>
    </Stack>
  );
}

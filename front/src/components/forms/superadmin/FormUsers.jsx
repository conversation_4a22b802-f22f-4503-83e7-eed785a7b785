"use client";
import Input from "@/components/inputs/Input";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import ROUTES from "@/enums/ROUTES";
import USER_ROLES from "@/enums/USER_ROLES";
import { <PERSON>ton, FormControlLabel, Paper, Stack, Switch, Typography } from "@mui/material";
import Link from "next/link";
import { useRouter } from "next/navigation";

export default function FormUsers({ user }) {
  const { post, put } = useApi();
  const { add } = useSnack();
  const router = useRouter();

  const handleSubmit = async (e) => {
    try {
      e.preventDefault();

      const data = new FormData(e.target);
      const url = user ? `/users/${user.uuid}` : "/users";
      const fetch = user ? put : post;

      await fetch(url, {
        ...user,
        lastname: data.get("lastname"),
        firstname: data.get("firstname"),
        email: data.get("email") || user?.email,
        role: data.get("role") === "on" ? USER_ROLES.SUPERADMIN : USER_ROLES.ADMIN,
      });

      add("success", `L'utilisateur a bien été ${user ? "modifié" : "ajouté"} !`);
      router.push(ROUTES.SUPERADMIN_USERS);
      router.refresh();
    } catch (error) {
      add("error", error?.description);
    }
  };

  return (
    <Stack component={Paper} p={5}>
      <Typography variant="h4" mb={3}>
        {user ? "Modifier l'utilisateur" : "Ajouter un utilisateur"}
      </Typography>
      <Stack gap={2} component="form" onSubmit={handleSubmit}>
        <Stack direction="row" gap={2} alignItems="flex-end">
          <Input label="Nom" name="lastname" fullWidth defaultValue={user?.lastname || ""} />
          <Input label="Prénom" name="firstname" fullWidth defaultValue={user?.firstname || ""} />
          <Input label="Email" name="email" fullWidth defaultValue={user?.email || ""} type="email" disabled={user?.email} required />
          <FormControlLabel control={<Switch defaultChecked={user?.role === USER_ROLES.SUPERADMIN} />} label="Superadmin" name="role" />
        </Stack>
        <Stack direction="row" justifyContent="flex-end" gap={2}>
          <Button color="secondary" component={Link} href={ROUTES.SUPERADMIN_USERS}>
            Retour
          </Button>
          <Button type="submit">Enregistrer</Button>
        </Stack>
      </Stack>
    </Stack>
  );
}

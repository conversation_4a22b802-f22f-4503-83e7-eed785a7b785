"use client";

import { <PERSON><PERSON>, FormControlLabel, Paper, Stack, Switch, Typography } from "@mui/material";
import Input from "@/components/inputs/Input";
import ROUTES from "@/enums/ROUTES";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import FormatUtils from "@/utils/format.utils";
import { useRouter, useSearchParams } from "next/navigation";
import SelectCantons from "@/components/inputs/SelectCantons";

export default function FormCity({ item }) {
  const { put, post } = useApi();
  const { add } = useSnack();
  const router = useRouter();
  const searchParams = useSearchParams();

  const handleSubmit = async (e) => {
    try {
      e.preventDefault();
      const data = Object.fromEntries(new FormData(e.target));
      data.hasTermite = e.target.hasTermite.checked;
      const url = item ? `/location-cities/${item.uuid}` : "/location-cities";
      const fetch = item ? put : post;
      await fetch(url, data);
      add("success", "La commune a bien été modifiée !");
      router.push(ROUTES.COMMUNES + FormatUtils.formatUrl(searchParams));
      router.refresh();
    } catch (error) {
      add("error", error?.description);
      console.log(error);
    }
  };

  return (
    <Stack component={Paper} p={5} gap={3}>
      <Typography variant="h4">Modifier la commune</Typography>
      <Stack gap={2} component="form" onSubmit={handleSubmit}>
        <Stack direction="row" gap={2} alignItems="flex-end">
          <Input defaultValue={item?.name} label="Nom" name="name" fullWidth required />
          <Input defaultValue={item?.zip} label="Code postal" name="zip" fullWidth required />
          <SelectCantons defaultValue={item?.area} required />
          <FormControlLabel
            control={<Switch defaultChecked={item?.hasTermite} />}
            label="Termites"
            name="hasTermite"
          />
        </Stack>
        <Stack direction="row" justifyContent="flex-end" gap={2}>
          <Button color="secondary" onClick={() => router.push(ROUTES.COMMUNES + FormatUtils.formatUrl(searchParams))}>
            Retour
          </Button>
          <Button type="submit">Enregistrer</Button>
        </Stack>
      </Stack>
    </Stack>
  );
}

"use client";
import Input from "@/components/inputs/Input";
import InputMedias from "@/components/inputs/InputMedias";
import SelectDepartment from "@/components/inputs/SelectDepartment";
import SelectPartnersActivities from "@/components/inputs/SelectPartnersActivities";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import ROUTES from "@/enums/ROUTES";
import { Button, Paper, Stack, Typography } from "@mui/material";
import Link from "next/link";
import { useRouter } from "next/navigation";

const defaultDep = {
  "@id": "all",
  name: "France entière",
};

export default function FormPartner({ partner }) {
  const { post, put } = useApi();
  const { add } = useSnack();
  const router = useRouter();

  const handleSubmit = async (e) => {
    try {
      e.preventDefault();

      const obj = {
        upload: e.target.upload.value,
        name: e.target.name.value,
        link: e.target.link.value,
        department: e.target.department.value === "all" ? null : e.target.department.value,
        activity: e.target.activity.value,
      };
      const url = partner ? `/partners/${partner.uuid}` : "/partners";
      const fetch = partner ? put : post;

      await fetch(url, {
        ...partner,
        ...obj,
      });

      add("success", `Le partenaire a bien été ${partner ? "modifié" : "ajouté"} !`);
      router.push(ROUTES.SUPERADMIN_PARTNERS);
      router.refresh();
    } catch (error) {
      console.log(error);
      add("error", "Une erreur est survenue !");
    }
  };

  return (
    <Stack component={Paper} p={5}>
      <Typography variant="h4" mb={3}>
        {partner ? "Modifier un partenaire" : "Ajouter un partenaire"}
      </Typography>
      <Stack gap={2} component="form" onSubmit={handleSubmit}>
        <Stack gap={2}>
          <InputMedias defaultValue={partner?.upload || null} label="Logo" required />
          <Input label="Nom" name="name" fullWidth defaultValue={partner?.name || ""} />
          <Input label="Lien" name="link" fullWidth defaultValue={partner?.link || ""} />
          <SelectDepartment defaultValue={partner?.department || defaultDep} allValue required />
          <SelectPartnersActivities defaultValue={partner?.activity} />
        </Stack>
        <Stack direction="row" justifyContent="flex-end" gap={2}>
          <Button color="secondary" component={Link} href={ROUTES.SUPERADMIN_PARTNERS}>
            Retour
          </Button>
          <Button type="submit">Enregistrer</Button>
        </Stack>
      </Stack>
    </Stack>
  );
}

"use client";
import FormBackButton from "@/components/buttons/FormBackButton";
import Input from "@/components/inputs/Input";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import ROUTES from "@/enums/ROUTES";
import TranslationUtils from "@/utils/translation.utils";
import { Button, FormControlLabel, Stack, Switch, Typography } from "@mui/material";
import { useRouter } from "next/navigation";
import SelectStores from "../../inputs/SelectStores";
import InputWysiwyg from "../../inputs/wysiwyg/InputWysiwyg";
import FormatUtils from "@/utils/format.utils";

export default function FormJobOffers({ item }) {
  const { add } = useSnack();
  const router = useRouter();
  const { post, put } = useApi();

  const submit = async (e) => {
    try {
      e.preventDefault();
      let data = Object.fromEntries(new FormData(e.target));
      const method = item ? put : post;
      const url = item ? `/job-offers/${item.uuid}` : "/job-offers";
      data.status = e.target.status.checked;
      data.agency = e.target.agency.value.length > 0 ? e.target.agency.value : null;
      if (item) {
        data = { ...item, ...data };
      }
      await method(url, data);
      add("success", "L'offre d'emploi a bien été créé !");
      router.push(ROUTES.SUPERADMIN_JOBS);
      router.refresh();
    } catch (error) {
      add("error", error?.description);
      console.error(error);
    }
  };

  return (
    <Stack component="form" gap={2} onSubmit={submit}>
      <Stack direction="row" justifyContent="space-between" alignItems="center">
        <Typography variant="h4">{item?.title || TranslationUtils.get("jobs._add")}</Typography>
        <FormControlLabel control={<Switch defaultChecked={item ? item?.status : true} />} label="Publié" name="status" />
      </Stack>
      <Stack gap={2}>
        <Input
          label="Référence"
          name="reference"
          defaultValue={item?.reference}
          fullWidth
          autoFocus={!item?.reference}
          required
          disabled={item?.reference}
        />
        <Input
          type="date"
          label="Date de publication"
          name="publicationDate"
          defaultValue={item ? FormatUtils.formatDateEn(item?.publicationDate) : FormatUtils.formatDateEn(new Date())}
          fullWidth
          autoFocus={!!item?.reference}
          required
        />
        <Input label="Titre de l'offre" name="title" defaultValue={item?.title} fullWidth required />
        <Input label="Poste" name="jobName" fullWidth defaultValue={item?.jobName} required />
        <Input label="Lieu" name="location" fullWidth defaultValue={item?.location} required />
        <SelectStores label="Agence" name="agency" defaultValue={item?.agency} />
        <InputWysiwyg label="Description" name="description" defaultValue={item?.description} fullWidth />
        <Stack direction="row" justifyContent="flex-end" gap={2}>
          <FormBackButton>{TranslationUtils.get("global.back")}</FormBackButton>
          <Button type="submit">{item ? "Modifier" : "Ajouter"}</Button>
        </Stack>
      </Stack>
    </Stack>
  );
}

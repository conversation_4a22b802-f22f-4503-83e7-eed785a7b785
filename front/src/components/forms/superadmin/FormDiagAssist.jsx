"use client";

import Input from "@/components/inputs/Input";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import { Button, Grid2, Paper, Stack, Typography } from "@mui/material";
import { useRouter } from "next/navigation";
import SelectStores from "@/components/inputs/SelectStores";

export default function FormDiagAssist({ title }) {
  const { post } = useApi();
  const { add } = useSnack();
  const router = useRouter();

  const handleSubmit = async (e) => {
    try {
      e.preventDefault();
      const data = Object.fromEntries(new FormData(e.target));
      await post("/mailer/diag-assist", { ...data, numberDiagAssist: +data.numberDiagAssist });
      add("success", "La demande a bien été envoyée !");
      router.push("/");
    } catch (error) {
      const err = await error.json();
      add("error", err?.description);
    }
  };

  const formFields = [
    {
      label: "Nombre de Diag Assist",
      name: "numberDiagAssist",
      type: "number",
      required: true,
      size: { xs: 12, md: 6 },
    },
    {
      label: "Société",
      name: "company",
      required: true,
      size: { xs: 12, md: 6 },
    },
    {
      label: "Nom",
      name: "lastname",
      required: true,
      size: { xs: 12, md: 6 },
    },
    {
      label: "Prénom",
      name: "firstname",
      required: true,
      size: { xs: 12, md: 6 },
    },
    {
      label: "Code postal",
      name: "zip",
      required: true,
      size: { xs: 12, md: 6 },
    },
    {
      label: "Ville",
      name: "city",
      required: true,
      size: { xs: 12, md: 6 },
    },
    {
      label: "Adresse",
      name: "address",
      required: true,
      size: 12,
    },
    {
      label: "Téléphone",
      name: "phone",
      type: "tel",
      required: true,
      size: { xs: 12, md: 6 },
    },
    {
      label: "Email",
      name: "contactEmail",
      type: "email",
      required: true,
      size: { xs: 12, md: 6 },
    },
  ];

  return (
    <Stack component={Paper} p={5} my={2} gap={3}>
      <Typography variant="h3" textAlign="center">
        {title}
      </Typography>
      <Grid2 container spacing={2} component="form" onSubmit={handleSubmit}>
        {formFields.map((field) => (
          <Grid2 key={field.name} size={field.size}>
            <Input label={field.label} name={field.name} type={field.type} required={field.required} fullWidth />
          </Grid2>
        ))}
        <SelectStores defaultValue={null} />
        <Grid2 size={12}>
          <Input minRows={3} multiline label="Message" name="message" fullWidth required />
        </Grid2>
        <Grid2 size={12} textAlign="end">
          <Button type="submit" variant="contained">
            Envoyer
          </Button>
        </Grid2>
      </Grid2>
    </Stack>
  );
}

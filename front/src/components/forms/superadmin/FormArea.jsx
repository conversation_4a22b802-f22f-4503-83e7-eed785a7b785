"use client";

import { <PERSON><PERSON>, Paper, Stack, Typography } from "@mui/material";
import Input from "@/components/inputs/Input";
import ROUTES from "@/enums/ROUTES";
import SelectDepartment from "@/components/inputs/SelectDepartment";
import SelectStores from "@/components/inputs/SelectStores";
import FormatUtils from "@/utils/format.utils";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import { useRouter, useSearchParams } from "next/navigation";

export default function FormArea({ item }) {
  const { put, post } = useApi();
  const { add } = useSnack();
  const router = useRouter();
  const searchParams = useSearchParams();


  const handleSubmit = async (e) => {
    try {
      e.preventDefault();
      const data = Object.fromEntries(new FormData(e.target));
      const url = item ? `/location-areas/${item.uuid}` : `/location-areas`;
      const fetch = item ? put : post;
      await fetch(url, data);
      add("success", "Le canton a bien été modifié !");
      router.push(ROUTES.CANTONS + FormatUtils.formatUrl(searchParams));
      router.refresh();
    } catch (error) {
      add("error", error?.description);
      console.log(error);
    }
  };

  return (
    <Stack component={Paper} p={5} gap={3}>
      <Typography variant="h4">Modifier le canton</Typography>
      <Stack gap={2} component="form" onSubmit={handleSubmit}>
        <Stack direction="row" gap={2}>
          <Input defaultValue={item?.name} label="Nom" name="name" fullWidth required />
          <SelectDepartment defaultValue={item?.department} required />
          <SelectStores defaultValue={item?.agency} required />
        </Stack>
        <Stack direction="row" justifyContent="flex-end" gap={2}>
          <Button color="secondary" onClick={() => router.push(ROUTES.CANTONS + FormatUtils.formatUrl(searchParams))}>
            Retour
          </Button>
          <Button type="submit">Enregistrer</Button>
        </Stack>
      </Stack>
    </Stack>
  );
}

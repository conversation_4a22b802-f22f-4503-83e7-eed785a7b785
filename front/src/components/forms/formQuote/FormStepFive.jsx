import { Box, Grid2, Stack, Typography } from "@mui/material";
import ButtonStepChoice from "@/components/blocks/BlockQuote/ButtonStepChoice";
import { useState } from "react";
import ButtonsNextPrev from "@/components/blocks/BlockQuote/ButtonsNextPrev";

export default function FormStepFive({ currentStep, setCurrentStep, setBreadcrumb }) {
  const [elec, setElec] = useState(null);
  const [gaz, setGaz] = useState(null);

  if (5 !== currentStep) {
    return null;
  }

  const goToPrev = () => {
    setCurrentStep(4);
    setBreadcrumb((breadcrumb) => ({
      step_1: breadcrumb.step_1,
      step_2: breadcrumb.step_2,
      step_3: breadcrumb.step_3,
      step_4: breadcrumb.step_4,
    }));
  };

  const goToNext = () => {
    if (elec && gaz) {
      setCurrentStep(6);
      setBreadcrumb((breadcrumb) => ({
        ...breadcrumb,
        step_5: {
          elec,
          gaz,
        },
      }));
    }
  };

  return (
    <Stack spacing={2} flexGrow={1}>
      <Grid2 container spacing={2}>
        <Grid2
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Stack spacing={1} alignItems="center">
            <Typography variant="body2">
              Installation gaz + de 15 ans
            </Typography>
            <Stack direction="row" spacing={1}>
              <Box>
                <ButtonStepChoice
                  index="oui"
                  selected={gaz}
                  setSelected={setGaz}
                >
                  Oui
                </ButtonStepChoice>
              </Box>
              <Box>
                <ButtonStepChoice
                  index="non"
                  selected={gaz}
                  setSelected={setGaz}
                >
                  Non
                </ButtonStepChoice>
              </Box>
            </Stack>
          </Stack>
        </Grid2>
        <Grid2
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Stack spacing={1} alignItems="center">
            <Typography variant="body2">
              Installation élec. + de 15 ans
            </Typography>
            <Stack direction="row" spacing={1}>
              <Box>
                <ButtonStepChoice
                  index="oui"
                  selected={elec}
                  setSelected={setElec}
                >
                  Oui
                </ButtonStepChoice>
              </Box>
              <Box>
                <ButtonStepChoice
                  index="non"
                  selected={elec}
                  setSelected={setElec}
                >
                  Non
                </ButtonStepChoice>
              </Box>
            </Stack>
          </Stack>
        </Grid2>
      </Grid2>
      <ButtonsNextPrev onClickNext={goToNext} onClickPrev={goToPrev} nextIsDisabled={elec === null || gaz === null} />
    </Stack>
  );
}

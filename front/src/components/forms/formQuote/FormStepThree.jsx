"use client";
import { Grid2, Stack, Typography } from "@mui/material";
import { useState } from "react";
import ButtonStepChoice from "@/components/blocks/BlockQuote/ButtonStepChoice";
import ButtonsNextPrev from "@/components/blocks/BlockQuote/ButtonsNextPrev";
import <PERSON>ctoHouse from "@public/assets/pictos/PictoHouse";
import PictoAppartment from "@public/assets/pictos/PictoAppartment";
import PictoCopro from "@public/assets/pictos/PictoCopro";
import PictoHous2 from "@public/assets/pictos/PictoHouse2";
import { PROPERTY_TYPE } from "@/enums/ESTIMATES";

export default function FormStepThree({ currentStep, setCurrentStep, setBreadcrumb }) {
  const [selected, setSelected] = useState(null);

  if (3 !== currentStep) {
    return null;
  }

  const goToNext = (selected) => {
    if (selected) {
      // Condition below makes 422 missing roomNumber in step 4
      // if (selected === PROPERTY_TYPE.OTHER) {
      //   setCurrentStep(6);
      // } else {
      //   setCurrentStep(4);
      // }
      setCurrentStep(4);
      setBreadcrumb((breadcrumb) => ({
        ...breadcrumb,
        step_3: selected,
      }));
    }
  };

  const goToPrev = () => {
    setCurrentStep(2);
  };

  return (
    <Stack spacing={2} width={1}>
      <Typography variant="body2">Type de bien</Typography>
      <Stack spacing={2} alignItems="center">
        <Grid2 container spacing={1} justifyContent="center">
          <Grid2
            size={{
              xs: 12,
              sm: 3,
            }}
          >
            <ButtonStepChoice
              index={PROPERTY_TYPE.APARTMENT}
              selected={selected}
              setSelected={setSelected}
              goToNext={goToNext}
            >
              <PictoAppartment />
              Appartement
            </ButtonStepChoice>
          </Grid2>
          <Grid2
            size={{
              xs: 12,
              sm: 3,
            }}
          >
            <ButtonStepChoice
              index={PROPERTY_TYPE.DETACHED_HOUSE}
              selected={selected}
              setSelected={setSelected}
              goToNext={goToNext}
            >
              <PictoHouse />
              Maison individuelle
            </ButtonStepChoice>
          </Grid2>
          <Grid2
            size={{
              xs: 12,
              sm: 3,
            }}
          >
            <ButtonStepChoice
              index={PROPERTY_TYPE.CO_OWNED_HOUSE}
              selected={selected}
              setSelected={setSelected}
              goToNext={goToNext}
            >
              <PictoCopro />
              Maison en copropriété
            </ButtonStepChoice>
          </Grid2>
          <Grid2
            size={{
              xs: 12,
              sm: 3,
            }}
          >
            <ButtonStepChoice
              index={PROPERTY_TYPE.OTHER}
              selected={selected}
              setSelected={setSelected}
              goToNext={goToNext}
            >
              <PictoHous2 />
              Autre
            </ButtonStepChoice>
          </Grid2>
        </Grid2>
        <ButtonsNextPrev
          setCurrentStep={setCurrentStep}
          onClickPrev={goToPrev}
        />
      </Stack>
    </Stack>
  );
}

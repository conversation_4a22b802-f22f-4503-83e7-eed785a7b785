import { Box, Stack, Typography } from "@mui/material";
import { useState } from "react";
import ButtonStepChoice from "@/components/blocks/BlockQuote/ButtonStepChoice";
import ButtonsNextPrev from "@/components/blocks/BlockQuote/ButtonsNextPrev";
import { BUILD_YEAR_TRANSLATED, ESTIMATE_TYPE, PROPERTY_TYPE, ROOM_NUMBER } from "@/enums/ESTIMATES";
import Input from "@/components/inputs/Input";

export default function FormStepFour({ currentStep, setCurrentStep, breadcrumb, setBreadcrumb }) {
  const yearChoices = Object.keys(BUILD_YEAR_TRANSLATED);
  const countRoomsChoices = Object.values(ROOM_NUMBER);
  const [propertyType, setPropertyType] = useState(null);
  const [countRooms, setCountRooms] = useState(null);
  const [year, setYear] = useState(null);

  if (4 !== currentStep) {
    return null;
  }

  const goToNext = () => {
    if ((countRooms && year) || (countRooms && breadcrumb.step_2 === ESTIMATE_TYPE.AUDIT)) {
      if (breadcrumb.step_2 === ESTIMATE_TYPE.AUDIT) {
        setCurrentStep(6);
      } else {
        setCurrentStep(5);
      }
      setBreadcrumb((breadcrumb) => ({
        ...breadcrumb,
        step_4: {
          customPropertyType: propertyType,
          countRooms: countRooms,
          year: year || "",
        },
      }));
    }
  };

  const goToPrev = () => {
    if (breadcrumb.step_2 === ESTIMATE_TYPE.AUDIT) {
      setCurrentStep(2);
      setBreadcrumb((breadcrumb) => ({
        step_1: breadcrumb.step_1,
      }));
    } else {
      setCurrentStep(3);
      setBreadcrumb((breadcrumb) => ({
        step_1: breadcrumb.step_1,
        step_2: breadcrumb.step_2,
      }));
    }
  };

  return (
    <Stack spacing={2}>
      <Stack spacing={1}>
        {breadcrumb.step_3 === PROPERTY_TYPE.OTHER ? (
          <>
            <Typography variant="body2">Type de propriété</Typography>
            <Input
              placeholder="Type de propriété*"
              required
              fullWidth
              name="customPropertyType"
              onChange={(e) => setPropertyType(e.target.value)}
              defaultValue={propertyType}
            />
          </>
        ) : null}
        <Typography variant="body2">Nombre de pièces</Typography>
        <Stack direction="row" flexWrap="wrap" gap={1}>
          {countRoomsChoices.map((n) => (
            <Box key={n}>
              <ButtonStepChoice index={n} selected={countRooms} setSelected={setCountRooms} fullWidth={false}>
                {n}
              </ButtonStepChoice>
            </Box>
          ))}
        </Stack>
      </Stack>
      {breadcrumb.step_2 !== ESTIMATE_TYPE.AUDIT ? (
        <Stack spacing={1} flexWrap="wrap">
          <Typography variant="body2">Année de construction</Typography>
          <Stack direction="row" gap={1} flexWrap="wrap">
            {yearChoices.map((choice, i) => (
              <Box key={i}>
                <ButtonStepChoice key={i} size="small" index={choice} selected={year} setSelected={setYear} fullWidth={false}>
                  {BUILD_YEAR_TRANSLATED[choice]}
                </ButtonStepChoice>
              </Box>
            ))}
          </Stack>
        </Stack>
      ) : null}
      <ButtonsNextPrev
        setCurrentStep={setCurrentStep}
        onClickNext={goToNext}
        onClickPrev={goToPrev}
        nextIsDisabled={countRooms === null || (year === null && breadcrumb.step_2 !== ESTIMATE_TYPE.AUDIT) || (
          breadcrumb.step_3 === PROPERTY_TYPE.OTHER && propertyType === null
        )}
      />
    </Stack>
  );
}

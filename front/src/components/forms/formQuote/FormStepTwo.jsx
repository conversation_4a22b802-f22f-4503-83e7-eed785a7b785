"use client";
import { Grid2, Stack, Typography } from "@mui/material";
import { useState } from "react";
import PictoMains from "@public/assets/pictos/PictoMains";
import PictoRent from "@public/assets/pictos/PictoRent";
import PictoRent2 from "@public/assets/pictos/PictoRent2";
import ButtonStepChoice from "@/components/blocks/BlockQuote/ButtonStepChoice";
import ButtonsNextPrev from "@/components/blocks/BlockQuote/ButtonsNextPrev";
import { ESTIMATE_TYPE } from "@/enums/ESTIMATES";

export default function FormStepTwo({ currentStep, setCurrentStep, setBreadcrumb }) {
  const [selected, setSelected] = useState(null);

  if (2 !== currentStep) {
    return null;
  }

  const goToNext = (selected) => {
    if (selected) {
      setCurrentStep(3);
      setBreadcrumb((breadcrumb) => ({
        ...breadcrumb,
        step_2: selected,
      }));
    }
  };

  const goToPrev = () => {
    setCurrentStep(1);
  };

  return (
    <Stack spacing={2} width={1}>
      <Typography variant="body2">Vous souhaitez</Typography>
      <Stack spacing={2} alignItems="center">
        <Grid2 container spacing={1} justifyContent="center" width="100%">
          <Grid2
            size={{
              xs: 12,
              sm: 4,
            }}
          >
            <ButtonStepChoice index={ESTIMATE_TYPE.SELL} selected={selected} setSelected={setSelected} goToNext={goToNext}>
              <PictoMains />
              Vendre
            </ButtonStepChoice>
          </Grid2>
          <Grid2
            size={{
              xs: 12,
              sm: 4,
            }}
          >
            <ButtonStepChoice index={ESTIMATE_TYPE.RENT} selected={selected} setSelected={setSelected} goToNext={goToNext}>
              <PictoRent2 />
              Louer
            </ButtonStepChoice>
          </Grid2>
          <Grid2
            size={{
              xs: 12,
              sm: 4,
            }}
          >
            <ButtonStepChoice index={ESTIMATE_TYPE.AUDIT} selected={selected} setSelected={setSelected} goToNext={goToNext}>
              <PictoRent />
              Audit réglementaire
            </ButtonStepChoice>
          </Grid2>
        </Grid2>
        <ButtonsNextPrev onClickPrev={goToPrev} />
      </Stack>
    </Stack>
  );
}

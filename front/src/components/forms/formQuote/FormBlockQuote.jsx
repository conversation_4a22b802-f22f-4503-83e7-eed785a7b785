import Input from "@/components/inputs/Input";
import InputId from "@/components/inputs/InputId";
import InputMargin from "@/components/inputs/InputMargin";
import InputMedias from "@/components/inputs/InputMedias";
import InputWysiwyg from "@/components/inputs/wysiwyg/InputWysiwyg";
import { getBlockFieldValue } from "@/utils/builder.utils";
import { <PERSON><PERSON>, Stack } from "@mui/material";

export const FIELD_NAMES = {
  TEXT: "text",
  FORM_TEXT: "formText",
  FILE: "file",
  MT: "mt",
  MB: "mb",
  LINK_DIAGNOSTIC: "linkDiagnostic",
  LINK_RDV: "linkRdv",
};

export default function FormBlockQuote({ bloc, addBloc }) {
  const mt = getBlockFieldValue(FIELD_NAMES.MT, bloc, 20);
  const mb = getBlockFieldValue(FIELD_NAMES.MB, bloc, 20);
  const linkDiagnostic = getBlockFieldValue(FIELD_NAMES.LINK_DIAGNOSTIC, bloc, "");
  const linkRdv = getBlockFieldValue(FIELD_NAMES.LINK_RDV, bloc, "");
  const anchor = getBlockFieldValue("anchor", bloc, "");

  const defaultValue = { ...bloc };

  const handleSubmit = (e) => {
    e.preventDefault();
    defaultValue.parameters = [
      {
        type: FIELD_NAMES.TEXT,
        value: e.target[FIELD_NAMES.TEXT].value,
      },
      {
        type: FIELD_NAMES.FILE,
        value: e.target[FIELD_NAMES.FILE].value,
      },
      {
        type: FIELD_NAMES.FORM_TEXT,
        value: e.target[FIELD_NAMES.FORM_TEXT].value,
      },
      {
        type: FIELD_NAMES.LINK_DIAGNOSTIC,
        value: e.target[FIELD_NAMES.LINK_DIAGNOSTIC].value,
      },
      {
        type: FIELD_NAMES.LINK_RDV,
        value: e.target[FIELD_NAMES.LINK_RDV].value,
      },
      {
        type: FIELD_NAMES.MT,
        value: e.target[FIELD_NAMES.MT].value,
      },
      {
        type: FIELD_NAMES.MB,
        value: e.target[FIELD_NAMES.MB].value,
      },
      {
        type: "anchor",
        value: e.target.anchor.value,
      }
    ];
    addBloc(defaultValue);
  };

  return (
    <form onSubmit={handleSubmit}>
      <Stack spacing={2}>
        <InputMedias label="Bannière" name="file" defaultValue={getBlockFieldValue(FIELD_NAMES.FILE, bloc) || ""} />
        <InputWysiwyg name={FIELD_NAMES.TEXT} label="Texte" defaultValue={getBlockFieldValue(FIELD_NAMES.TEXT, bloc) || ""} />
        <InputWysiwyg name={FIELD_NAMES.FORM_TEXT} label="Titre du formulaire" defaultValue={getBlockFieldValue(FIELD_NAMES.FORM_TEXT, bloc) || ""} />
        <Input name={FIELD_NAMES.LINK_DIAGNOSTIC} label="Lien trouver un diagnostiqueur" defaultValue={linkDiagnostic} fullWidth />
        <Input name={FIELD_NAMES.LINK_RDV} label="Lien prendre un rendez-vous" defaultValue={linkRdv} fullWidth />
        <InputMargin defaultValue={{ mt, mb }} />
        <InputId defaultValue={anchor} />
        <Button type="submit">Valider</Button>
      </Stack>
    </form>
  );
}

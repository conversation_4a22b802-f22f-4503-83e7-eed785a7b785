"use client";
import { getBlockFieldValue } from "@/utils/builder.utils";
import { Paper, Stack, Typography } from "@mui/material";
import { useState } from "react";
import { FIELD_NAMES } from "./FormBlockQuote";
import FormStepFive from "./FormStepFive";
import FormStepFour from "./FormStepFour";
import FormStepOne from "./FormStepOne";
import FormStepSix from "./FormStepSix";
import FormStepThree from "./FormStepThree";
import FormStepTwo from "./FormStepTwo";

export default function FormQuote({ block }) {
  const defaultStep = getBlockFieldValue("defaultStep", block, false);
  const [currentStep, setCurrentStep] = useState(defaultStep ? 2 : 1);
  const [breadcrumb, setBreadcrumb] = useState({});

  return (
    <Paper>
      <Stack spacing={2} p={4}>
        <Stack spacing={4}>
          <Typography
            component="div"
            dangerouslySetInnerHTML={{
              __html: getBlockFieldValue(FIELD_NAMES.FORM_TEXT, block),
            }}
          />
          <Stack direction="row" spacing={1}>
            <FormStepOne block={block} currentStep={currentStep} setCurrentStep={setCurrentStep} setBreadcrumb={setBreadcrumb} />
            <FormStepTwo currentStep={currentStep} setCurrentStep={setCurrentStep} setBreadcrumb={setBreadcrumb} />
            <FormStepThree currentStep={currentStep} setCurrentStep={setCurrentStep} setBreadcrumb={setBreadcrumb} />
            <FormStepFour currentStep={currentStep} setCurrentStep={setCurrentStep} breadcrumb={breadcrumb} setBreadcrumb={setBreadcrumb} />
            <FormStepFive currentStep={currentStep} setCurrentStep={setCurrentStep} setBreadcrumb={setBreadcrumb} />
            <FormStepSix breadcrumb={breadcrumb} currentStep={currentStep} setCurrentStep={setCurrentStep} setBreadcrumb={setBreadcrumb} />
          </Stack>
        </Stack>
      </Stack>
    </Paper>
  );
}

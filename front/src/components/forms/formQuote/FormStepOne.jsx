"use client";

import { Grid2, Stack, Typography } from "@mui/material";
import PictoCalendar from "@public/assets/pictos/PictoCalendar";
import PictoNote from "@public/assets/pictos/PictoNote";
import PictoSearchUser from "@public/assets/pictos/PictoSearchUser";
import ButtonStepChoice from "@/components/blocks/BlockQuote/ButtonStepChoice";
import { FIELD_NAMES } from "../formBlocks/FormBlockQuoteSingle";
import { getBlockFieldValue } from "@/utils/builder.utils";

export default function FormStepOne({ currentStep, setCurrentStep, block }) {
  if (currentStep !== 1) {
    return null;
  }
  const linkDiagnostic = getBlockFieldValue(FIELD_NAMES.LINK_DIAGNOSTIC, block, "/");
  const linkRdv = getBlockFieldValue(FIELD_NAMES.LINK_RDV, block, "/");

  return (
    <Stack spacing={2} width={1}>
      <Typography variant="body2">Vous souhaitez</Typography>
      <Stack spacing={2} alignItems="center">
        <Grid2 container spacing={1} justifyContent="center" width="100%">
          <Grid2
            size={{
              xs: 12,
              sm: 4,
            }}
          >
            <ButtonStepChoice onClick={() => setCurrentStep((currentstep) => currentstep + 1)}>
              <PictoSearchUser />
              Faire un devis
            </ButtonStepChoice>
          </Grid2>
          <Grid2
            size={{
              xs: 12,
              sm: 4,
            }}
          >
            <ButtonStepChoice href={linkDiagnostic}>
              <PictoNote />
              Trouver un diagnostiqueur
            </ButtonStepChoice>
          </Grid2>
          <Grid2
            size={{
              xs: 12,
              sm: 4,
            }}
          >
            <ButtonStepChoice href={linkRdv}>
              <PictoCalendar />
              Prendre rendez-vous
            </ButtonStepChoice>
          </Grid2>
        </Grid2>
      </Stack>
    </Stack>
  );
}

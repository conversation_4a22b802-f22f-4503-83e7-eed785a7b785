"use client";

import Input from "@/components/inputs/Input";
import SelectCommune from "@/components/inputs/SelectCommune";
import { ESTIMATE_TYPE, PROPERTY_TYPE } from "@/enums/ESTIMATES";
import ROUTES from "@/enums/ROUTES";
import useDebounce from "@/hooks/useDebounce";
import { Box, Button, Checkbox, FormControlLabel, Grid2, Link, Stack, Typography } from "@mui/material";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";

export default function FormStepSix({ breadcrumb, setBreadcrumb, currentStep, setCurrentStep }) {
  const router = useRouter();
  const [zip, setZip] = useState("");
  const [form, setForm] = useState({
    city: "",
    customerEmail: "",
    customerPhone: "",
  });
  const debouncedZip = useDebounce((e) => setZip(e.target.value), 500);
  const formIsCompleted = form.city && form.customerEmail && form.customerPhone;

  if (currentStep !== 6) return null;

  const goToPrev = () => {
    if (breadcrumb.step_3 === PROPERTY_TYPE.OTHER) {
      setCurrentStep(3);
      setBreadcrumb((breadcrumb) => ({
        step_1: breadcrumb.step_1,
        step_2: breadcrumb.step_2,
      }));
    } else if (breadcrumb.step_2 === ESTIMATE_TYPE.AUDIT) {
      setCurrentStep(2);
      setBreadcrumb((breadcrumb) => ({
        step_1: breadcrumb.step_1,
      }));
    } else {
      setCurrentStep(5);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    localStorage.setItem(
      "estimate",
      JSON.stringify({
        type: breadcrumb.step_2,
        propertyType: breadcrumb.step_3,
        customPropertyType: breadcrumb.step_4?.customPropertyType || null,
        roomNumber: breadcrumb.step_4?.countRooms,
        buildYear: breadcrumb.step_4?.year,
        gazOlderThan15Years: breadcrumb.step_5?.gaz === "oui",
        electricityOlderThan15Years: breadcrumb.step_5?.elec === "oui",
        opportunityWanted: {},
        appointmentDate: null,
        discountCode: null,
        ...form,
      })
    );
    router.push(ROUTES.DEVIS_ANSWER);
  };

  const handleChange = (e) => {
    setForm({
      ...form,
      [e.target.name]: e.target.value,
    });
  };

  return (
    <Stack gap={2} component="form" onSubmit={handleSubmit}>
      <Grid2 container spacing={1}>
        <Grid2
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Input required placeholder="Code postal*" onChange={debouncedZip} fullWidth />
        </Grid2>
        <Grid2
          size={{
            xs: 12,
            md: 6,
          }}
        >
          {zip?.length !== 5 ? (
            <Input required placeholder="Commune*" disabled fullWidth />
          ) : (
            <SelectCommune
              name="commune"
              zip={zip}
              noLabel
              required
              onChange={(e) =>
                setForm((f) => ({
                  ...f,
                  city: e.value,
                }))
              }
            />
          )}
        </Grid2>
        <Grid2
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Input placeholder="Téléphone*" required fullWidth name="customerPhone" onChange={handleChange} defaultValue={form.customerPhone} />
        </Grid2>
        <Grid2
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Input
            name="customerEmail"
            type="email"
            required
            placeholder="Email*"
            fullWidth
            onChange={handleChange}
            defaultValue={form.customerEmail}
          />
        </Grid2>
        <Grid2
          size={{
            xs: 12,
          }}
        >
          <Stack>
            <FormControlLabel
              sx={{ height: 30 }}
              required
              control={<Checkbox name="cgv" />}
              label={
                <Typography sx={{ lineHeight: 1, fontSize: { xs: "12px", sm: "14px" } }} component="span">
                  J&apos;ai lu et accepté les conditions générales de ventes.
                </Typography>
              }
            />
            <FormControlLabel
              sx={{ height: 30 }}
              control={<Checkbox name="newsletter" />}
              label={
                <Typography sx={{ lineHeight: 1, fontSize: { xs: "12px", sm: "14px" } }} component="span">
                  J&apos;accepte de recevoir les offres d&apos;AGENDA Diagnostics.
                </Typography>
              }
            />
            <Typography variant="subtitle2" pt={1}>
              Si vous ne souhaitez pas faire l&apos;objet de prospection commerciale par voie téléphonique, vous pouvez vous inscrire gratuitement sur
              la liste d&apos;opposition au démarchage téléphonique Bloctel (
              <Link sx={{ textDecoration: "underline" }} href="https://www.bloctel.gouv.fr" target="_blank" rel="noreferrer">
                www.bloctel.gouv.fr
              </Link>
              )
            </Typography>
          </Stack>
        </Grid2>
      </Grid2>
      <Stack
        direction="row"
        spacing={1}
        justifyContent="center"
        sx={{
          "& button": {
            fontWeight: "bold",
          },
        }}
      >
        <Box>
          <Button size="small" variant="contained" color="primary" onClick={goToPrev}>
            Précedent
          </Button>
        </Box>
        <Box>
          <Button size="small" variant="contained" color="primary" type="submit" disabled={!formIsCompleted}>
            Terminé
          </Button>
        </Box>
      </Stack>
    </Stack>
  );
}

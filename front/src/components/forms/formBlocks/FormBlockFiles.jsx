"use client";

import { <PERSON><PERSON>, <PERSON><PERSON> } from "@mui/material";
import { useState } from "react";
import Input from "@/components/inputs/Input";
import { v4 as uuidv4 } from "uuid";
import { getBlockFieldValue } from "@/utils/builder.utils";
import { BLOC_TYPES } from "@/enums/BLOC_TYPES";
import { BLOC_LABEL } from "@/enums/BLOC_LABEL";
import InputFileRow from "@/components/inputs/InputFileRow";
import InputMargin from "@/components/inputs/InputMargin";
import InputId from "../../inputs/InputId";

export const FIELD_NAMES = {
	TITLE: "title",
	ROWS: "rows",
	MT: "mt",
	MB: "mb",
};

export default function FormBlocFiles({ bloc, addBloc }) {
	const title = getBlockFieldValue(FIELD_NAMES.TITLE, bloc, "");
	const [rows, setRows] = useState(
		getBlockFieldValue(FIELD_NAMES.ROWS, bloc) || []
	);
	const mt = getBlockFieldValue(FIELD_NAMES.MT, bloc, 20);
	const mb = getBlockFieldValue(FIELD_NAMES.MB, bloc, 20);
  const anchor = getBlockFieldValue("anchor", bloc, "");

	const handleSubmit = (e) => {
		e.preventDefault();

		const uuid = bloc.uuid ?? uuidv4();
		const b = bloc || {
			uuid,
			type: BLOC_TYPES.BLOCK_FILES,
			name: "Tableau de fichiers",
		};

		addBloc({
			...b,
			parameters: [
				{
					type: FIELD_NAMES.TITLE,
					value: e.target[FIELD_NAMES.TITLE].value,
				},
				{
					type: FIELD_NAMES.ROWS,
					value: rows || [],
				},
				{
					type: FIELD_NAMES.MT,
					value: e.target.mt.value,
				},
				{
					type: FIELD_NAMES.MB,
					value: e.target.mb.value,
				},
        {
          type: "anchor",
          value: e.target.anchor.value,
        },
			],
		});
	};

	return (
		<form onSubmit={handleSubmit}>
			<Stack spacing={2}>
				<Input
					label="Titre du tableau"
					name={FIELD_NAMES.TITLE}
					defaultValue={title}
					fullWidth
				/>
				<InputFileRow
					label="Lignes du tableau"
					defaultValue={
						getBlockFieldValue(FIELD_NAMES.ROWS, bloc) || []
					}
					onChange={(val) => setRows(val)}
				/>
				<InputMargin defaultValue={{ mt, mb }} />
        <InputId defaultValue={anchor} />
				<Button type="submit">Valider</Button>
			</Stack>
		</form>
	);
}

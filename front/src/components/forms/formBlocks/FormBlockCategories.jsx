"use client";
import { BLOC_TYPES } from "@/enums/BLOC_TYPES";
import { getBlockFieldValue } from "@/utils/builder.utils";
import { <PERSON><PERSON>, Stack } from "@mui/material";
import { useState } from "react";
import { v4 as uuidv4 } from "uuid";
import InputWysiwyg from "@/components/inputs/wysiwyg/InputWysiwyg";
import InputCategories from "@/components/inputs/InputCategories";
import InputMargin from "../../inputs/InputMargin";
import InputId from "../../inputs/InputId";

const FIELD_NAMES = {
  TITLE: "title",
  CATEGORIES: "categories",
  MT: "mt",
  MB: "mb",
};

export default function FormBlockCategories({ bloc, addBloc }) {
  const mt = getBlockFieldValue(FIELD_NAMES.MT, bloc, 20);
  const mb = getBlockFieldValue(FIELD_NAMES.MB, bloc, 20);
  const anchor = getBlockFieldValue("anchor", bloc, "");
  const [categories, setCategories] = useState(getBlockFieldValue(FIELD_NAMES.CATEGORIES, bloc) || []);

  const handleSubmit = (e) => {
    e.preventDefault();

    const uuid = bloc.uuid ?? uuidv4();
    const b = bloc || {
      uuid,
      type: BLOC_TYPES.BLOCK_CATEGORIES,
      name: "Tableau de fichiers",
    };

    addBloc({
      ...b,
      parameters: [
        {
          type: FIELD_NAMES.TITLE,
          value: e.target[FIELD_NAMES.TITLE].value,
        },
        {
          type: FIELD_NAMES.CATEGORIES,
          value: categories || [],
        },
        {
          type: FIELD_NAMES.MT,
          value: e.target[FIELD_NAMES.MT].value,
        },
        {
          type: FIELD_NAMES.MB,
          value: e.target[FIELD_NAMES.MB].value,
        },
        {
          type: "anchor",
          value: e.target.anchor.value,
        },
      ],
    });
  };

  return (
    <form onSubmit={handleSubmit}>
      <Stack gap={2}>
        <InputWysiwyg label="Titre" name={FIELD_NAMES.TITLE} defaultValue={getBlockFieldValue(FIELD_NAMES.TITLE, bloc) || ""} />
        <InputCategories
          label="Catégories"
          defaultValue={getBlockFieldValue(FIELD_NAMES.CATEGORIES, bloc) || []}
          onChange={(val) => setCategories(val)}
        />
        <InputMargin defaultValue={{ mt, mb }} />
        <InputId defaultValue={anchor} />
        <Button type="submit">Valider</Button>
      </Stack>
    </form>
  );
}

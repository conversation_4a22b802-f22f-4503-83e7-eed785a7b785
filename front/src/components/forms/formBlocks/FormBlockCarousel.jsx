"use client";
import { BLOC_TYPES } from "@/enums/BLOC_TYPES";
import { getBlockFieldValue } from "@/utils/builder.utils";
import { <PERSON><PERSON>, Stack } from "@mui/material";
import { useState } from "react";
import { v4 as uuidv4 } from "uuid";
import InputSlideCarousel from "@/components/blocks/BlockCarousel/InputSlideCarousel";
import Input from "../../inputs/Input";
import InputMargin from "../../inputs/InputMargin";
import InputId from "../../inputs/InputId";

const FIELD_NAMES = {
  TITLE: "title",
  LINK: "link",
  ITEMS: "items",
  MT: "mt",
  MB: "mb",
};

export default function FormBlockCarousel({ bloc, addBloc }) {
  const mt = getBlockFieldValue(FIELD_NAMES.MT, bloc, 20);
  const mb = getBlockFieldValue(FIELD_NAMES.MB, bloc, 20);
  const anchor = getBlockFieldValue("anchor", bloc, "");
  const [items, setItems] = useState(getBlockFieldValue(FIELD_NAMES.ITEMS, bloc) || []);

  const handleSubmit = (e) => {
    e.preventDefault();

    const uuid = bloc.uuid ?? uuidv4();
    const b = bloc || {
      uuid,
      type: BLOC_TYPES.BLOCK_CATEGORIES,
      name: "Tableau de fichiers",
    };

    addBloc({
      ...b,
      parameters: [
        {
          type: FIELD_NAMES.TITLE,
          value: e.target[FIELD_NAMES.TITLE].value,
        },
        {
          type: FIELD_NAMES.LINK,
          value: e.target[FIELD_NAMES.LINK].value,
        },
        {
          type: FIELD_NAMES.ITEMS,
          value: items || [],
        },
        {
          type: FIELD_NAMES.MT,
          value: mt,
        },
        {
          type: FIELD_NAMES.MB,
          value: mb,
        },
        {
          type: "anchor",
          value: e.target.anchor.value,
        },
      ],
    });
  };

  return (
    <form onSubmit={handleSubmit}>
      <Stack spacing={2}>
        <Input label="Titre" name={FIELD_NAMES.TITLE} defaultValue={getBlockFieldValue(FIELD_NAMES.TITLE, bloc)} fullWidth />
        <Input label="Lien vers la page" name={FIELD_NAMES.LINK} defaultValue={getBlockFieldValue(FIELD_NAMES.LINK, bloc)} fullWidth />
        <InputSlideCarousel label="Cartes" defaultValue={getBlockFieldValue(FIELD_NAMES.ITEMS, bloc) || []} onChange={(val) => setItems(val)} />
        <InputMargin defaultValue={{ mt, mb }} />
        <InputId defaultValue={anchor} />
        <Button type="submit">Valider</Button>
      </Stack>
    </form>
  );
}

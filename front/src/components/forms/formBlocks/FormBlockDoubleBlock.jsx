"use client";

import { <PERSON><PERSON>, Divider, Stack, styled, Typography } from "@mui/material";
import { useState } from "react";
import InputMedias from "@/components/inputs/InputMedias";
import InputWysiwyg from "@/components/inputs/wysiwyg/InputWysiwyg";
import { getBlockFieldValue } from "@/utils/builder.utils";
import InputButton from "../../inputs/InputButton";
import InputMargin from "../../inputs/InputMargin";
import arrow from "@public/assets/pictos/arrow.svg";
import InputId from "../../inputs/InputId";

export const FIELD_NAMES = {
  CONTENT_FIRST: "contentFirst",
  CONTENT_SECOND: "contentSecond",
  BACKGROUND_FIRST: "backgroundFirst",
  BACKGROUND_SECOND: "backgroundSecond",
  BUTTON_FIRST: "buttonFirst",
  BUTTON_SECOND: "buttonSecond",
  MT: "mt",
  MB: "mb",
};

const StyledTextContainer = styled(Stack)(({ theme }) => ({
  border: "1px solid " + theme.palette.grey.main,
  padding: theme.spacing(2),
  borderColor: theme.palette.grey.main,
  borderRadius: theme.shape.borderRadius,
  ".tiptap": {
    "& ul": {
      padding: "0px !important",
    },
    "& li": {
      display: "flex",
      alignItems: "center",
      "& p span:after": {
        content: "'\\2192'",
        marginLeft: theme.spacing(1),
        fontSize: "16px",
      },

      "& p:not(:has(span)):after": {
        content: "'\\2192'",
        marginLeft: theme.spacing(1),
        fontSize: "16px",
      },
      "&:after": {
        content: "''",
      },
    },
  },
}));

export default function FormBlockDoubleBlock({ bloc, addBloc }) {
  const [buttonFirst, setButtonFirst] = useState(getBlockFieldValue(FIELD_NAMES.BUTTON_FIRST, bloc));
  const [buttonSecond, setButtonSecond] = useState(getBlockFieldValue(FIELD_NAMES.BUTTON_SECOND, bloc));
  const anchor = getBlockFieldValue("anchor", bloc, "");

  const defaultValue = { ...bloc };

  const handleSubmit = (e) => {
    e.preventDefault();
    defaultValue.parameters = [
      {
        type: FIELD_NAMES.CONTENT_FIRST,
        value: e.target[FIELD_NAMES.CONTENT_FIRST].value,
      },
      {
        type: FIELD_NAMES.CONTENT_SECOND,
        value: e.target[FIELD_NAMES.CONTENT_SECOND].value,
      },
      {
        type: FIELD_NAMES.BUTTON_FIRST,
        value: buttonFirst,
      },
      {
        type: FIELD_NAMES.BUTTON_SECOND,
        value: buttonSecond,
      },
      {
        type: FIELD_NAMES.BACKGROUND_FIRST,
        value: e.target[FIELD_NAMES.BACKGROUND_FIRST].value,
      },
      {
        type: FIELD_NAMES.BACKGROUND_SECOND,
        value: e.target[FIELD_NAMES.BACKGROUND_SECOND].value,
      },
      {
        type: FIELD_NAMES.MT,
        value: e.target[FIELD_NAMES.MT].value,
      },
      {
        type: FIELD_NAMES.MB,
        value: e.target[FIELD_NAMES.MB].value,
      },
      {
        type: "anchor",
        value: e.target.anchor.value,
      },
    ];
    addBloc(defaultValue);
  };

  return (
    <Stack component="form" gap={2} onSubmit={handleSubmit}>
      <Stack>
        <Typography variant="h6" color="grey">
          Bloc 1
        </Typography>
        <StyledTextContainer gap={2}>
          <InputMedias
            label="Image de fond"
            name={FIELD_NAMES.BACKGROUND_FIRST}
            defaultValue={getBlockFieldValue(FIELD_NAMES.BACKGROUND_FIRST, bloc)}
          />
          <InputWysiwyg name={FIELD_NAMES.CONTENT_FIRST} label="Texte" defaultValue={getBlockFieldValue(FIELD_NAMES.CONTENT_FIRST, bloc)} />
          <InputButton defaultValue={buttonFirst} onChange={(val) => setButtonFirst(val)} />
        </StyledTextContainer>
      </Stack>
      <Divider />
      <Stack>
        <Typography variant="h6" color="grey">
          Bloc 2
        </Typography>
        <StyledTextContainer spacing={2}>
          <InputMedias
            label="Image de fond"
            name={FIELD_NAMES.BACKGROUND_SECOND}
            defaultValue={getBlockFieldValue(FIELD_NAMES.BACKGROUND_SECOND, bloc)}
          />
          <InputWysiwyg name={FIELD_NAMES.CONTENT_SECOND} label="Texte" defaultValue={getBlockFieldValue(FIELD_NAMES.CONTENT_SECOND, bloc)} />
          <InputButton defaultValue={buttonSecond} onChange={(val) => setButtonSecond(val)} />
        </StyledTextContainer>
      </Stack>
      <InputMargin defaultValue={{ mt: getBlockFieldValue(FIELD_NAMES.MT, bloc, 20), mb: getBlockFieldValue(FIELD_NAMES.MB, bloc, 20) }} />
      <InputId defaultValue={anchor} />
      <Button type="submit">Valider</Button>
    </Stack>
  );
}

import { <PERSON><PERSON>, <PERSON><PERSON> } from "@mui/material";
import InputWysiwyg from "@/components/inputs/wysiwyg/InputWysiwyg";
import InputMedias from "@/components/inputs/InputMedias";
import InputMargin from "@/components/inputs/InputMargin";
import { getBlockFieldValue } from "@/utils/builder.utils";
import InputId from "../../inputs/InputId";

export const FIELD_NAMES = {
  ANCHOR: "anchor",
  UPLOAD: "upload",
  CONTENT: "content",
  MT: "mt",
  MB: "mb",
};

export default function FormBlocImageBanner({ addBloc, bloc }) {
  const upload = getBlockFieldValue(FIELD_NAMES.UPLOAD, bloc, null);
  const content = getBlockFieldValue(FIELD_NAMES.CONTENT, bloc, "");
  const mt = getBlockFieldValue(FIELD_NAMES.MT, bloc, 20);
  const mb = getBlockFieldValue(FIELD_NAMES.MB, bloc, 20);
  const anchor = getBlockFieldValue(FIELD_NAMES.ANCHOR, bloc, "");

  const defaultValue = { ...bloc };

  const submit = (e) => {
    e.preventDefault();
    defaultValue.parameters = [
      {
        type: FIELD_NAMES.CONTENT,
        value: e.target.content.value,
      },
      {
        type: FIELD_NAMES.UPLOAD,
        value: e.target?.upload?.value,
      },
      {
        type: FIELD_NAMES.MT,
        value: e.target.mt.value,
      },
      {
        type: FIELD_NAMES.MB,
        value: e.target.mb.value,
      },
      {
        type: "anchor",
        value: e.target.anchor.value,
      },
    ];
    addBloc(defaultValue);
  };
  return (
    <Stack component="form" gap={2} py={2} onSubmit={submit}>
      <InputMedias label="Média" defaultValue={upload} />
      <InputWysiwyg label="Contenu" name="content" defaultValue={content} />
      <InputMargin defaultValue={{ mt, mb }} />
      <InputId defaultValue={anchor} />
      <Button type="submit">Valider</Button>
    </Stack>
  );
}

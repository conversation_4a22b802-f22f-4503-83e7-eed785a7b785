"use client";

import { <PERSON><PERSON>, <PERSON><PERSON> } from "@mui/material";
import InputMargin from "@/components/inputs/InputMargin";
import { BLOC_LABEL } from "@/enums/BLOC_LABEL";
import { getBlockFieldValue } from "@/utils/builder.utils";
import Input from "../../inputs/Input";
import InputCardSummary from "../../inputs/InputCardSummary";
import { useState } from "react";
import InputId from "../../inputs/InputId";

const FIELD_NAMES = {
  TITLE: "title",
  CARDS: "cards",
  MT: "mt",
  MB: "mb",
};

export default function FormBlockCardSummary({ bloc, addBloc }) {
  const title = getBlockFieldValue(FIELD_NAMES.TITLE, bloc, null);
  const mt = getBlockFieldValue(FIELD_NAMES.MT, bloc, 20);
  const mb = getBlockFieldValue(FIELD_NAMES.MB, bloc, 20);
  const anchor = getBlockFieldValue("anchor", bloc, "");
  const [cards, setCards] = useState(getBlockFieldValue(FIELD_NAMES.CARDS, bloc) || []);
  const defaultValue = { ...bloc };

  const submit = (e) => {
    e.preventDefault();
    defaultValue.parameters = [
      {
        type: FIELD_NAMES.TITLE,
        value: e.target.title.value,
      },
      {
        type: FIELD_NAMES.CARDS,
        value: cards,
      },
      {
        type: FIELD_NAMES.MT,
        value: e.target.mt.value,
      },
      {
        type: FIELD_NAMES.MB,
        value: e.target.mb.value,
      },
      {
        type: "anchor",
        value: e.target.anchor.value,
      },
    ];
    addBloc(defaultValue);
  };
  return (
    <Stack component="form" gap={2} py={2} onSubmit={submit}>
      <Input label="Titre" name="title" defaultValue={title} fullWidth />
      <InputCardSummary label="Cartes" defaultValue={cards} onChange={setCards} />
      <InputMargin defaultValue={{ mt, mb }} />
      <InputId defaultValue={anchor} />
      <Button type="submit">Valider</Button>
    </Stack>
  );
}

import InputMargin from "@/components/inputs/InputMargin";
import InputMedias from "@/components/inputs/InputMedias";
import InputWysiwyg from "@/components/inputs/wysiwyg/InputWysiwyg";
import { getBlockFieldValue } from "@/utils/builder.utils";
import { <PERSON><PERSON>, FormControlLabel, MenuItem, Stack, Switch } from "@mui/material";
import Input from "../../inputs/Input";
import InputId from "../../inputs/InputId";

export const FIELD_NAMES = {
  TEXT: "formText",
  LINK_DIAGNOSTIC: "linkDiagnostic",
  LINK_RDV: "linkRdv",
  DEFAULT_STEP: "defaultStep",
  MT: "mt",
  MB: "mb",
};

export default function FormBlockQuoteSingle({ bloc, addBloc }) {
  const mt = getBlockFieldValue(FIELD_NAMES.MT, bloc, 20);
  const mb = getBlockFieldValue(FIELD_NAMES.MB, bloc, 20);
  const linkDiagnostic = getBlockFieldValue(FIELD_NAMES.LINK_DIAGNOSTIC, bloc, "");
  const linkRdv = getBlockFieldValue(FIELD_NAMES.LINK_RDV, bloc, "");
  const anchor = getBlockFieldValue("anchor", bloc, "");
  const defaultStep = getBlockFieldValue(FIELD_NAMES.DEFAULT_STEP, bloc, false);

  const defaultValue = { ...bloc };

  const handleSubmit = (e) => {
    e.preventDefault();
    defaultValue.parameters = [
      {
        type: FIELD_NAMES.TEXT,
        value: e.target[FIELD_NAMES.TEXT].value,
      },
      {
        type: FIELD_NAMES.LINK_DIAGNOSTIC,
        value: e.target[FIELD_NAMES.LINK_DIAGNOSTIC].value,
      },
      {
        type: FIELD_NAMES.LINK_RDV,
        value: e.target[FIELD_NAMES.LINK_RDV].value,
      },
      {
        type: FIELD_NAMES.MT,
        value: e.target[FIELD_NAMES.MT].value,
      },
      {
        type: FIELD_NAMES.MB,
        value: e.target[FIELD_NAMES.MB].value,
      },
      {
        type: FIELD_NAMES.DEFAULT_STEP,
        value: e.target[FIELD_NAMES.DEFAULT_STEP].checked,
      },
      {
        type: "anchor",
        value: e.target.anchor.value,
      },
    ];
    addBloc(defaultValue);
  };

  return (
    <form onSubmit={handleSubmit}>
      <Stack spacing={2}>
        <InputWysiwyg name={FIELD_NAMES.TEXT} label="Titre du formulaire" defaultValue={getBlockFieldValue(FIELD_NAMES.TEXT, bloc) || ""} />
        <Input name={FIELD_NAMES.LINK_DIAGNOSTIC} label="Lien trouver un diagnostiqueur" defaultValue={linkDiagnostic} fullWidth />
        <Input name={FIELD_NAMES.LINK_RDV} label="Lien prendre un rendez-vous" defaultValue={linkRdv} fullWidth />
        <FormControlLabel control={<Switch defaultChecked={defaultStep} />} label={"Passer l'étape 1"} name={FIELD_NAMES.DEFAULT_STEP} />
        <InputMargin defaultValue={{ mt, mb }} />
        <InputId defaultValue={anchor} />
        <Button type="submit">Valider</Button>
      </Stack>
    </form>
  );
}

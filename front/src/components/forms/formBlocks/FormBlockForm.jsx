import { <PERSON><PERSON>, <PERSON>ack } from "@mui/material";
import InputMargin from "@/components/inputs/InputMargin";
import { getBlockFieldValue } from "@/utils/builder.utils";
import InputId from "../../inputs/InputId";
import Input from "@/components/inputs/Input";

const FIELD_NAMES = {
  MT: "mt",
  MB: "mb",
  TITLE: "title",
};

export default function FormBlockForm({ bloc, addBloc }) {
  const mt = getBlockFieldValue(FIELD_NAMES.MT, bloc, 20);
  const mb = getBlockFieldValue(FIELD_NAMES.MB, bloc, 20);
  const title = getBlockFieldValue(FIELD_NAMES.TITLE, bloc, null);
  const anchor = getBlockFieldValue("anchor", bloc, "");

  const defaultValue = { ...bloc };

  const submit = (e) => {
    e.preventDefault();
    defaultValue.parameters = [
      {
        type: FIELD_NAMES.TITLE,
        value: e.target.title.value,
      },
      {
        type: FIELD_NAMES.MT,
        value: e.target.mt.value,
      },
      {
        type: FIELD_NAMES.MB,
        value: e.target.mb.value,
      },
      {
        type: "anchor",
        value: e.target.anchor.value,
      },
    ];
    addBloc(defaultValue);
  };
  return (
    <Stack component="form" gap={2} py={2} onSubmit={submit}>
      <Input name="title" label="Titre" defaultValue={title} fullWidth />
      <InputMargin defaultValue={{ mt, mb }} />
      <InputId defaultValue={anchor} />
      <Button type="submit">Valider</Button>
    </Stack>
  );
}

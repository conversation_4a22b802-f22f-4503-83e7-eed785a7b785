"use client";

import { <PERSON><PERSON>, <PERSON><PERSON> } from "@mui/material";
import InputMargin from "@/components/inputs/InputMargin";
import { getBlockFieldValue } from "@/utils/builder.utils";
import InputId from "../../inputs/InputId";
import Input from "../../inputs/Input";

const FIELD_NAMES = {
  JS: "js",
  HTML: "html",
  MT: "mt",
  MB: "mb",
  ANCHOR: "anchor",
};

export default function FormBlockReviews({ bloc, addBloc }) {
  const js = getBlockFieldValue(FIELD_NAMES.JS, bloc, "");
  const html = getBlockFieldValue(FIELD_NAMES.HTML, bloc, "");
  const mt = getBlockFieldValue(FIELD_NAMES.MT, bloc, 20);
  const mb = getBlockFieldValue(FIELD_NAMES.MB, bloc, 20);
  const anchor = getBlockFieldValue(FIELD_NAMES.ANCHOR, bloc, "");

  const defaultValue = { ...bloc };

  const submit = (e) => {
    e.preventDefault();
    defaultValue.parameters = [
      {
        type: FIELD_NAMES.JS,
        value: e.target.js.value,
      },
      {
        type: FIELD_NAMES.HTML,
        value: e.target.html.value,
      },
      {
        type: FIELD_NAMES.MT,
        value: e.target.mt.value,
      },
      {
        type: FIELD_NAMES.MB,
        value: e.target.mb.value,
      },
      {
        type: FIELD_NAMES.ANCHOR,
        value: e.target.anchor.value,
      },
    ];
    addBloc(defaultValue);
  };
  return (
    <Stack component="form" gap={2} py={2} onSubmit={submit}>
      <Input multiline minRows={4} type="text" name="html" label="Balise HTML" defaultValue={html} fullWidth />
      <Input multiline minRows={4} type="text" name="js" label="Balise JS" defaultValue={js} fullWidth />
      <InputMargin defaultValue={{ mt, mb }} />
      <InputId defaultValue={anchor} />
      <Button type="submit">Valider</Button>
    </Stack>
  );
}

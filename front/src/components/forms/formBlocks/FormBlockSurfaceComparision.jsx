"use client";

import { <PERSON><PERSON>, <PERSON>ack } from "@mui/material";
import { useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { getBlockFieldValue } from "@/utils/builder.utils";
import Input from "@/components/inputs/Input";
import { BLOC_TYPES } from "@/enums/BLOC_TYPES";
import InputRowSurface from "@/components/inputs/InputRowSurface";
import InputMargin from "../../inputs/InputMargin";
import TranslationUtils from "@/utils/translation.utils";
import InputId from "../../inputs/InputId";

export const FIELD_NAMES = {
  TITLE: "title",
  ROWS: "rows",
  MT: "mt",
  MB: "mb",
};

export default function FormBlockSurfaceComparision({ bloc, addBloc }) {
  const [rows, setRows] = useState(getBlockFieldValue(FIELD_NAMES.ROWS, bloc) || []);
  const mt = getBlockFieldValue(FIELD_NAMES.MT, bloc, 20);
  const mb = getBlockFieldValue(FIELD_NAMES.MB, bloc, 20);
  const anchor = getBlockFieldValue("anchor", bloc, "");

  const handleSubmit = (e) => {
    e.preventDefault();

    const uuid = bloc.uuid ?? uuidv4();
    const b = bloc || {
      uuid,
      type: BLOC_TYPES.BLOCK_SURFACE_COMPARISION,
      name: "Tableau de comparaison de surfaces",
    };

    addBloc({
      ...b,
      parameters: [
        {
          type: FIELD_NAMES.TITLE,
          value: e.target[FIELD_NAMES.TITLE].value,
        },
        {
          type: FIELD_NAMES.ROWS,
          value: rows || [],
        },
        {
          type: FIELD_NAMES.MT,
          value: e.target[FIELD_NAMES.MT].value,
        },
        {
          type: FIELD_NAMES.MB,
          value: e.target[FIELD_NAMES.MB].value,
        },
        {
          type: "anchor",
          value: e.target.anchor.value,
        },
      ],
    });
  };

  return (
    <form onSubmit={handleSubmit}>
      <Stack gap={2}>
        <Input label={TranslationUtils.get("blocks.blockSurfaceComparision.form.title")} name={FIELD_NAMES.TITLE} defaultValue={getBlockFieldValue(FIELD_NAMES.TITLE, bloc)} fullWidth />
        <InputRowSurface label={TranslationUtils.get("blocks.blockSurfaceComparision.form.rows")} defaultValue={getBlockFieldValue(FIELD_NAMES.ROWS, bloc) || []} onChange={(val) => setRows(val)} />
        <InputMargin defaultValue={{ mt, mb }} />
        <InputId defaultValue={anchor} />
        <Button type="submit">{TranslationUtils.get("global.validate")}</Button>
      </Stack>
    </form>
  );
}

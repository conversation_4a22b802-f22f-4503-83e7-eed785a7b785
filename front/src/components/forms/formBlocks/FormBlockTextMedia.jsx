"use client";

import { <PERSON><PERSON>, <PERSON><PERSON> } from "@mui/material";
import InputWysiwyg from "@/components/inputs/wysiwyg/InputWysiwyg";
import InputMedias from "@/components/inputs/InputMedias";
import InputMargin from "@/components/inputs/InputMargin";
import { getBlockFieldValue } from "@/utils/builder.utils";
import InputAlignment from "../../inputs/InputAlignment";
import InputMediaChoice from "../../inputs/InputMediaChoice";
import InputId from "../../inputs/InputId";
import InputButton from "@/components/inputs/InputButton";
import { useState } from "react";

const FIELD_NAMES = {
  UPLOAD: "upload",
  CONTENT: "content",
  BACKGROUND: "background",
  BUTTON: "button",
  ALIGNMENT: "alignment",
  MT: "mt",
  MB: "mb",
  VIDEO: "video",
};

export default function FormBlockTextMedia({ bloc, addBloc }) {
  const upload = getBlockFieldValue(FIELD_NAMES.UPLOAD, bloc, null);
  const video = getBlockFieldValue(FIELD_NAMES.VIDEO, bloc, null);
  const background = getBlockFieldValue(FIELD_NAMES.BACKGROUND, bloc, null);
  const content = getBlockFieldValue(FIELD_NAMES.CONTENT, bloc, "");
  const alignment = getBlockFieldValue(FIELD_NAMES.ALIGNMENT, bloc, "left");
  const mt = getBlockFieldValue(FIELD_NAMES.MT, bloc, 20);
  const mb = getBlockFieldValue(FIELD_NAMES.MB, bloc, 20);
  const anchor = getBlockFieldValue("anchor", bloc, "");
  const [button, setButton] = useState(getBlockFieldValue(FIELD_NAMES.BUTTON, bloc) || []);

  const defaultValue = { ...bloc };

  const submit = (e) => {
    e.preventDefault();
    defaultValue.parameters = [
      {
        type: FIELD_NAMES.CONTENT,
        value: e.target.content.value,
      },
      {
        type: FIELD_NAMES.UPLOAD,
        value: e.target?.media?.value || null,
      },
      {
        type: FIELD_NAMES.BACKGROUND,
        value: e.target?.background?.value,
      },
      {
        type: FIELD_NAMES.ALIGNMENT,
        value: e.target?.alignment?.value,
      },
      {
        type: FIELD_NAMES.MT,
        value: e.target.mt.value,
      },
      {
        type: FIELD_NAMES.MB,
        value: e.target.mb.value,
      },
      {
        type: FIELD_NAMES.VIDEO,
        value: e.target.video?.value || null,
      },
      {
        type: "anchor",
        value: e.target.anchor.value,
      },
      {
        type: FIELD_NAMES.BUTTON,
        value: button || [],
      },
    ];
    addBloc(defaultValue);
  };
  return (
    <Stack component="form" gap={2} py={2} onSubmit={submit}>
      <InputMediaChoice label="Media" nameImage="media" nameVideo="video" defaultValue={upload || video} />
      <InputWysiwyg label="Contenu" name="content" defaultValue={content} />
      <InputButton defaultValue={button} onChange={(val) => setButton(val)} />
      <InputMedias label="Image de fond" name="background" defaultValue={background} />
      <InputAlignment defaultValue={alignment} />
      <InputMargin defaultValue={{ mt, mb }} />
      <InputId defaultValue={anchor} />
      <Button type="submit">Valider</Button>
    </Stack>
  );
}

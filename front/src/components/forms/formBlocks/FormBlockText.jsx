"use client";

import { <PERSON><PERSON>, <PERSON><PERSON> } from "@mui/material";
import InputWysiwyg from "@/components/inputs/wysiwyg/InputWysiwyg";
import InputMargin from "@/components/inputs/InputMargin";
import { getBlockFieldValue } from "@/utils/builder.utils";
import InputId from "../../inputs/InputId";
import InputColor from "@/components/inputs/InputColor";
import { useState } from "react";

export const FIELD_NAMES = {
  CONTENT: "content",
  MT: "mt",
  MB: "mb",
  BACKGROUND_COLOR: "backgroundColor",
};

export default function FormBlocText({ bloc, addBloc }) {
  const content = getBlockFieldValue(FIELD_NAMES.CONTENT, bloc, "");
  const mt = getBlockFieldValue(FIELD_NAMES.MT, bloc, 20);
  const mb = getBlockFieldValue(FIELD_NAMES.MB, bloc, 20);
  const anchor = getBlockFieldValue("anchor", bloc, "");
  const backgroundColor = getBlockFieldValue(FIELD_NAMES.BACKGROUND_COLOR, bloc, "");

  const [color, setColor] = useState(backgroundColor);

  const defaultValue = { ...bloc };

  const submit = (e) => {
    e.preventDefault();
    defaultValue.parameters = [
      {
        type: FIELD_NAMES.CONTENT,
        value: e.target[FIELD_NAMES.CONTENT].value,
      },
      {
        type: FIELD_NAMES.MT,
        value: e.target[FIELD_NAMES.MT].value,
      },
      {
        type: FIELD_NAMES.BACKGROUND_COLOR,
        value: color,
      },
      {
        type: FIELD_NAMES.MB,
        value: e.target[FIELD_NAMES.MB].value,
      },
      {
        type: "anchor",
        value: e.target.anchor.value,
      },
    ];
    addBloc(defaultValue);
  };

  return (
    <Stack component="form" gap={2} py={2} onSubmit={submit}>
      <InputColor label="Couleur de fond" name={FIELD_NAMES.BACKGROUND_COLOR} defaultValue={backgroundColor} onChange={setColor} />
      <InputWysiwyg label="Contenu" name={FIELD_NAMES.CONTENT} defaultValue={content} />
      <InputMargin defaultValue={{ mt, mb }} />
      <InputId defaultValue={anchor} />
      <Button type="submit">Valider</Button>
    </Stack>
  );
}

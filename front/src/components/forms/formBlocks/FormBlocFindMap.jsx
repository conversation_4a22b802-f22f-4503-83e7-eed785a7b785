import { <PERSON><PERSON>, <PERSON>ack } from "@mui/material";
import Input from "@/components/inputs/Input";
import InputMargin from "../../inputs/InputMargin";
import { getBlockFieldValue } from "@/utils/builder.utils";
import InputId from "../../inputs/InputId";

const FIELD_NAMES = {
  LABEL: "label",
  LINK: "link",
  MT: "mt",
  MB: "mb",
};

export default function FormBlocFindMap({ bloc, addBloc }) {
  const label = getBlockFieldValue(FIELD_NAMES.LABEL, bloc, null);
  const link = getBlockFieldValue(FIELD_NAMES.LINK, bloc, "");
  const mt = getBlockFieldValue(FIELD_NAMES.MT, bloc, 20);
  const mb = getBlockFieldValue(FIELD_NAMES.MB, bloc, 20);
  const anchor = getBlockFieldValue("anchor", bloc, "");

  const defaultValue = { ...bloc };

  const submit = (e) => {
    e.preventDefault();
    defaultValue.parameters = [
      {
        type: "label",
        value: e.target.label.value,
      },
      {
        type: "link",
        value: e.target.link.value,
      },
      {
        type: "mt",
        value: e.target.mt.value,
      },
      {
        type: "mb",
        value: e.target.mb.value,
      },
      {
        type: "anchor",
        value: e.target.anchor.value,
      },
    ];
    addBloc(defaultValue);
  };
  return (
    <Stack component="form" gap={2} py={2} onSubmit={submit}>
      <Input label="Titre" name="label" defaultValue={label} fullWidth />
      <Input label="Lien" name="link" defaultValue={link} fullWidth />
      <InputMargin defaultValue={{ mt, mb }} />
      <InputId defaultValue={anchor} />
      <Button type="submit">Valider</Button>
    </Stack>
  );
}

"use client";
import { BLOC_TYPES } from "@/enums/BLOC_TYPES";
import { getBlockFieldValue } from "@/utils/builder.utils";
import { <PERSON><PERSON>, <PERSON>ack } from "@mui/material";
import { v4 as uuidv4 } from "uuid";
import InputWysiwyg from "@/components/inputs/wysiwyg/InputWysiwyg";
import InputMargin from "../../inputs/InputMargin";
import Input from "../../inputs/Input";
import InputId from "../../inputs/InputId";

const FIELD_NAMES = {
  TITLE: "title",
  TEXT: "text",
  LINK: "link",
  LINK_NEWSLETTER: "link_newsletter",
  MT: "mt",
  MB: "mb",
};

export default function FormBlockNewsPreview({ bloc, addBloc }) {
  const mt = getBlockFieldValue("mt", bloc, 20);
  const mb = getBlockFieldValue("mb", bloc, 20);
  const anchor = getBlockFieldValue("anchor", bloc, "");

  const handleSubmit = (e) => {
    e.preventDefault();

    const uuid = bloc.uuid ?? uuidv4();
    const b = bloc || {
      uuid,
      type: BLOC_TYPES.BLOCK_NEWS_PREVIEW,
      name: "News preview",
    };

    addBloc({
      ...b,
      parameters: [
        {
          type: FIELD_NAMES.LINK,
          value: e.target[FIELD_NAMES.LINK].value,
        },
        {
          type: FIELD_NAMES.TITLE,
          value: e.target[FIELD_NAMES.TITLE].value,
        },
        {
          type: FIELD_NAMES.TEXT,
          value: e.target[FIELD_NAMES.TEXT].value,
        },
        {
          type: FIELD_NAMES.LINK_NEWSLETTER,
          value: e.target[FIELD_NAMES.LINK_NEWSLETTER].value,
        },
        {
          type: FIELD_NAMES.MT,
          value: mt,
        },
        {
          type: FIELD_NAMES.MB,
          value: mb,
        },
        {
          type: "anchor",
          value: e.target.anchor.value,
        },
      ],
    });
  };

  return (
    <Stack component="form" gap={2} onSubmit={handleSubmit}>
      <Input fullWidth label="Lien vers la page" name={FIELD_NAMES.LINK} defaultValue={getBlockFieldValue(FIELD_NAMES.LINK, bloc)} />
      <InputWysiwyg label="Titre" name={FIELD_NAMES.TITLE} defaultValue={getBlockFieldValue(FIELD_NAMES.TITLE, bloc)} />
      <InputWysiwyg label="Form newsletter text" name={FIELD_NAMES.TEXT} defaultValue={getBlockFieldValue(FIELD_NAMES.TEXT, bloc)} />
      <Input
        fullWidth
        label="Lien newsletter"
        name={FIELD_NAMES.LINK_NEWSLETTER}
        defaultValue={getBlockFieldValue(FIELD_NAMES.LINK_NEWSLETTER, bloc)}
      />
      <InputMargin defaultValue={{ mt, mb }} />
      <InputId defaultValue={anchor} />
      <Button type="submit">Valider</Button>
    </Stack>
  );
}

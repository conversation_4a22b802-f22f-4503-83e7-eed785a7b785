import { <PERSON><PERSON>, <PERSON><PERSON>, TextField } from "@mui/material";
import InputColor from "@/components/inputs/InputColor";
import InputWysiwyg from "@/components/inputs/wysiwyg/InputWysiwyg";
import InputMargin from "@/components/inputs/InputMargin";
import { getBlockFieldValue } from "@/utils/builder.utils";
import InputId from "../../inputs/InputId";
import InputSkeleton from "@/components/inputs/InputSkeleton";

export const FIELD_NAMES = {
  ANCHOR: "anchor",
  COLOR: "color",
  CONTENT: "content",
  MT: "mt",
  MB: "mb",
  HEIGHT: "height",
};

export default function FormBlocBanner({ bloc, addBloc }) {
  const color = getBlockFieldValue(FIELD_NAMES.COLOR, bloc, null);
  const content = getBlockFieldValue(FIELD_NAMES.CONTENT, bloc, "");
  const mt = getBlockFieldValue(FIELD_NAMES.MT, bloc, 20);
  const mb = getBlockFieldValue(FIELD_NAMES.MB, bloc, 20);
  const anchor = getBlockFieldValue(FIELD_NAMES.ANCHOR, bloc, "");
  const height = getBlockFieldValue(FIELD_NAMES.HEIGHT, bloc, 230);

  const defaultValue = { ...bloc };

  const submit = (e) => {
    e.preventDefault();
    defaultValue.parameters = [
      {
        type: FIELD_NAMES.COLOR,
        value: e.target.color.value,
      },
      {
        type: FIELD_NAMES.CONTENT,
        value: e.target.content.value,
      },
      {
        type: FIELD_NAMES.MT,
        value: e.target.mt.value,
      },
      {
        type: FIELD_NAMES.MB,
        value: e.target.mb.value,
      },
      {
        type: "anchor",
        value: e.target.anchor.value,
      },
      {
        type: FIELD_NAMES.HEIGHT,
        value: e.target.height.value,
      }
    ];
    addBloc(defaultValue);
  };
  return (
    <Stack component="form" gap={2} py={2} onSubmit={submit}>
      <InputColor label="Couleur" name="color" defaultValue={color} />
      <InputWysiwyg label="Contenu" name="content" defaultValue={content} />
      <InputMargin defaultValue={{ mt, mb }} />
      <InputSkeleton id={FIELD_NAMES.HEIGHT} label="Hauteur du bloc" required={false} fullWidth={true}>
        <TextField id={FIELD_NAMES.HEIGHT} required={false} type="number" name={FIELD_NAMES.HEIGHT} defaultValue={height} />
      </InputSkeleton>
      <InputId defaultValue={anchor} />
      <Button type="submit">Valider</Button>
    </Stack>
  );
}

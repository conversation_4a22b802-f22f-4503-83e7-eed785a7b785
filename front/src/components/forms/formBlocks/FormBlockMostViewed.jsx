"use client";
import { BLOC_TYPES } from "@/enums/BLOC_TYPES";
import { getBlockFieldValue } from "@/utils/builder.utils";
import { <PERSON><PERSON>, Stack } from "@mui/material";
import { useState } from "react";
import { v4 as uuidv4 } from "uuid";
import InputCardMostViewed from "@/components/inputs/InputCardMostViewed";
import Input from "../../inputs/Input";
import InputMargin from "../../inputs/InputMargin";
import InputId from "../../inputs/InputId";

const FIELD_NAMES = {
  TITLE: "title",
  ITEMS: "items",
  MT: "mt",
  MB: "mb",
};

export default function FormBlockMostViewed({ bloc, addBloc }) {
  const [items, setItems] = useState(getBlockFieldValue(FIELD_NAMES.ITEMS, bloc) || []);
  const mt = getBlockFieldValue(FIELD_NAMES.MT, bloc, 20);
  const mb = getBlockFieldValue(FIELD_NAMES.MB, bloc, 20);
  const anchor = getBlockFieldValue("anchor", bloc, "");

  const handleSubmit = (e) => {
    e.preventDefault();

    const uuid = bloc.uuid ?? uuidv4();
    const b = bloc || {
      uuid,
      type: BLOC_TYPES.BLOCK_CATEGORIES,
      name: "Tableau de fichiers",
    };

    addBloc({
      ...b,
      parameters: [
        {
          type: FIELD_NAMES.TITLE,
          value: e.target[FIELD_NAMES.TITLE].value,
        },
        {
          type: FIELD_NAMES.ITEMS,
          value: items || [],
        },
        {
          type: FIELD_NAMES.MT,
          value: mt,
        },
        {
          type: FIELD_NAMES.MB,
          value: mb,
        },
        {
          type: "anchor",
          value: e.target.anchor.value,
        },
      ],
    });
  };

  return (
    <Stack gap={2} component="form" onSubmit={handleSubmit}>
      <Input label="Titre" name={FIELD_NAMES.TITLE} defaultValue={getBlockFieldValue(FIELD_NAMES.TITLE, bloc)} fullWidth />
      <InputCardMostViewed
        label="Sites les plus vus"
        defaultValue={getBlockFieldValue(FIELD_NAMES.ITEMS, bloc) || []}
        onChange={(val) => setItems(val)}
      />
      <InputMargin defaultValue={{ mt, mb }} />
      <InputId defaultValue={anchor} />
      <Button type="submit">Valider</Button>
    </Stack>
  );
}

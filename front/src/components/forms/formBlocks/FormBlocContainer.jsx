import { IconButton, Rating, Slide, Stack, Typography } from "@mui/material";
import { createElement, useState } from "react";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import { BLOC_FORMS } from "@/enums/BLOC_FORMS";
import { BLOC_LABEL } from "@/enums/BLOC_LABEL";
import ModalFavorite from "../../superadmin/ModalFavorite";

export default function FormBlocContainer({ bloc, onClose, addBloc }) {
  const form = BLOC_FORMS[bloc.type];
  const label = BLOC_LABEL[bloc.type];
  const [favorite, setFavorite] = useState(null);

  const handleFavorite = (value, bloc) => {
    if (value) {
      setFavorite(bloc);
    } else {
      const newBloc = { ...bloc, name: null };
      setFavorite(null);
      addBloc(newBloc);
    }
  };

  return (
    <Slide in direction="left">
      <Stack p={2} height="100%">
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Stack direction="row" alignItems="center" gap={1}>
            <IconButton onClick={onClose}>
              <ChevronLeftIcon />
            </IconButton>
            <Typography variant="h3">{label}</Typography>
          </Stack>
          <Rating max={1} value={!!bloc.name} name="favorite" onChange={(_, value) => handleFavorite(!!value, bloc)} />
          <ModalFavorite bloc={favorite} onClose={() => setFavorite(null)} addBloc={addBloc} onSuccess={() => setFavorite(null)} />
        </Stack>
        <Stack py={1}>{createElement(form, { addBloc, bloc })}</Stack>
      </Stack>
    </Slide>
  );
}

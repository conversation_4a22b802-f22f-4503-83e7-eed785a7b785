import { Button, Stack } from "@mui/material";
import InputMargin from "@/components/inputs/InputMargin";
import { getBlockFieldValue } from "@/utils/builder.utils";
import InputId from "../../inputs/InputId";

const FIELD_NAMES = {
  MT: "mt",
  MB: "mb",
};

export default function FormEmpty({ bloc, addBloc }) {
  const mt = getBlockFieldValue(FIELD_NAMES.MT, bloc, 20);
  const mb = getBlockFieldValue(FIELD_NAMES.MB, bloc, 20);
  const anchor = getBlockFieldValue("anchor", bloc, "");

  const defaultValue = { ...bloc };

  const submit = (e) => {
    e.preventDefault();
    defaultValue.parameters = [
      {
        type: FIELD_NAMES.MT,
        value: e.target.mt.value,
      },
      {
        type: FIELD_NAMES.MB,
        value: e.target.mb.value,
      },
      {
        type: "anchor",
        value: e.target.anchor.value,
      }
    ];
    addBloc(defaultValue);
  };
  return (
    <Stack component="form" gap={2} py={2} onSubmit={submit}>
      <InputMargin defaultValue={{ mt, mb }} />
      <InputId defaultValue={anchor} />
      <Button type="submit">Valider</Button>
    </Stack>
  );
}

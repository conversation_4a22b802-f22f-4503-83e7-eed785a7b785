"use client";

import { <PERSON><PERSON>, Divider, Stack, styled, Typography } from "@mui/material";
import { useState } from "react";
import { getBlockFieldValue } from "@/utils/builder.utils";
import InputWysiwyg from "@/components/inputs/wysiwyg/InputWysiwyg";
import InputMargin from "@/components/inputs/InputMargin";
import { BLOC_LABEL } from "@/enums/BLOC_LABEL";
import InputButton from "../../inputs/InputButton";
import InputId from "../../inputs/InputId";

export const FIELD_NAMES = {
  TEXT_FIRST: "textFirst",
  TEXT_SECOND: "textSecond",
  BUTTON_FIRST: "buttonFirst",
  BUTTON_SECOND: "buttonSecond",
  MT: "mt",
  MB: "mb",
};

const StyledTextContainer = styled(Stack)(({ theme }) => ({
  border: "1px solid " + theme.palette.grey.main,
  padding: theme.spacing(2),
  borderColor: theme.palette.grey.main,
  borderRadius: theme.shape.borderRadius,
}));

export default function FormBlocDoubleText({ bloc, addBloc }) {
  const [buttonFirst, setButtonFirst] = useState(getBlockFieldValue(FIELD_NAMES.BUTTON_FIRST, bloc) || []);
  const [buttonSecond, setButtonSecond] = useState(getBlockFieldValue(FIELD_NAMES.BUTTON_SECOND, bloc) || []);
  const mt = getBlockFieldValue(FIELD_NAMES.MT, bloc, 20);
  const mb = getBlockFieldValue(FIELD_NAMES.MB, bloc, 20);
  const anchor = getBlockFieldValue("anchor", bloc, "");

  const defaultValue = { ...bloc };

  const handleSubmit = (e) => {
    e.preventDefault();
    defaultValue.parameters = [
      {
        type: FIELD_NAMES.TEXT_FIRST,
        value: e.target[FIELD_NAMES.TEXT_FIRST].value,
      },
      {
        type: FIELD_NAMES.TEXT_SECOND,
        value: e.target[FIELD_NAMES.TEXT_SECOND].value,
      },
      {
        type: FIELD_NAMES.BUTTON_FIRST,
        value: buttonFirst || [],
      },
      {
        type: FIELD_NAMES.BUTTON_SECOND,
        value: buttonSecond || [],
      },
      {
        type: FIELD_NAMES.MT,
        value: e.target.mt.value,
      },
      {
        type: FIELD_NAMES.MB,
        value: e.target.mb.value,
      },
      {
        type: "anchor",
        value: e.target.anchor.value,
      },
    ];
    addBloc(defaultValue);
  };

  return (
    <Stack gap={2} onSubmit={handleSubmit} component="form">
      <Stack>
        <Typography variant="h6" color="grey">
          Colonne 1
        </Typography>
        <StyledTextContainer gap={2}>
          <InputWysiwyg name={FIELD_NAMES.TEXT_FIRST} label="Texte" defaultValue={getBlockFieldValue(FIELD_NAMES.TEXT_FIRST, bloc, "")} />
          <InputButton defaultValue={buttonFirst} onChange={(val) => setButtonFirst(val)} />
        </StyledTextContainer>
      </Stack>
      <Divider />
      <Stack>
        <Typography variant="h6" color="grey">
          Colonne 2
        </Typography>
        <StyledTextContainer gap={2}>
          <InputWysiwyg name={FIELD_NAMES.TEXT_SECOND} label="Texte" defaultValue={getBlockFieldValue(FIELD_NAMES.TEXT_SECOND, bloc, "")} />
          <InputButton defaultValue={buttonSecond} onChange={(val) => setButtonSecond(val)} />
        </StyledTextContainer>
      </Stack>
      <InputMargin defaultValue={{ mt, mb }} />
      <InputId defaultValue={anchor} />
      <Button type="submit">Valider</Button>
    </Stack>
  );
}

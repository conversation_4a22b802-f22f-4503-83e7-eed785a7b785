"use client";

import { <PERSON><PERSON>, <PERSON><PERSON> } from "@mui/material";
import { useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { getBlockFieldValue } from "@/utils/builder.utils";
import Input from "@/components/inputs/Input";
import { BLOC_TYPES } from "@/enums/BLOC_TYPES";
import InputCardTestimonials from "@/components/inputs/InputCardTestimonials";
import TranslationUtils from "@/utils/translation.utils";
import InputMargin from "../../inputs/InputMargin";
import InputId from "../../inputs/InputId";

export const FIELD_NAMES = {
  TITLE: "title",
  CARDS: "cards",
  MT: "mt",
  MB: "mb",
};

export default function FormBlockTestimonials({ bloc, addBloc }) {
  const mt = getBlockFieldValue(FIELD_NAMES.MT, bloc, 20);
  const mb = getBlockFieldValue(FIELD_NAMES.MB, bloc, 20);
  const anchor = getBlockFieldValue("anchor", bloc, "");

  const [cards, setCards] = useState(getBlockFieldValue(FIELD_NAMES.CARDS, bloc) || []);
  const handleSubmit = (e) => {
    e.preventDefault();

    const uuid = bloc.uuid ?? uuidv4();
    const b = bloc || {
      uuid,
      type: BLOC_TYPES.BLOCK_CARDS,
      name: "Block témoignages",
    };

    addBloc({
      ...b,
      parameters: [
        {
          type: FIELD_NAMES.TITLE,
          value: e.target[FIELD_NAMES.TITLE].value,
        },
        {
          type: FIELD_NAMES.CARDS,
          value: cards || [],
        },
        {
          type: FIELD_NAMES.MT,
          value: mt,
        },
        {
          type: FIELD_NAMES.MB,
          value: mb,
        },
        {
          type: "anchor",
          value: e.target.anchor.value,
        },
      ],
    });
  };

  return (
    <Stack component="form" gap={2} onSubmit={handleSubmit}>
      <Input label="Titre" name={FIELD_NAMES.TITLE} defaultValue={getBlockFieldValue(FIELD_NAMES.TITLE, bloc)} fullWidth />
      <InputCardTestimonials label="Cartes" defaultValue={getBlockFieldValue(FIELD_NAMES.CARDS, bloc) || []} onChange={(val) => setCards(val)} />
      <InputMargin defaultValue={{ mt, mb }} />
      <InputId defaultValue={anchor} />
      <Button type="submit">{TranslationUtils.get("global.validate")}</Button>
    </Stack>
  );
}

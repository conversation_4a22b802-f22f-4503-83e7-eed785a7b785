"use client";

import { <PERSON><PERSON>, <PERSON><PERSON> } from "@mui/material";
import { useState } from "react";
import { v4 as uuidv4 } from "uuid";
import Input from "@/components/inputs/Input";
import InputCard from "@/components/inputs/InputCard";
import { BLOC_TYPES } from "@/enums/BLOC_TYPES";
import InputWysiwyg from "@/components/inputs/wysiwyg/InputWysiwyg";
import { getBlockFieldValue } from "@/utils/builder.utils";
import InputMargin from "@/components/inputs/InputMargin";
import TranslationUtils from "@/utils/translation.utils";
import InputId from "../../inputs/InputId";

export const FIELD_NAMES = {
  ANCHOR: "anchor",
  TITLE: "title",
  CARDS: "cards",
  TEXT: "text",
  MT: "mt",
  MB: "mb",
  COLUMNS: "columns",
};

export default function FormBlockCards({ bloc, addBloc }) {
  const title = getBlockFieldValue(FIELD_NAMES.TITLE, bloc, "");
  const text = getBlockFieldValue(FIELD_NAMES.TEXT, bloc, "");
  const [cards, setCards] = useState(getBlockFieldValue(FIELD_NAMES.CARDS, bloc) || []);
  const columns = getBlockFieldValue(FIELD_NAMES.COLUMNS, bloc, 2);
  const mt = getBlockFieldValue(FIELD_NAMES.MT, bloc, 20);
  const mb = getBlockFieldValue(FIELD_NAMES.MB, bloc, 20);
  const anchor = getBlockFieldValue(FIELD_NAMES.ANCHOR, bloc, "");

  const handleSubmit = (e) => {
    e.preventDefault();

    const uuid = bloc.uuid ?? uuidv4();
    const b = bloc || {
      uuid,
      type: BLOC_TYPES.BLOCK_CARDS,
      name: "Tableau de fichiers",
    };

    addBloc({
      ...b,
      parameters: [
        {
          type: FIELD_NAMES.TITLE,
          value: e.target[FIELD_NAMES.TITLE].value,
        },
        {
          type: FIELD_NAMES.CARDS,
          value: cards || [],
        },
        {
          type: FIELD_NAMES.TEXT,
          value: e.target[FIELD_NAMES.TEXT].value,
        },
        {
          type: FIELD_NAMES.MT,
          value: e.target.mt.value,
        },
        {
          type: FIELD_NAMES.MB,
          value: e.target.mb.value,
        },
        {
          type: FIELD_NAMES.COLUMNS,
          value: e.target.columns.value,
        },
        {
          type: "anchor",
          value: e.target.anchor.value,
        },
      ],
    });
  };

  return (
    <form onSubmit={handleSubmit}>
      <Stack spacing={2}>
        <Input label={TranslationUtils.get("blocks.blockCards.form.title")} name={FIELD_NAMES.TITLE} defaultValue={title} fullWidth />
        <InputCard label={TranslationUtils.get("blocks.blockCards.form.cards")} defaultValue={cards} onChange={(val) => setCards(val)} />
        <InputWysiwyg label={TranslationUtils.get("blocks.blockCards.form.text")} name={FIELD_NAMES.TEXT} defaultValue={text} />
        <InputMargin defaultValue={{ mt, mb }} />
        <Input type="number" label="Nombre de colonnes" name={FIELD_NAMES.COLUMNS} defaultValue={columns} />
        <InputId defaultValue={anchor} />
        <Button type="submit">{TranslationUtils.get("global.validate")}</Button>
      </Stack>
    </form>
  );
}

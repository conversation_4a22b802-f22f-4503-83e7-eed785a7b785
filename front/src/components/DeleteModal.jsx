import { Button, Dialog, Stack, Typography } from "@mui/material";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import { useRouter } from "next/navigation";
import TranslationUtils from "@/utils/translation.utils";

export default function DeleteModal({ item, onClose, onRefresh, nameKey = "name" }) {
  const { delete: deleteItem } = useApi();
  const { add } = useSnack();
  const router = useRouter();

  const handleDelete = async () => {
    try {
      await deleteItem(item["@id"].replace("/api", ""));
      add("success", "L'élément a bien été supprimé !");
      router.refresh();
      onClose();
      onRefresh ? onRefresh() : null;
    } catch (error) {
      add("error", TranslationUtils.get("global.error"));
    }
  };

  if (!item) {
    return null;
  }

  return (
    <Dialog open={!!item} onClose={onClose}>
      <Stack p={3}>
        <Typography mb={2} variant="h4">
          {item?.[nameKey]}
        </Typography>
        <Typography variant="body1" mb={2}>
          {TranslationUtils.get("global.deleteConfirmation")}
        </Typography>
        <Stack direction="row" gap={2} justifyContent="flex-end">
          <Button color="secondary" onClick={onClose}>
            {TranslationUtils.get("global.cancel")}
          </Button>
          <Button onClick={handleDelete}>{TranslationUtils.get("global.delete")}</Button>
        </Stack>
      </Stack>
    </Dialog>
  );
}

"use client";

import { Pagination as MuiPagination, Stack } from "@mui/material";
import FormatUtils from "@/utils/format.utils";
import { useRouter, useSearchParams } from "next/navigation";

export default function Pagination({ data, itemsPerPage = 30 }) {
  const router = useRouter();
  const searchParams = useSearchParams();
  if (!data?.member?.length || data?.totalItems <= itemsPerPage) return null;
  return (
    <Stack alignItems="center" py={1}>
      <MuiPagination
        showFirstButton
        showLastButton={data?.member?.length >= itemsPerPage}
        hideNextButton={data?.member?.length < itemsPerPage}
        count={Math.ceil(data.totalItems / itemsPerPage)}
        page={Number(searchParams.get("page")) || 1}
        onChange={(_, page) =>
          router.push(
            FormatUtils.formatUrl({
              ...searchParams,
              page,
            })
          )
        }
      />
    </Stack>
  );
}

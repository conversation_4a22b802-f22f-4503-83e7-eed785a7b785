"use client";

import { getBlockFieldValue } from "@/utils/builder.utils";
import LayoutContainer from "../LayoutContainer";
import { Button, Checkbox, FormControlLabel, MenuItem, Stack, Typography } from "@mui/material";
import Input from "../inputs/Input";
import TranslationUtils from "@/utils/translation.utils";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";

export default function BlockNewsletter({ block }) {
  const { post } = useApi();
  const { add } = useSnack();

  const anchor = getBlockFieldValue("anchor", block, "");
  const title = getBlockFieldValue("title", block, null);

  const civilities = ["MRS", "MISTER", "MISS", "DOCTOR", "PROFESSOR", "MASTER"];
  const activities = [
    "notaire",
    "agento_immobilier",
    "particulier_vendeur",
    "particulier_acheteur",
    "syndic_de_copropritete",
    "organisme_financier",
    "autre",
  ];

  const submit = async (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    const data = {
      ...Object.fromEntries(formData.entries()),
      agency: formData.get("agency") === "" ? null : formData.get("agency"),
    };
    try {
      await post("/mailer/newsletter", data);
      add("success", "Message envoyé avec succès !");
    } catch (error) {
      console.log(error);
      add("error", "Une erreur est survenue lors de l'envoi du message.");
    }
  };

  return (
    <LayoutContainer id={anchor || block?.uuid}>
      <Stack component="form" bgcolor="white" p={4} borderRadius={1} gap={2} onSubmit={submit}>
        {title ? (
          <Typography variant="h4" mb={3}>
            {title}
          </Typography>
        ) : null}
        <Stack display="grid" gridTemplateColumns="1fr 1fr" gap={2}>
          <Input name="civility" label="Civilité" select fullWidth>
            <MenuItem value={null}>
              <em>Non défini</em>
            </MenuItem>
            {civilities.map((civility) => (
              <MenuItem key={civility} value={civility}>
                {TranslationUtils.get(`civility.${civility}`)}
              </MenuItem>
            ))}
          </Input>
          <Input required name="lastname" label="Nom" fullWidth />
          <Input required name="firstname" label="Prénom" fullWidth />
          <Input name="address" label="Adresse" fullWidth />
          <Input required name="zip" label="Code postal" fullWidth />
          <Input name="city" label="Ville" fullWidth />
          <Input name="activity" label="Activité" select fullWidth>
            <MenuItem value={null}>
              <em>Non défini</em>
            </MenuItem>
            {activities.map((activity) => (
              <MenuItem key={activity} value={activity}>
                {TranslationUtils.get(`activity.${activity}`)}
              </MenuItem>
            ))}
          </Input>
          <Input type="tel" name="phone" label="Téléphone" fullWidth />
          <Input type="email" required name="contactEmail" label="Email" fullWidth />
        </Stack>
        <Stack>
          <FormControlLabel required control={<Checkbox name="cgv" />} label="J'ai lu et accepté les conditions générales de ventes." />
          <FormControlLabel control={<Checkbox name="newsletter" />} label="J'accepte de recevoir les offres d'AGENDA Diagnostics." />
        </Stack>
        <Stack direction="row" justifyContent="center" mt={1}>
          <Button type="submit">Envoyer</Button>
        </Stack>
      </Stack>
    </LayoutContainer>
  );
}

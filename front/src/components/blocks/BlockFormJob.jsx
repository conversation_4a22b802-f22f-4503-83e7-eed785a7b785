"use client";

import Input from "@/components/inputs/Input";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import TranslationUtils from "@/utils/translation.utils";
import { Button, Checkbox, FormControlLabel, MenuItem, Stack, Typography } from "@mui/material";
import { useRouter } from "next/navigation";
import { useState } from "react";
import LayoutContainer from "../LayoutContainer";
import { getBlockFieldValue } from "@/utils/builder.utils";
import SelectStores from "../inputs/SelectStores";

export default function BlockFormJob({ block }) {
  const { post, getToken } = useApi();
  const router = useRouter();
  const { add } = useSnack();
  const [coverLetter, setCoverLetter] = useState(null);
  const [cv, setCv] = useState(null);

  const anchor = getBlockFieldValue("anchor", block, "");
  const title = getBlockFieldValue("title", block, null);

  const civilities = ["MRS", "MISTER", "MISS", "DOCTOR", "PROFESSOR", "MASTER"];
  const situations = ["IN_OFFICE", "UNEMPLOYED", "IN_TRAING"];

  const submit = async (e) => {
    try {
      e.preventDefault();
      const formData = new FormData(e.target);

      await post("/job-applications", {
        civility: formData.get("civility") || null,
        lastname: formData.get("lastname"),
        firstname: formData.get("firstname"),
        address: formData.get("address"),
        zipCode: formData.get("zipCode"),
        city: formData.get("city"),
        phone: formData.get("phone"),
        email: formData.get("email"),
        birthdate: formData.get("birthdate"),
        training: formData.get("training"),
        professionalExperience: formData.get("professionalExperience"),
        computerLevel: formData.get("computerLevel") || null,
        situation: formData.get("situation") || null,
        workplace: formData.get("workplace") || null,
        availability: formData.get("availability") || null,
        salary: formData.get("salary") ? parseFloat(formData.get("salary")) : null,
        coverLetter: coverLetter ? coverLetter?.["@id"] : null,
        cv: cv ? cv?.["@id"] : null,
        jobOffer: null,
        wantedJob: formData.get("wantedJob"),
      });
      add("success", "Votre candidature a bien été envoyée !");
      e.target.reset();
      router.back();
      router.refresh();
    } catch (error) {
      add("error", err?.description ?? 'Une erreur est survenue, merci de vérifier votre saisie');
    }
  };

  const uploadImage = (file) => {
    let req = new XMLHttpRequest();

    return new Promise((resolve, reject) => {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("alt", file.name);
      req.open("POST", `${process.env.NEXT_PUBLIC_API_URL}/api/uploads`);
      req.setRequestHeader("X-Sinfin-Token", getToken());
      req.onreadystatechange = (e) => {
        if (req.readyState !== 4) {
          return;
        }

        if (req.status === 201) {
          resolve(JSON.parse(req.responseText));
        }
      };

      req.send(formData);
    });
  };

  const handleMedia = async (e, callback) => {
    const file = e.target.files[0];
    const media = await uploadImage(file);
    callback(media);
  };
  return (
    <LayoutContainer id={anchor || block?.uuid}>
      <Stack component="form" bgcolor="white" p={4} borderRadius={1} gap={2} onSubmit={submit}>
        {title ? (
          <Typography variant="h4" mb={3}>
            {title}
          </Typography>
        ) : null}
        <Stack display="grid" gridTemplateColumns="1fr 1fr" gap={2}>
          <Input name="wantedJob" label="Poste souhaité" select fullWidth required>
            <MenuItem value={"diagnostiqueur"}>Diagnostiqueur (H/F)</MenuItem>
            <MenuItem value={"assistant"}>Assistant(e)</MenuItem>
            <MenuItem value={"commercial"}>Commercial(e)</MenuItem>
            <MenuItem value={"autre"}>Autres</MenuItem>
          </Input>
          <Input name="civility" label="Civilité" select fullWidth>
            <MenuItem value={null}>
              <em>Non défini</em>
            </MenuItem>
            {civilities.map((civility) => (
              <MenuItem key={civility} value={civility}>
                {TranslationUtils.get(`civility.${civility}`)}
              </MenuItem>
            ))}
          </Input>
          <Input name="lastname" required label="Nom" fullWidth />
          <Input name="firstname" required label="Prénom" fullWidth />
          <Input name="address" required label="Adresse" fullWidth />
          <Input name="zipCode" required label="Code postal" fullWidth />
          <Input name="city" required label="Ville" fullWidth />
          <Input name="phone" required label="Téléphone" fullWidth />
          <Input name="email" required label="Email" fullWidth type="email" />
          <Input name="birthdate" required label="Date de naissance" type="date" fullWidth />
          <SelectStores name="agency" />
        </Stack>
        <Input name="training" multiline rows={3} required label="Vos formations et certifications" fullWidth />
        <Input name="professionalExperience" multiline rows={3} required label="Expériences professionnelles" fullWidth />
        <Input name="computerLevel" multiline rows={3} label="Niveau de connaissance informatique" fullWidth />
        <Input name="situation" label="Situation actuelle" select fullWidth>
          <MenuItem value={null}>
            <em>Non défini</em>
          </MenuItem>
          {situations.map((situation) => (
            <MenuItem key={situation} value={situation}>
              {TranslationUtils.get(`situation.${situation}`)}
            </MenuItem>
          ))}
        </Input>
        <Input name="workplace" label="Lieu de travail souhaité" fullWidth />
        <Input name="availability" label="Date de disponibilité" type="date" fullWidth />
        <Input name="salary" label="Salaire mensuel souhaité (brut)" fullWidth type="number" />
        <Input name="coverLetter" type="file" label="Lettre de motivation" fullWidth required onChange={(e) => handleMedia(e, setCoverLetter)} />
        <Input name="cv" type="file" label="CV" fullWidth required onChange={(e) => handleMedia(e, setCv)} />
        <Stack>
          <FormControlLabel
            required
            control={<Checkbox name="cgv" />}
            label="J'ai lu et accepté les conditions générales de ventes."
          />
          <FormControlLabel
            control={<Checkbox name="newsletter" />}
            label="J'accepte de recevoir les offres d'AGENDA Diagnostics."
          />
        </Stack>
        <Stack direction="row" justifyContent="flex-end">
          <Button type="submit">{TranslationUtils.get("global.validate")}</Button>
        </Stack>
      </Stack>
    </LayoutContainer>
  );
}

"use client";

import { Box, Button, Checkbox, FormControlLabel, Grid2, Paper, Stack, Typography } from "@mui/material";
import Input from "@/components/inputs/Input";
import LayoutContainer from "../LayoutContainer";
import SelectStores from "../inputs/SelectStores";
import { getBlockFieldValue } from "@/utils/builder.utils";
import { useApi } from "@/context/ApiProvider";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useSnack } from "@/context/SnackProvider";
import ROUTES from "@/enums/ROUTES";

export default function BlockFormNewsLetter({ block }) {
  const { post, get } = useApi();
  const { add } = useSnack();
  const router = useRouter();
  const searchParams = useSearchParams();
  const email = searchParams.get("email");

  const defaultAgency = searchParams.get("agency");
  const [agency, setAgency] = useState(null);
  const [count, setCount] = useState(0);

  const getAgency = async () => {
    try {
      const agency = await get(`/agencies/${defaultAgency}`);
      setAgency(agency);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (defaultAgency) {
      void getAgency();
    }
  }, [defaultAgency]);

  const anchor = getBlockFieldValue("anchor", block, "");
  const title = getBlockFieldValue("title", block, null);

  const handleSubmit = async (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    const data = {
      ...Object.fromEntries(formData.entries()),
      agency: formData.get("agency") === "" ? null : formData.get("agency"),
      sendMeCopy: formData.get("sendMeCopy") === "on",
    };
    try {
      await post("/mailer/contact", data);
      add("success", "Demande envoyée avec succès !");
      router.back();
      router.refresh();
    } catch (error) {
      console.log(error);
      add("error", "Veuillez renseigner tous les champs obligatoires");
    }
  };

  return (
    <LayoutContainer id={anchor || block.uuid}>
      <Stack component="form" onSubmit={handleSubmit} key={JSON.stringify(count)}>
        <Paper sx={{ p: 4 }}>
          {title ? (
            <Typography variant="h4" mb={3}>
              {title}
            </Typography>
          ) : null}
          <Grid2 container columnSpacing={4} rowSpacing={2}>
            <Grid2 size={{ xs: 12, md: 6 }} container>
              <Field label="Nom" required name="lastname" key={count} />
            </Grid2>
            <Grid2 size={{ xs: 12, md: 6 }} container>
              <Field label="Prénom" required name="firstname" />
            </Grid2>
            <Grid2 size={{ xs: 12, md: 6 }} container>
              <Field label="Email" required type="email" name="contactEmail" defaultValue={email ?? ""} />
            </Grid2>
            <Grid2 size={{ xs: 12, md: 6 }} container>
              <Field label="Téléphone" required type="tel" name="phone" />
            </Grid2>
            <Grid2 size={{ xs: 12, md: 6 }} container>
              <Field label="Code postal" required type="number" name="zip" />
            </Grid2>
            <Grid2 size={{ xs: 12, md: 6 }} container>
              <Grid2 size={{ xs: 12, md: 4 }}>
                <Typography variant="h6">Cabinet AGENDA</Typography>
              </Grid2>
              <Grid2 size={{ xs: 12, md: 8 }}>
                <SelectStores noLabel defaultValue={agency} />
              </Grid2>
            </Grid2>
            <Grid2 size={{ xs: 12 }} container>
              <Field
                label="Message"
                name="message"
                cols={[
                  { xs: 12, md: 2 },
                  { xs: 12, md: 10 },
                ]}
                minRows={4}
                required
                multiline
              />
            </Grid2>
            <Grid2 size={12} container>
              <Grid2 size={{ xs: 12, sm: 2 }} />
              <Grid2 size={{ xs: 12, sm: 10 }}>
                <Stack gap={2}>
                  <FormControlLabel
                    control={<Checkbox defaultChecked name="sendMeCopy" />}
                    label={<Typography variant="h6">{"M'envoyer une copie de cet Email"}</Typography>}
                  />
                  <Stack>
                    <FormControlLabel required control={<Checkbox name="cgv" />} label="J'ai lu et accepté les conditions générales de ventes." />
                    <FormControlLabel control={<Checkbox name="newsletter" />} label="J'accepte de recevoir les offres d'AGENDA Diagnostics." />
                  </Stack>
                  <Box>
                    <Button type="submit" variant="contained" color="primary">
                      Envoyer
                    </Button>
                  </Box>
                </Stack>
              </Grid2>
            </Grid2>
          </Grid2>
        </Paper>
      </Stack>
    </LayoutContainer>
  );
}

const Field = ({
  name,
  label,
  required,
  cols = [
    {
      xs: 12,
      md: 4,
    },
    {
      xs: 12,
      md: 8,
    },
  ],
  multiline,
  minRows,
  defaultValue = "",
  type = "text",
}) => {
  return (
    <>
      <Grid2 size={cols[0]}>
        <Typography variant="h6">
          {label} {required && "*"}
        </Typography>
      </Grid2>
      <Grid2 size={cols[1]}>
        <Input
          required={required}
          placeholder={label}
          name={name}
          defaultValue={defaultValue}
          minRows={minRows}
          multiline={multiline}
          fullWidth
          type={type}
        />
      </Grid2>
    </>
  );
};

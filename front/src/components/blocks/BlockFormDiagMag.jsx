"use client";

import { getBlockFieldValue } from "@/utils/builder.utils";
import React from "react";
import LayoutContainer from "../LayoutContainer";
import FormDiagmag from "../forms/superadmin/FormDiagmag";

export default function BlockFormDiagMag({ block }) {
  const anchor = getBlockFieldValue("anchor", block, "");
  const title = getBlockFieldValue("title", block, null);

  return (
    <LayoutContainer id={anchor || block?.uuid}>
      <FormDiagmag title={title} />
    </LayoutContainer>
  );
}

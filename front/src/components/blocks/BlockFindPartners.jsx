"use client";

import theme from "@/lib/theme";
import useQueryParams from "@/hooks/useQueryParams";
import { Box, Button, Paper, Stack, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import LayoutContainer from "@/components/LayoutContainer";
import SelectDepartment from "../inputs/SelectDepartment";
import { PARTNERS_ACTIVITIES } from "@/enums/PARTNERS_ACTIVITIES";
import FormatUtils from "@/utils/format.utils";
import Link from "next/link";
import { getBlockFieldValue } from "@/utils/builder.utils";
import { useApi } from "@/context/ApiProvider";
import Image from "next/image";

export default function BlockFindPartners({ block }) {
  const { get } = useApi();
  const [department, setDepartment] = useState(null);
  const [activity, setActivity] = useState([]);
  const [partners, setPartners] = useState([]);

  const { createQueryString } = useQueryParams();

  const anchor = getBlockFieldValue("anchor", block, "");

  const handleSubmit = async (e) => {
    e.preventDefault();
    const params = {
      ...(department?.value &&
        department?.value !== "all" && {
          department: department.value.replace("/api/location-departments/", ""),
        }),
      ...Object.fromEntries(activity.map((item, index) => [`activity[${index}]`, item])),
    };
    const data = await get(`/partners?${createQueryString(params)}`);
    setPartners(data.member);
  };

  const getData = async () => {
    const data = await get(`/partners`);
    setPartners(data.member);
  };

  useEffect(() => {
    getData();
  }, []);

  return (
    <LayoutContainer component={Paper} id={anchor || block.uuid}>
      <Stack component="form" onSubmit={handleSubmit} gap={2} p={4}>
        <Typography variant="h4">Rechercher des partenaires</Typography>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Typography>Département de la recherche</Typography>
          <SelectDepartment onChange={(e) => setDepartment(e)} noLabel allValue />
        </Stack>
        <Stack spacing={2}>
          <Typography>{"Domaine d'activité"}</Typography>
          <Stack
            direction={{
              xs: "column",
              md: "row",
            }}
            spacing={1}
          >
            {Object.values(PARTNERS_ACTIVITIES).map((a) => (
              <Stack
                key={a}
                onClick={() => setActivity((prev) => (prev.includes(a) ? prev.filter((e) => e !== a) : [...prev, a]))}
                p={1}
                borderRadius={1}
                sx={{
                  border: `1px solid ${theme.palette.primary.main}`,
                  fontWeight: "bold",
                  cursor: "pointer",
                  backgroundColor: activity.includes(a) ? theme.palette.primary.main : "transparent",
                  color: activity.includes(a) ? theme.palette.primary.contrastText : theme.palette.primary.main,
                  "&:hover": {
                    backgroundColor: theme.palette.primary.main,
                    color: theme.palette.primary.contrastText,
                  },
                }}
              >
                <Typography>{a}</Typography>
              </Stack>
            ))}
          </Stack>
        </Stack>
        <Box mt={3}>
          <Button type="submit" variant="contained" color="primary">
            Lancer la recherche
          </Button>
        </Box>
      </Stack>
      {partners?.length ? (
        <Stack gap={2} p={4} pt={0} display="grid" gridTemplateColumns="repeat(auto-fill, minmax(200px, 1fr))">
          {partners.map((partner) => (
            <Stack
              key={partner.uuid}
              gap={2}
              p={2}
              border={`1px solid ${theme.palette.primary.main}`}
              borderRadius={1}
              component={partner.link ? Link : "div"}
              href={partner.link}
              target="_blank"
              alignItems="center"
              justifyContent="space-between"
              sx={{
                color: theme.palette.primary.main,
                "&:hover": {
                  backgroundColor: partner.link ? theme.palette.primary.main : "transparent",
                  color: partner.link ? theme.palette.primary.contrastText : theme.palette.primary.main,
                },
              }}
            >
              <Image src={FormatUtils.image(partner.upload)} width={150} height={100} alt={partner.name} style={{ objectFit: "contain" }} />
              <Typography textAlign="center">{partner.name}</Typography>
            </Stack>
          ))}
        </Stack>
      ) : (
        <Typography p={4} pt={0} textAlign="center">
          <strong>Aucun partenaire trouvé correspondant à votre recherche.</strong>
        </Typography>
      )}
    </LayoutContainer>
  );
}

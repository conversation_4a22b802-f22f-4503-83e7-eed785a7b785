"use client";

import { <PERSON><PERSON>, Pagination, Paper, Stack, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import LayoutContainer from "../LayoutContainer";
import theme from "@/lib/theme";
import ROUTES from "@/enums/ROUTES";
import { getBlockFieldValue } from "@/utils/builder.utils";
import { useApi } from "@/context/ApiProvider";
import DIRECTIONS from "@/enums/DIRECTIONS";
import Loader from "../Loader";

export default function BlockJob({ block, agencyIRI = null }) {
  const { get } = useApi();
  const [page, setPage] = useState(1);
  const [data, setData] = useState(null);
  const anchor = getBlockFieldValue("anchor", block, "");

  const getData = async () => {
    const data = await get("/job-offers", {
      "order[publicationDate]": DIRECTIONS.DESC,
      status: true,
      page,
      ...agencyIRI && { "agency": agencyIRI },
    });
    setData(data);
  };

  useEffect(() => {
    getData();
  }, [page]);

  if (!data) {
    return (
      <LayoutContainer>
        <Loader />
      </LayoutContainer>
    );
  }

  if (!block && data.member.length === 0) {
    return null;
  }

  return (
    <LayoutContainer id={anchor || block?.uuid}>
      <Stack direction="row" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h2">Offre(s) d&apos;emploi</Typography>
        <Button href={ROUTES.JOB_ONLINE} color="secondary" sx={{ textAlign: "center" }}>
          Candidature spontanée
        </Button>
      </Stack>
      <Stack display={{ xs: "none", sm: "grid" }} gridTemplateColumns="1fr 1fr 1fr 90px" gap={2} py={2} px={3}>
        <Typography variant="bold">Référence</Typography>
        <Typography variant="bold">Poste</Typography>
        <Typography variant="bold">Lieu</Typography>
        <Stack />
      </Stack>
      {!data.member.length && <Typography>Aucune offre d&apos;emploi trouvée</Typography>}
      <Stack gap={1}>
        {data.member.map((job, i) => (
          <Stack
            key={i}
            gap={2}
            display="grid"
            gridTemplateColumns={{ sm: "1fr 1fr 1fr max-content", xs: "1fr" }}
            alignItems="center"
            py={2}
            px={3}
            component={Paper}
            border={`1px solid ${theme.palette.grey.light}`}
          >
            <Typography>{job.reference}</Typography>
            <Typography variant="bold">{job.jobName}</Typography>
            <Typography>{job.location}</Typography>
            <Stack>
              <Button color="primary" href={`/detail-job/${job.uuid}`}>
                Postuler
              </Button>
            </Stack>
          </Stack>
        ))}
      </Stack>
      <Stack justifyContent="center" alignItems="center" mt={2}>
        <Pagination
          showFirstButton
          showLastButton
          count={Math.ceil(data.totalItems / 30)}
          page={Number(page)}
          onChange={(_, page) => setPage(page)}
        />
      </Stack>
    </LayoutContainer>
  );
}

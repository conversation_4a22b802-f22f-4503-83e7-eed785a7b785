import { Stack, Typography } from "@mui/material";
import FormatUtils from "@/utils/format.utils";
import { getBlockFieldValue } from "@/utils/builder.utils";
import { FIELD_NAMES } from "../forms/formBlocks/FormBlocImageBanner";

export default function BlocImageBanner({ block }) {
  const upload = getBlockFieldValue(FIELD_NAMES.UPLOAD, block, null);
  const content = getBlockFieldValue(FIELD_NAMES.CONTENT, block, "");
  const anchor = getBlockFieldValue(FIELD_NAMES.ANCHOR, block, "");

  return (
    <Stack
      id={anchor || block.uuid}
      minHeight={300}
      justifyContent="center"
      alignItems="center"
      sx={{
        backgroundImage: `url(${FormatUtils.binary(upload)})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
      }}
    >
      <Typography component="div" dangerouslySetInnerHTML={{ __html: content }} />
    </Stack>
  );
}

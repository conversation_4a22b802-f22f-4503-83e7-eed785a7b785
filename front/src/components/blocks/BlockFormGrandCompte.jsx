"use client";

import { getBlockFieldValue } from "@/utils/builder.utils";
import React from "react";
import LayoutContainer from "../LayoutContainer";
import { Button, Checkbox, FormControlLabel, Stack, Typography } from "@mui/material";
import Input from "../inputs/Input";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";

export default function BlockFormGrandCompte({ block }) {
  const { add } = useSnack();
  const { post } = useApi();

  const anchor = getBlockFieldValue("anchor", block, "");
  const title = getBlockFieldValue("title", block, null);

  const submit = async (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    const data = {
      ...Object.fromEntries(formData.entries()),
    };
    try {
      await post("/mailer/big-account", data);
      add("success", "Message envoyé avec succès !");
    } catch (error) {
      console.log(error);
      add("error", "Une erreur est survenue lors de l'envoi du message.");
    }
  };
  return (
    <LayoutContainer id={anchor || block?.uuid}>
      <Stack component="form" bgcolor="white" p={4} borderRadius={1} gap={2} onSubmit={submit}>
        <Typography variant="h4" mb={3}>
          {title}
        </Typography>
        <Stack display="grid" gridTemplateColumns="1fr 1fr" gap={2}>
          <Input required name="company" label="Société" fullWidth />
          <Input required name="lastname" label="Nom" fullWidth />
          <Input required name="firstname" label="Prénom" fullWidth />
          <Input type="email" required name="contactEmail" label="Email" fullWidth />
          <Input type="tel" required name="phone" label="Téléphone" fullWidth />
        </Stack>
        <Input required multiline minRows={5} name="message" label="Message" fullWidth />
        <Stack>
          <FormControlLabel required control={<Checkbox name="cgv" />} label="J'ai lu et accepté les conditions générales de ventes." />
          <FormControlLabel control={<Checkbox name="newsletter" />} label="J'accepte de recevoir les offres d'AGENDA Diagnostics." />
        </Stack>
        <Stack direction="row" justifyContent="center" mt={1}>
          <Button type="submit">Envoyer</Button>
        </Stack>
      </Stack>
    </LayoutContainer>
  );
}

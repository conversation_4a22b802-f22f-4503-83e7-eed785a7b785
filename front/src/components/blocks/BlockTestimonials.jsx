"use client";

import { Paper, Stack, Typography } from "@mui/material";
import { getBlockFieldValue } from "@/utils/builder.utils";
import { FIELD_NAMES } from "@/components/forms/formBlocks/FormBlockTestimonials";
import CardTestimonial from "../cards/CardTestimonial";
import LayoutContainer from "../LayoutContainer";

export default function BlockTestimonials({ block }) {
  const title = getBlockFieldValue(FIELD_NAMES.TITLE, block, "");
  const cards = getBlockFieldValue(FIELD_NAMES.CARDS, block) || [];
  const anchor = getBlockFieldValue("anchor", block, "");

  return (
    <LayoutContainer id={anchor || block.uuid}>
      <Paper>
        <Stack gap={1} p={4}>
          <Typography variant="h2">
            {title}
          </Typography>
          {cards?.length > 0 && cards.map((card) => <CardTestimonial key={card.id} card={card} />)}
        </Stack>
      </Paper>
    </LayoutContainer>
  );
}

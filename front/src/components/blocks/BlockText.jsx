"use client";

import { Paper, Typography } from "@mui/material";
import LayoutContainer from "../LayoutContainer";
import { getBlockFieldValue } from "@/utils/builder.utils";
import { FIELD_NAMES } from "../forms/formBlocks/FormBlockText";

export default function BlockText({ block }) {
  const content = getBlockFieldValue(FIELD_NAMES.CONTENT, block, "");
  const anchor = getBlockFieldValue("anchor", block, "");
  const backgroundColor = getBlockFieldValue(FIELD_NAMES.BACKGROUND_COLOR, block, "");

  return (
    <LayoutContainer id={anchor || block.uuid} component={Paper} bgcolor={backgroundColor} py={2} px={4}>
      <Typography component="div" dangerouslySetInnerHTML={{ __html: content }} />
    </LayoutContainer>
  );
}

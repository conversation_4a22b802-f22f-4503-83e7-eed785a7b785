"use client";
import { getBlockFieldValue } from "@/utils/builder.utils";
import { Stack, Typography } from "@mui/material";
import LayoutContainer from "../LayoutContainer";
import CardMostViewed from "../cards/CardMostViewed";

export default function BlockMostViewed({ block }) {
  const title = getBlockFieldValue("title", block);
  const items = getBlockFieldValue("items", block);
  const anchor = getBlockFieldValue("anchor", block, "");

  return (
    <LayoutContainer id={anchor || block.uuid}>
      <Stack gap={2}>
        <Typography variant="h3">{title}</Typography>
        <Stack direction="row" gap={2} flexWrap="wrap">
          {items.map((item, index) => (
            <CardMostViewed key={index} item={item} />
          ))}
        </Stack>
      </Stack>
    </LayoutContainer>
  );
}

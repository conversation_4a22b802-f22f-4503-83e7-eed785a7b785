"use client";

import useQueryParams from "@/hooks/useQueryParams";
import { Box, Button, Pagination, Stack, Typography } from "@mui/material";
import LayoutContainer from "@/components/LayoutContainer";
import { useApi } from "@/context/ApiProvider";
import { useEffect, useState } from "react";
import CardNews from "../cards/CardNews";
import Loader from "../Loader";
import { POST_CATEGORIES } from "@/enums/POST_CATEGORIES";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { getBlockFieldValue } from "@/utils/builder.utils";

export default function BlockNews({ block }) {
  const [data, setData] = useState(null);
  const { get } = useApi();
  const router = useRouter();
  const pathname = usePathname();
  const { setParams } = useQueryParams();
  const searchParams = useSearchParams();
  const category = searchParams.get("category");
  const page = searchParams.get("page") ?? 1;

  const anchor = getBlockFieldValue("anchor", block, "");

  const getData = async () => {
    try {
      const data = await get("/posts", { itemsPerPage: 19, page, ...(category ? (category !== "all" ? { category } : {}) : {}) });
      setData(data);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    void getData();
  }, [category, page]);

  const handleChangeCategory = (c) => {
    if (category === c) {
      setParams({ category: "all", page: 1 });
    } else {
      setParams({ category: c, page: 1 });
    }
  };

  if (!data) {
    return (
      <LayoutContainer>
        <Loader />
      </LayoutContainer>
    );
  }

  if (!data.member.length) {
    return null;
  }

  window.scrollTo({ top: 0, behavior: "smooth" });

  return (
    <LayoutContainer gap={2} id={anchor || block.uuid}>
      <Typography variant="h3" component="p">Filtrer les actualités par thème :</Typography>
      <Stack direction="row" gap={1} flexWrap="wrap">
        <Button size="small" onClick={() => setParams({ category: "all" })} variant={!category || category === "all" ? "contained" : "outlined"}>
          Tout
        </Button>
        {Object.values(POST_CATEGORIES).map((c) => (
          <Button key={c} size="small" onClick={() => handleChangeCategory(c)} variant={c === category ? "contained" : "outlined"}>
            {c}
          </Button>
        ))}
      </Stack>
      <Box>{data.member.map((n, i) => (i === 0 ? <CardNews key={i} news={n} index={i} direction="row" /> : null))}</Box>
      <Stack display="grid" gridTemplateColumns={{ xs: "1fr", md: "1fr 1fr 1fr" }} gap={2}>
        {data.member.map((n, i) => (i !== 0 ? <CardNews key={i} news={n} index={i} direction="column" /> : null))}
      </Stack>
      <Stack alignItems="center">
        <Pagination
          showFirstButton
          showLastButton
          count={Math.ceil(data.totalItems / 19)}
          page={Number(page)}
          onChange={(_, page) => router.push(pathname + "?page=" + page)}
        />
      </Stack>
    </LayoutContainer>
  );
}

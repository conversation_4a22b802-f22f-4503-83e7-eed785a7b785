"use client";

import { <PERSON>ton, Checkbox, FormControlLabel, MenuItem, Paper, Stack, Typography } from "@mui/material";
import { useState } from "react";
import LayoutContainer from "../LayoutContainer";
import Input from "../inputs/Input";
import SelectCommune from "../inputs/SelectCommune";
import { QUOTE_MISSIONS } from "@/enums/QUOTE_MISSIONS";
import TranslationUtils from "@/utils/translation.utils";
import { useApi } from "@/context/ApiProvider";
import { useSnack } from "@/context/SnackProvider";
import { getBlockFieldValue } from "@/utils/builder.utils";

export default function BlockProQuote({ block }) {
  const { post } = useApi();
  const [zip, setZip] = useState("");
  const [missions, setMissions] = useState([]);
  const { add } = useSnack();
  const anchor = getBlockFieldValue("anchor", block, "");
  const title = getBlockFieldValue("title", block, null);

  const handleMissionClick = (mission) => {
    if (missions.includes(mission)) {
      setMissions(missions.filter((m) => m !== mission));
    } else {
      setMissions([...missions, mission]);
    }
  };

  const submit = async (e) => {
    e.preventDefault();
    try {
      const obj = {};
      Object.values(QUOTE_MISSIONS).forEach((mission, index) => {
        obj[mission] = missions.includes(mission);
      });
      await post("/pro-estimates", {
        civility: e.target.civility.value,
        lastname: e.target.lastname.value,
        firstname: e.target.firstname.value,
        company: e.target.company.value,
        city: e.target.department.value,
        email: e.target.email.value,
        customMissions: e.target.customMissions.value.length > 0 ? e.target.customMissions.value : null,
        ...obj,
      });
      e.target.reset();
      setMissions([]);
      setZip("");
      add("success", "Demande de devis envoyée avec succès");
    } catch (error) {
      add("error", "Erreur lors de l'envoi de la demande de devis");
      console.log(error);
    }
  };

  return (
    <LayoutContainer component={Paper} p={4} gap={2} id={anchor || block?.uuid}>
      <Stack component="form" onSubmit={submit}>
        {title ? (
          <Typography variant="h4" mb={3}>
            {title}
          </Typography>
        ) : null}
        <Stack gap={2} display="grid" gridTemplateColumns={{ xs: "1fr", md: "repeat(2, 1fr)" }}>
          <Input name="civility" label="Civilité" select fullWidth required>
            <MenuItem value={"MRS"}>Madame</MenuItem>
            <MenuItem value={"MISTER"}>Monsieur</MenuItem>
            <MenuItem value={"OTHER"}>Autre</MenuItem>
          </Input>
          <Input label="Nom" name="lastname" fullWidth required />
          <Input label="Prénom" name="firstname" fullWidth required />
          <Input label="Société" name="company" fullWidth required />
          <Input label="Code postal" name="zipcode" fullWidth value={zip} onChange={(e) => setZip(e.target.value)} required />
          {zip?.length !== 5 ? (
            <Input required placeholder="Commune*" disabled fullWidth label={"Commune"} />
          ) : (
            <SelectCommune name="city" zip={zip} required />
          )}
          <Input label="Email" name="email" fullWidth type="email" required />
        </Stack>
        <Stack gap={2} mt={2}>
          <Typography>Type de missions à réaliser</Typography>
          <Stack display="grid" gridTemplateColumns={{ xs: "1fr", md: "repeat(2, 1fr)" }} gap={2}>
            {Object.keys(QUOTE_MISSIONS).map((mission) => (
              <Button
                variant={missions.includes(QUOTE_MISSIONS[mission]) ? "contained" : "outlined"}
                size="large"
                key={mission}
                onClick={() => handleMissionClick(QUOTE_MISSIONS[mission])}
              >
                {TranslationUtils.get(`quote_missions.${mission}`)}
              </Button>
            ))}
          </Stack>
          <Input label="Mission spécifique" name="customMissions" fullWidth />
          <Stack>
            <FormControlLabel required control={<Checkbox name="cgv" />} label="J'ai lu et accepté les conditions générales de ventes." />
            <FormControlLabel control={<Checkbox name="newsletter" />} label="J'accepte de recevoir les offres d'AGENDA Diagnostics." />
          </Stack>
          <Stack direction="row" justifyContent="center">
            <Button type="submit">Envoyer</Button>
          </Stack>
        </Stack>
      </Stack>
    </LayoutContainer>
  );
}

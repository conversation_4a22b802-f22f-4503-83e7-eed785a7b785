"use client";
import { getBlockFieldValue } from "@/utils/builder.utils";
import { Stack, styled, Typography } from "@mui/material";
import { FIELD_NAMES } from "@/components/forms/formQuote/FormBlockQuote";
import FormQuote from "@/components/forms/formQuote/FormQuote";
import FormatUtils from "@/utils/format.utils";
import LayoutContainer from "@/components/LayoutContainer";

const StyledContainer = styled(Stack, {
  shouldForwardProp: (prop) => prop !== "background",
})(({ theme, background }) => ({
  backgroundImage: `url(${FormatUtils.binary(background)})`,
  backgroundSize: "cover",
  backgroundPosition: "center",
  backgroundRepeat: "no-repeat",
}));

export default function BlockQuote({ block }) {
  const file = getBlockFieldValue(FIELD_NAMES.FILE, block);
  const text = getBlockFieldValue(FIELD_NAMES.TEXT, block);
  const anchor = getBlockFieldValue("anchor", block, "");

  return (
    <StyledContainer background={file} id={anchor || block.uuid}>
      <LayoutContainer direction={{ xs: "column", md: "row" }} py={4} gap={10} justifyContent="space-between">
        <Stack py={5}>
          <Typography
            component="div"
            dangerouslySetInnerHTML={{
              __html: text,
            }}
          />
        </Stack>
        <Stack>
          <FormQuote block={block} />
        </Stack>
      </LayoutContainer>
    </StyledContainer>
  );
}

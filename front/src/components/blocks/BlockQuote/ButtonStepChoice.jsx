"use client";
import { But<PERSON>, Stack, styled } from "@mui/material";

const StyledButton = styled(<PERSON><PERSON>, {
  shouldForwardProp: (prop) => prop !== "isSelected",
})(({ theme, isSelected }) => ({
  height: "100%",
  fontWeight: "bold",
  textAlign: "center",
  "& svg": {
    fill: isSelected ? "white" : theme.palette.primary.main,
  },
}));

export default function ButtonStepChoice({
  children,
  index,
  selected,
  setSelected = () => {},
  fullWidth = true,
  goToNext,
  ...props
}) {
  return (
    <StyledButton
      variant={index && index === selected ? "contained" : "outlined"}
      color="primary"
      isSelected={index && index === selected}
      onClick={() => goToNext ? goToNext(index) : setSelected(index)}
      size="small"
      fullWidth={fullWidth}
      {...props}
    >
      <Stack spacing={1} justifyContent="center" alignItems="center">
        {children}
      </Stack>
    </StyledButton>
  );
}

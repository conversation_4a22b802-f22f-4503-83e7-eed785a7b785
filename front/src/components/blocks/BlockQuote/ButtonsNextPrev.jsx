"use client";
import { <PERSON>, But<PERSON>, Stack } from "@mui/material";

export default function ButtonsNextPrev({ onClickPrev, onClickNext, nextIsDisabled }) {
  return (
    <Stack
      direction="row"
      spacing={1}
      justifyContent="center"
      sx={{
        "& button": {
          fontWeight: "bold",
        },
      }}
    >
      {onClickPrev && (
        <Box>
          <Button
            size="small"
            variant="contained"
            color="primary"
            onClick={onClickPrev}
          >
            Précedent
          </Button>
        </Box>
      )}
      {onClickNext && (
        <Box>
          <Button
            size="small"
            variant="contained"
            color="primary"
            onClick={onClickNext}
            disabled={nextIsDisabled}
          >
            Suivant
          </Button>
        </Box>
      )}
    </Stack>
  );
}

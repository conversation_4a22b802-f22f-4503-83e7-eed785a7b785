"use client";

import { Button, Paper, Stack, Typography } from "@mui/material";
import { FIELD_NAMES } from "@/components/forms/formBlocks/FormBlocDoubleText";
import { getBlockFieldValue } from "@/utils/builder.utils";
import LayoutContainer from "../LayoutContainer";

export default function BlockDoubleText({ block }) {
  const left = {
    text: getBlockFieldValue(FIELD_NAMES.TEXT_FIRST, block),
    button: getBlockFieldValue(FIELD_NAMES.BUTTON_FIRST, block),
  };
  const right = {
    text: getBlockFieldValue(FIELD_NAMES.TEXT_SECOND, block),
    button: getBlockFieldValue(FIELD_NAMES.BUTTON_SECOND, block),
  };
  const anchor = getBlockFieldValue("anchor", block, "");

  return (
    <LayoutContainer component={Paper} direction={{ xs: "column", md: "row" }} gap={4} p={4} id={anchor || block.uuid}>
      {left.text || left.button ? (
        <Stack width="100%" gap={2}>
          <Typography
            component="div"
            dangerouslySetInnerHTML={{
              __html: left.text,
            }}
          />
          {left.button?.label ? (
            <Stack direction="row">
              <Button sx={{ backgroundColor: left.button.color, color: left.button.colorText }} href={left.button.link}>
                {left.button.label}
              </Button>
            </Stack>
          ) : null}
        </Stack>
      ) : null}
      {right.text || right.button ? (
        <Stack width="100%" gap={2}>
          <Typography
            component="div"
            dangerouslySetInnerHTML={{
              __html: right.text,
            }}
          />
          {right.button?.label ? (
            <Stack direction="row">
              <Button sx={{ backgroundColor: right.button.color, color: right.button.colorText }} href={right.button.link}>
                {right.button.label}
              </Button>
            </Stack>
          ) : null}
        </Stack>
      ) : null}
    </LayoutContainer>
  );
}

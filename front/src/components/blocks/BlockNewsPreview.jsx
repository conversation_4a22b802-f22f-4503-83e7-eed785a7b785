"use client";
import { getBlockFieldValue } from "@/utils/builder.utils";
import { Button, Paper, Stack, Typography } from "@mui/material";
import LayoutContainer from "../LayoutContainer";
import Input from "@/components/inputs/Input";
import CardNewsPreview from "../cards/CardNewsPreview";
import { useEffect, useState } from "react";
import { useApi } from "@/context/ApiProvider";
import Link from "next/link";
import { useRouter } from "next/navigation";

export default function BlockNewsPreview({ block }) {
  const { get } = useApi();
  const router = useRouter();

  const [news, setNews] = useState([]);

  const anchor = getBlockFieldValue("anchor", block, "");

  const getData = async () => {
    try {
      const data = await get("/posts", { itemsPerPage: 3, page: 1 });
      setNews(data.member);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    void getData();
  }, []);

  const handleSubmit = (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    const email = formData.get("email");

    router.push(getBlockFieldValue("link_newsletter", block) ? `${getBlockFieldValue("link_newsletter", block)}?email=${email}` : "#");
  };

  return (
    <LayoutContainer gap={2} id={anchor || block.uuid}>
      <Stack gap={2}>
        <Stack direction="row" alignItems="center" gap={2}>
          <Typography
            component="div"
            dangerouslySetInnerHTML={{
              __html: getBlockFieldValue("title", block),
            }}
          />
          <Typography component={Link} href={getBlockFieldValue("link", block, "")} variant="link">
            Voir tout
          </Typography>
        </Stack>
        <Stack direction={{ xs: "column", md: "row" }} gap={2} width="100%">
          {news.map((n, i) => (i === 0 ? <CardNewsPreview key={i} news={n} direction="column" /> : null))}
          <Stack gap={2}>{news.map((n, i) => (i !== 0 ? <CardNewsPreview key={i} news={n} direction="row" /> : null))}</Stack>
        </Stack>
        <Stack component={Paper} alignItems="center" spacing={2} p={4}>
          <Stack component="form" onSubmit={handleSubmit}>
            <Typography
              component="div"
              dangerouslySetInnerHTML={{
                __html: getBlockFieldValue("text", block),
              }}
            />
            <Stack direction="row" gap={2} justifyContent="center" mt={2}>
              <Stack
                sx={{
                  minWidth: {
                    xs: "auto",
                    lg: 300,
                  },
                }}
              >
                <Input name="email" placeholder="Votre adresse email*" type="email" fullWidth required />
              </Stack>
              <Button color="primary" type="submit">
                {"S'inscrire"}
              </Button>
              {/* <Link href={getBlockFieldValue("link_newsletter", block) ? `${getBlockFieldValue("link_newsletter", block)}?email=${email}` : "#"}>
              </Link> */}
            </Stack>
          </Stack>
        </Stack>
      </Stack>
    </LayoutContainer>
  );
}

"use client";

import { getBlockFieldValue } from "@/utils/builder.utils";
import React from "react";
import LayoutContainer from "../LayoutContainer";
import FormDiagAssist from "../forms/superadmin/FormDiagAssist";

export default function BlockFormDiagAssist({ block }) {
  const anchor = getBlockFieldValue("anchor", block, "");
  const title = getBlockFieldValue("title", block, null);

  return (
    <LayoutContainer id={anchor || block?.uuid}>
      <FormDiagAssist title={title} />
    </LayoutContainer>
  );
}

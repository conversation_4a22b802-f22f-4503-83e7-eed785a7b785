"use client";

import { <PERSON><PERSON>, Stack } from "@mui/material";
import Map from "./Map";
import Input from "@/components/inputs/Input";
import LayoutContainer from "@/components/LayoutContainer";
import { useApi } from "@/context/ApiProvider";
import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { getBlockFieldValue } from "@/utils/builder.utils";

export default function BlockMap({ block }) {
  const { get } = useApi();
  const [stores, setStores] = useState([]);
  const [loading, setLoading] = useState(true);
  const searchParams = useSearchParams();
  const search = searchParams.get("search");

  const anchor = getBlockFieldValue("anchor", block, "");

  const getData = async (search) => {
    setLoading(true);
    try {
      const filters = {
        pagination: false,
      };
      if (search) {
        filters.search = search;
      }
      const data = await get("/agencies-map", filters);
      setStores(data.member);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!search) {
      void getData();
    }
  }, []);

  useEffect(() => {
    if (search) {
      void getData(search);
    }
  }, [search]);

  const submit = (e) => {
    e.preventDefault();
    void getData(e.target.search.value);
  };

  return (
    <LayoutContainer gap={1} id={anchor || block.uuid}>
      <Stack direction="row" gap={2} component="form" width={{ xs: "100%", sm: "50%" }} onSubmit={submit}>
        <Input type="search" placeholder="Ville,code postal" fullWidth name="search" defaultValue={search} />
        <Button type="submit">Rechercher</Button>
      </Stack>

      <Map markers={stores} key={JSON.stringify(stores)} loading={loading} showRadius />
    </LayoutContainer>
  );
}

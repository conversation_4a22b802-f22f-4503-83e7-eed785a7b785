"use client";
import { getPhoneFormat, slugify } from "@/utils/string.utils";
import { Box, Button, CircularProgress, IconButton, Paper, Slide, Stack, Typography } from "@mui/material";
import mapboxgl from "mapbox-gl";
import "mapbox-gl/dist/mapbox-gl.css";
import { useEffect, useRef, useState } from "react";
import "@mapbox/mapbox-gl-geocoder/dist/mapbox-gl-geocoder.css";
import { Close, OpenInNew } from "@mui/icons-material";
import ScheduleLine from "@/components/admin/ScheduleLine";
import Link from "next/link";
import CardCabinetDevis from "@/components/cards/CardCabinetDevis";

mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAP_API;

export default function Map({ markers, approx = false, showRadius = false, interactive = true, loading = false }) {
  const [selected, setSelected] = useState(null);
  const mapContainer = useRef(null);
  const map = useRef(null);

  useEffect(() => {
    if (!map.current) {
      map.current = new mapboxgl.Map({
        container: mapContainer.current,
        style: "mapbox://styles/mapbox/streets-v12?optimize=true",
        center: {
          lat: markers?.[0]?.location.latitude || 48.2664324,
          lng: markers?.[0]?.location.longitude || 4.076009,
        },
        zoom: 5,
        interactive,
        attributionControl: false,
      });

      if (approx) {
        map.current.scrollZoom.disable();
      }
    }

    markers.forEach((marker) => {
      if (!marker.location.latitude || !marker.location.longitude) {
        return;
      }

      const mapMarker = new mapboxgl.Marker({ color: "#ff0000" }).setLngLat([marker.location.longitude, marker.location.latitude]).addTo(map.current);

      mapMarker.getElement().addEventListener("click", () => {
        setSelected(marker);
        map.current.flyTo({
          // Doing this to make the marker appear when store infos is opened
          center: [marker.location.longitude - 0.007, marker.location.latitude - 0.007],
          zoom: 12,
        });
      });
    });
  }, [markers]);

  return (
    <Box borderRadius={showRadius ? "8px" : 0} overflow="hidden" position="relative">
      <Stack component="div" ref={mapContainer} sx={{ height: { xs: "600px", sm: "500px" }, width: "100%" }}>
        {loading && (
          <Stack
            component="div"
            sx={{ height: { xs: "600px", sm: "500px" }, width: "100%" }}
            id="map-loading"
            position="absolute"
            direction="row"
            justifyContent="center"
            alignItems="center"
            bgcolor="rgba(0, 0, 0, 0.2)"
            zIndex={1000}
          >
            <CircularProgress />
          </Stack>
        )}
      </Stack>
      <Box
        sx={{
          position: "absolute",
          top: { xs: "unset", sm: 0 },
          left: 0,
          bottom: { xs: 0, sm: "unset" },
          overflow: "auto",
          height: { xs: "40%", sm: "100%" },
          p: 1,
          maxWidth: { xs: "100%", sm: "30%" },
        }}
      >
        <Stack spacing={1}>
          {markers.map((marker, i) => (
            <Box
              key={i}
              component={Paper}
              sx={{
                p: 2,
                cursor: "pointer",
              }}
              onClick={() => {
                setSelected(marker);
                map.current.flyTo({
                  // Doing this to make the marker appear when store infos is opened
                  center: [marker.location.longitude - 0.007, marker.location.latitude - 0.007],
                  zoom: 12,
                });
              }}
            >
              <Typography>{marker.name}</Typography>
              <Typography variant="subtitle1">
                {marker.location.address1}, {marker.location.postcode} {marker.location.city}
              </Typography>
              <Stack direction="row" justifyContent="space-between">
                <Typography fontWeight="bold" color="primary">
                  {getPhoneFormat(marker.contact.phone)}
                </Typography>
                <IconButton
                  component={Link}
                  href={`/detail-cabinet/${slugify(marker?.name)}/${marker?.location?.postcode?.slice(0, 2)}/${slugify(marker?.location?.city)}/${
                    marker?.legacyId
                  }`}
                  size="small"
                  color="primary"
                >
                  <OpenInNew fontSize="small" color="white" />
                </IconButton>
              </Stack>
            </Box>
          ))}
        </Stack>
      </Box>
      <Slide direction="down" in={!!selected} mountOnEnter unmountOnExit>
        <Stack
          position="absolute"
          top={0}
          right={{ xs: "8px", sm: 25 }}
          width={{ xs: "calc(100% - 16px)", sm: "60%" }}
          bgcolor="#ffffff"
          p={2}
          sx={{
            borderBottomLeftRadius: "15px",
            borderBottomRightRadius: "15px",
          }}
          gap={2}
        >
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h3">{selected?.name}</Typography>
            <IconButton onClick={() => setSelected(null)}>
              <Close />
            </IconButton>
          </Stack>
          <CardCabinetDevis agency={selected} padding={0} />
          <Stack gap={1}>
            <Stack
              display="block"
              sx={{
                columnCount: { xs: 1, md: 2 },
              }}
            >
              {selected?.schedule &&
                Object.keys(selected?.schedule)
                  .filter((e) => typeof selected.schedule[e] === "object")
                  .map((e, i) => <ScheduleLine key={i} label={e} schedule={selected.schedule[e]} />)}
            </Stack>
            <Button
              component={Link}
              href={`/detail-cabinet/${slugify(selected?.name)}/${selected?.location?.postcode?.slice(0, 2)}/${slugify(selected?.location?.city)}/${
                selected?.legacyId
              }`}
            >
              Voir la fiche du cabinet
            </Button>
          </Stack>
        </Stack>
      </Slide>
    </Box>
  );
}

"use client";

import { Stack, Typography } from "@mui/material";
import { getBlockFieldValue } from "@/utils/builder.utils";
import CardBuilder from "../superadmin/CardBuilder";
import { FIELD_NAMES } from "@/components/forms/formBlocks/FormBlockCards";
import LayoutContainer from "../LayoutContainer";
import WarningIcon from "@mui/icons-material/Warning";

export default function BlockCards({ block }) {
  const anchor = getBlockFieldValue(FIELD_NAMES.ANCHOR, block, "");

  return (
    <LayoutContainer id={anchor || block.uuid}>
      <Stack gap={1}>
        <Typography variant="h3">{getBlockFieldValue(FIELD_NAMES.TITLE, block)}</Typography>
        <Stack
          display="grid"
          gap={1}
          mb={1}
          gridTemplateColumns={{ xs: "1fr", md: `repeat(${getBlockFieldValue(FIELD_NAMES.COLUMNS, block) || 2}, 1fr)` }}
        >
          {getBlockFieldValue(FIELD_NAMES.CARDS, block).map((card) => (
            <Stack key={card.id} height="100%">
              <CardBuilder card={card} />
            </Stack>
          ))}
        </Stack>
        {getBlockFieldValue(FIELD_NAMES.TEXT, block)?.length && getBlockFieldValue(FIELD_NAMES.TEXT, block) !== "<p></p>" ? (
          <Stack direction="row" gap={1}>
            <WarningIcon color="error" />
            <Typography
              component="div"
              dangerouslySetInnerHTML={{
                __html: getBlockFieldValue(FIELD_NAMES.TEXT, block),
              }}
            />
          </Stack>
        ) : null}
      </Stack>
    </LayoutContainer>
  );
}

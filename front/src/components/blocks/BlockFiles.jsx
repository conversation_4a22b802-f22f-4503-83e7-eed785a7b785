"use client";

import { getBlockFieldValue } from "@/utils/builder.utils";
import { Button, Stack, Typography } from "@mui/material";
import { FIELD_NAMES } from "@/components/forms/formBlocks/FormBlockFiles";
import LayoutContainer from "../LayoutContainer";

export default function BlockFiles({ block }) {
  const anchor = getBlockFieldValue("anchor", block, "");

  return (
    <LayoutContainer gap={2} id={anchor || block.uuid}>
      <Stack>
        <Typography variant="h2">{getBlockFieldValue(FIELD_NAMES.TITLE, block)}</Typography>
      </Stack>
      <Stack>
        {getBlockFieldValue(FIELD_NAMES.ROWS, block).map((row, i) => (
          <Stack
            key={row.id}
            direction="row"
            alignItems="center"
            justifyContent="space-between"
            bgcolor={i % 2 !== 0 ? "white" : "grey.extraLight"}
            py={1}
            px={4}
          >
            <Typography>{row.label}</Typography>
            {row.file && (
              <Stack direction="row" gap={1} width="35%">
                <Button color="primary" href={row.file}>
                  Lire
                </Button>
                <Button color="primary" href={row.file} download>
                  Télécharger
                </Button>
              </Stack>
            )}
          </Stack>
        ))}
      </Stack>
    </LayoutContainer>
  );
}

"use client";

import { But<PERSON>, Paper, Stack, Typography } from "@mui/material";
import LayoutContainer from "../LayoutContainer";
import FormatUtils from "@/utils/format.utils";
import { getBlockFieldValue } from "@/utils/builder.utils";
import ImageOrVideo from "../ImageOrVideo";
import { useApi } from "@/context/ApiProvider";
import { useEffect, useState } from "react";

export default function BlockTextMedia({ block }) {
  const { get } = useApi();
  const [media, setMedia] = useState(null);
  const upload = getBlockFieldValue("upload", block, null);
  const video = getBlockFieldValue("video", block, null);
  const content = getBlockFieldValue("content", block, "");
  const background = getBlockFieldValue("background", block, null);
  const alignment = getBlockFieldValue("alignment", block, "left");
  const anchor = getBlockFieldValue("anchor", block, "");
  const button = getBlockFieldValue("button", block, {});

  const getMedia = async () => {
    const media = await get(`/uploads/${upload?.replace("/api/uploads/", "")}`);
    setMedia(media);
  };

  useEffect(() => {
    if (upload) {
      getMedia();
    }
  }, [upload]);

  return (
    <LayoutContainer id={anchor || block.uuid}>
      <Stack
        component={Paper}
        p={3}
        gap={3}
        direction="row"
        display="grid"
        gridTemplateColumns={{ xs: "1fr", md: "1fr 1fr" }}
        sx={{
          backgroundImage: background ? `url(${FormatUtils.binary(background)})` : null,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      >
        {alignment === "left" && (media || video) ? <ImageOrVideo media={media} video={video} /> : null}
        <Stack>
          <Typography flexGrow={1} component="div" dangerouslySetInnerHTML={{ __html: content }} />
          {button.label?.length ? (
            <Stack direction="row" mt={2}>
              <Button sx={{ backgroundColor: button.color, color: button.colorText }} href={button.link}>
                {button.label}
              </Button>
            </Stack>
          ) : null}
        </Stack>
        {alignment === "right" && (media || video) ? <ImageOrVideo media={media} video={video} /> : null}
      </Stack>
    </LayoutContainer>
  );
}

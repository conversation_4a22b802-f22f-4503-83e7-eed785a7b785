"use client";

import { But<PERSON>, Stack, styled, Typography } from "@mui/material";
import arrow from "@public/assets/pictos/arrow.svg";
import { FIELD_NAMES } from "@/components/forms/formBlocks/FormBlockDoubleBlock";
import { getBlockFieldValue } from "@/utils/builder.utils";
import LayoutContainer from "../LayoutContainer";
import FormatUtils from "@/utils/format.utils";

const MainContainer = styled(Stack, {
  shouldForwardProp: (prop) => prop !== "background",
})(({ theme, background }) => ({
  padding: theme.spacing(4),
  borderRadius: theme.shape.borderRadius,
  "& ul": {
    padding: "0px !important",
  },
  "& li": {
    display: "flex",
    alignItems: "center",
    "& p span:after": {
      content: "'\\2192'",
      marginLeft: theme.spacing(1),
      fontSize: "16px",
    },

    "& p:not(:has(span)):after": {
      content: "'\\2192'",
      marginLeft: theme.spacing(1),
      fontSize: "16px",
    },
    "&:after": {
      content: "''",
    },
  },
  backgroundImage: background ? `url(${FormatUtils.binary(background)})` : null,
  backgroundColor: background ? null : theme.palette.white,
  backgroundSize: "cover",
  backgroundPosition: "center",
  width: "100%",
}));

export default function BlockDoubleBlock({ block }) {
  const left = {
    background: getBlockFieldValue(FIELD_NAMES.BACKGROUND_FIRST, block),
    content: getBlockFieldValue(FIELD_NAMES.CONTENT_FIRST, block),
    button: getBlockFieldValue(FIELD_NAMES.BUTTON_FIRST, block),
  };
  const right = {
    background: getBlockFieldValue(FIELD_NAMES.BACKGROUND_SECOND, block),
    content: getBlockFieldValue(FIELD_NAMES.CONTENT_SECOND, block),
    button: getBlockFieldValue(FIELD_NAMES.BUTTON_SECOND, block),
  };
  const anchor = getBlockFieldValue("anchor", block, "");

  return (
    <LayoutContainer id={anchor || block.uuid}>
      <Stack direction={{ xs: "column", md: "row" }} gap={3}>
        <MainContainer background={left.background} gap={4}>
          <Typography
            component="div"
            dangerouslySetInnerHTML={{
              __html: left.content,
            }}
          />
          <Stack direction="row">
            {left.button?.label ? (
              <Button sx={{ backgroundColor: left.button.color, color: left.button.colorText }} href={left.button.link}>
                {left.button.label}
              </Button>
            ) : null}
          </Stack>
        </MainContainer>
        <MainContainer background={right.background} gap={4}>
          <Typography
            component="div"
            dangerouslySetInnerHTML={{
              __html: right.content,
            }}
          />
          <Stack direction="row">
            {right.button?.label ? (
              <Button sx={{ backgroundColor: right.button.color, color: right.button.colorText }} href={right.button.link}>
                {right.button.label}
              </Button>
            ) : null}
          </Stack>
        </MainContainer>
      </Stack>
    </LayoutContainer>
  );
}

"use client";

import background from "@public/assets/images/banniere-map.jpg";
import { Button, Stack, Typography } from "@mui/material";
import Input from "@/components/inputs/Input";
import RoomIcon from "@mui/icons-material/Room";
import { useRouter } from "next/navigation";
import { getBlockFieldValue } from "@/utils/builder.utils";

export default function BlockFindMap({ block }) {
  const router = useRouter();

  const label = getBlockFieldValue("label", block || null);
  const link = getBlockFieldValue("link", block || "");
  const anchor = getBlockFieldValue("anchor", block, "");

  const submit = (e) => {
    e.preventDefault();
    router.push(`${link}?search=${e.target.search.value}`);
  };

  return (
    <Stack
      id={anchor || block.uuid}
      sx={{
        position: "relative",
        overflow: "hidden",
        minHeight: 300,
        color: "white",
      }}
      justifyContent="center"
      alignItems="center"
    >
      {/* To make custom brightness of image */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `url(${background.src})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          filter: "brightness(0.75)",
          // Some left red pixels are not covered by the image
          transform: "scale(1.01)",
          zIndex: -1,
        }}
      />
      <Stack justifyContent="center" alignItems="center" textAlign="center" maxWidth={500} gap={2}>
        <Typography variant="h2">{label}</Typography>
        <Stack direction={{ xs: "column", md: "row" }} gap={2} component="form" onSubmit={submit} alignItems="center">
          <Input name="search" placeholder="Ville,code postal" />
          <Button type="submit" variant="contained" color="primary" endIcon={<RoomIcon />}>
            Trouver un cabinet Agenda
          </Button>
        </Stack>
      </Stack>
    </Stack>
  );
}

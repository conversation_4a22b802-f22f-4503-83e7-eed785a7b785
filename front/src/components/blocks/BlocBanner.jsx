import { getBlockFieldValue } from "@/utils/builder.utils";
import { Stack, Typography } from "@mui/material";
import { FIELD_NAMES } from "../forms/formBlocks/FormBlocBanner";

export default function BlocBanner({ block }) {
  const color = getBlockFieldValue(FIELD_NAMES.COLOR, block, null);
  const content = getBlockFieldValue(FIELD_NAMES.CONTENT, block, "");
  const anchor = getBlockFieldValue(FIELD_NAMES.ANCHOR, block, "");
  const height = getBlockFieldValue(FIELD_NAMES.HEIGHT, block, 230);

  return (
    <Stack minHeight={parseInt(height)} bgcolor={color} justifyContent="center" alignItems="center" px={4} id={anchor || block.uuid}>
      <Typography component="div" dangerouslySetInnerHTML={{ __html: content }} />
    </Stack>
  );
}

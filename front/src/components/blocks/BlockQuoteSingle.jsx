import FormQuote from "@/components/forms/formQuote/FormQuote";
import LayoutContainer from "../LayoutContainer";
import { Stack } from "@mui/material";
import { getBlockFieldValue } from "@/utils/builder.utils";

export default function BlockQuoteSingle({ block }) {
  const anchor = getBlockFieldValue("anchor", block, "");

  return (
    <LayoutContainer id={anchor || block?.uuid}>
      <Stack maxWidth={500} m="auto">
        <FormQuote block={block} />
      </Stack>
    </LayoutContainer>
  );
}

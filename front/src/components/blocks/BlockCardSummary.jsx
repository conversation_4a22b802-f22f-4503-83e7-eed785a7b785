"use client";

import { getBlockFieldValue } from "@/utils/builder.utils";
import FormatUtils from "@/utils/format.utils";
import { Paper, Stack, Typography } from "@mui/material";
import Image from "next/image";
import React from "react";
import LayoutContainer from "../LayoutContainer";
import Link from "next/link";

export default function BlockCardSummary({ block }) {
  const title = getBlockFieldValue("title", block);
  const cards = getBlockFieldValue("cards", block, []);
  const anchor = getBlockFieldValue("anchor", block, "");

  const PaperLink = (props) => {
    return <Paper component={Link} {...props} />;
  };

  return (
    <LayoutContainer id={anchor || block.uuid}>
      <Stack>
        <Typography textAlign="center" variant="h2">
          {title}
        </Typography>
        <Stack display="grid" gridTemplateColumns={{ xs: "repeat(1, 1fr)", md: "repeat(4, 1fr)" }} gap={2} mt={4}>
          {cards.map((card) => (
            <Stack
              key={card.id}
              component={card.link ? PaperLink : Paper}
              {...(card.link && { href: card.link })}
              gap={2}
              p={2}
              justifyContent="space-between"
              alignItems="center"
              display="grid"
              gridTemplateColumns="1fr auto"
            >
              <Stack
                flexGrow={1}
                borderRadius={"100%"}
                overflow="hidden"
                width={55}
                height={55}
                bgcolor="primary.main"
                justifyContent="center"
                alignItems="center"
              >
                <Image src={FormatUtils.image(card?.image?.["@id"])} alt={card?.image?.alt} width={25} height={25} objectFit="contain" />
              </Stack>
              <Typography variant="bold">{card.title}</Typography>
            </Stack>
          ))}
        </Stack>
      </Stack>
    </LayoutContainer>
  );
}

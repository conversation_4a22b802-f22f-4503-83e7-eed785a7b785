"use client";

import { Table, TableBody, Table<PERSON>ell, TableContainer, TableHead, TableRow, Typography } from "@mui/material";
import React from "react";
import LayoutContainer from "../LayoutContainer";
import { getBlockFieldValue } from "@/utils/builder.utils";

export default function BlockDiagnostic({ block }) {
  const table = block.parameters.find((p) => p.type === "table");
  const values = table.value;
  const anchor = getBlockFieldValue("anchor", block, "");

  const Cell = ({ children }) => <TableCell align="center">{children}</TableCell>;

  return (
    <LayoutContainer id={anchor || block?.uuid}>
      <TableContainer>
        <Table sx={{ minWidth: 650 }} aria-label="simple table">
          <TableHead>
            <TableRow>
              <TableCell align="center">
                <Typography variant="h6">Prestations</Typography>
              </TableCell>
              <TableCell align="center">
                <strong>T/L</strong>
              </TableCell>
              <TableCell align="center">
                <strong>Types de locaux</strong>
              </TableCell>
              <TableCell align="center">
                <strong>Validité</strong>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {values.map((value, idx) => (
              <TableRow key={`${value.title}-${idx}`}>
                <Cell>
                  <Typography variant="bold">{value?.title}</Typography>
                </Cell>
                <Cell>{value?.transaction}</Cell>
                <Cell>
                  <Typography variant="body1" dangerouslySetInnerHTML={{ __html: value?.types }} />
                </Cell>
                <Cell>
                  <Typography variant="body1" dangerouslySetInnerHTML={{ __html: value?.validity }} />
                </Cell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </LayoutContainer>
  );
}

import { getBlockFieldValue } from "@/utils/builder.utils";
import { Button, Stack, Typography } from "@mui/material";

export default function BlockCategories({ block }) {
  const text = getBlockFieldValue("title", block);
  const categories = getBlockFieldValue("categories", block, []);
  const anchor = getBlockFieldValue("anchor", block, "");

  return (
    <Stack alignItems="center" justifyContent="center" spacing={2} id={anchor || block.uuid}>
      <Typography
        component="div"
        dangerouslySetInnerHTML={{
          __html: text,
        }}
      />
      <Stack gap={1} direction="row" maxWidth="50%" flexWrap="wrap" justifyContent="center">
        {categories.map((c, i) => (
          <Button key={i} size="small" variant="contained" color="primary" href={c.link}>
            {c.label}
          </Button>
        ))}
      </Stack>
    </Stack>
  );
}

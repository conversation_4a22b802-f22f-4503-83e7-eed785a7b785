"use client";
import { getBlockFieldValue } from "@/utils/builder.utils";
import { Stack, Typography } from "@mui/material";
import "swiper/css";
import "swiper/css/pagination";
import { Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import LayoutContainer from "../../LayoutContainer";
import "swiper/css/navigation";
import SlideContent from "./SlideContent";
import Link from "next/link";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";

export default function BlockCarousel({ block }) {
  const title = getBlockFieldValue("title", block);
  const link = getBlockFieldValue("link", block);
  const items = getBlockFieldValue("items", block, []);
  const anchor = getBlockFieldValue("anchor", block, "");

  return (
    <LayoutContainer id={anchor || block.uuid}>
      <Stack gap={2}>
        <Stack direction="row" alignItems="center" gap={1}>
          <Typography variant="h3" component="h2">{title}</Typography>
          {link ? (
            <Typography variant="link" component={Link} href={link}>
              Voir tout
            </Typography>
          ) : null}
        </Stack>
        <Stack className="mySwiper">
          {items.length ? (
            <>
              <Swiper
                spaceBetween={20}
                slidesPerView={"auto"}
                modules={[Navigation]}
                navigation={{
                  prevEl: ".prev",
                  nextEl: ".next",
                }}
              >
                {items.map((item, i) => (
                  <SwiperSlide key={i}>
                    <SlideContent item={item} />
                  </SwiperSlide>
                ))}
              </Swiper>
              <Stack className="prev">
                <ArrowBackIcon />
              </Stack>
              <Stack className="next">
                <ArrowForwardIcon />
              </Stack>
            </>
          ) : null}
        </Stack>
      </Stack>
    </LayoutContainer>
  );
}

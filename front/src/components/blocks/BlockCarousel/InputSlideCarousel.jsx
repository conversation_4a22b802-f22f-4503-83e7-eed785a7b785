"use client";

import { <PERSON><PERSON>, <PERSON>vider, Stack, styled, Typography } from "@mui/material";
import Input from "@/components/inputs/Input";
import InputMedias from "@/components/inputs/InputMedias";
import InputSkeleton from "@/components/inputs/InputSkeleton";
import InputWysiwyg from "@/components/inputs/wysiwyg/InputWysiwyg";
import useCollection from "@/hooks/useCollection";
import SlideContent from "./SlideContent";
import Dom from "@/utils/dom.utils";
import { useState } from "react";

const StyledContainer = styled(Stack)(({ theme }) => ({
  border: "1px solid " + theme.palette.grey.main,
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
}));

export default function InputSlideCarousel({ label, required, defaultValue, onChange }) {
  const {
    items: cards,
    setValues,
    hasError,
    create,
    remove,
    values,
    update,
  } = useCollection({
    defaultItems: defaultValue,
    requiredFields: ["title"],
    onChange,
  });
  const [reset, setReset] = useState(false);

  const handleCreate = () => {
    if (values.id) {
      update(values.id);
    } else {
      create();
    }
    Dom.wysiwygReset("input-slide-carousel_wysiwyg");
    setReset(true);
  };

  const handleEdit = (card) => {
    setValues(card);
  };

  return (
    <InputSkeleton label={label} required={required} fullWidth>
      <StyledContainer gap={1}>
        <Input
          fullWidth
          label="Titre"
          value={values.title || ""}
          onChange={(e) =>
            setValues((values) => ({
              ...values,
              title: e.target.value,
            }))
          }
        />
        <InputWysiwyg
          id="input-slide-carousel_wysiwyg"
          label="Texte"
          defaultValue={values.text || ""}
          onChange={(e) =>
            setValues((values) => ({
              ...values,
              text: e,
            }))
          }
        />
        <Input
          fullWidth
          label="Lien"
          value={values.link || ""}
          onChange={(e) =>
            setValues((values) => ({
              ...values,
              link: e.target.value,
            }))
          }
        />
        <InputMedias
          label="Fond"
          name="file"
          reset={reset}
          defaultValue={values.file}
          onChange={(val) => {
            setValues((values) => ({
              ...values,
              file: val,
            }));
            setReset(false);
          }}
        />
        {hasError && (
          <Typography variant="body2" color="error">
            Veuillez remplir tous les champs
          </Typography>
        )}
        <Button onClick={handleCreate}>{values.id ? "Modifier la carte" : "Ajouter la carte"}</Button>
        {cards?.length > 0 && (
          <>
            <Divider />
            <Stack direction="row" alignItems="center" gap={1} flexWrap="wrap">
              {cards.map((card) => (
                <Stack key={card.id} width={200}>
                  <SlideContent item={card} edit={() => handleEdit(card)} remove={() => remove(card.id)} />
                </Stack>
              ))}
            </Stack>
          </>
        )}
      </StyledContainer>
    </InputSkeleton>
  );
}

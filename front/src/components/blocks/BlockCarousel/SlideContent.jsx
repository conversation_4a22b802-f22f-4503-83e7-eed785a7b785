"use client";
import { Chip, IconButton, Stack, styled, Typography } from "@mui/material";
import "swiper/css";
import "swiper/css/pagination";

import theme from "@/lib/theme";
import { hexToRgba } from "@/utils/theme.utils";
import { useRef } from "react";
import FormatUtils from "@/utils/format.utils";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import Link from "next/link";
const StyledCardContainer = styled(Stack, {
  shouldForwardProp: (prop) => prop !== "image",
})(({ theme, image }) => ({
  backgroundImage: `url(${FormatUtils.binary(image)})`,
  backgroundSize: "cover",
  backgroundPosition: "center",
  borderRadius: theme.spacing(1),
  width: "100%",
  height: "100%",
  alignItems: "center",
  transition: "justify-content 0.5s ease",
  position: "relative",
}));

const DeleteButton = styled(IconButton)(({ theme }) => ({
  position: "absolute",
  top: "5px",
  right: "5px",
  zIndex: 10,
}));

const EditButton = styled(IconButton)(({ theme }) => ({
  position: "absolute",
  top: "5px",
  right: "35px",
  zIndex: 10,
}));

const StyledOverlay = styled(Stack)(({ theme }) => ({
  transition: "opacity 0.5s ease",
  opacity: "0",
  backgroundColor: hexToRgba(theme.palette.primary.main, 0.5),
  height: "100%",
  minHeight: "200px",
  width: "100%",
  "*": {
    color: "white",
    fontWeight: "bold",
  },
}));

const StyledChip = styled(Chip)(({ theme }) => ({
  position: "absolute",
  zIndex: 1,
  top: "40%",
  transition: "top 0.5s ease",
  backgroundColor: theme.palette.primary.main,
  color: "white",
  fontWeight: "bold",
}));

const SlideContent = ({ item, remove, edit }) => {
  const overlayRef = useRef(null);
  const titleRef = useRef(null);

  return (
    <StyledCardContainer
      component={!item?.link?.length ? "div" : Link}
      href={item?.link}
      image={item.file?.["@id"]}
      onMouseEnter={() => {
        overlayRef.current.style.opacity = "1";
        titleRef.current.style.top = "10px";
        titleRef.current.style.backgroundColor = "inherit";
      }}
      onMouseLeave={() => {
        overlayRef.current.style.opacity = "0";
        titleRef.current.style.top = "40%";
        titleRef.current.style.backgroundColor = theme.palette.primary.main;
      }}
    >
      {edit ? (
        <EditButton onClick={edit}>
          <EditIcon />
        </EditButton>
      ) : null}
      {remove ? (
        <DeleteButton onClick={remove}>
          <DeleteIcon />
        </DeleteButton>
      ) : null}
      <StyledChip ref={titleRef} label={item.title} />
      <StyledOverlay ref={overlayRef} justifyContent="center" alignItems="center" px={1}>
        <Typography component="div" variant="body2" dangerouslySetInnerHTML={{ __html: item.text }} />
      </StyledOverlay>
    </StyledCardContainer>
  );
};

export default SlideContent;

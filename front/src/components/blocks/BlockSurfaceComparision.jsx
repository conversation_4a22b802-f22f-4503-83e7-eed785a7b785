"use client";

import { Paper, Stack, Typography } from "@mui/material";
import { getBlockFieldValue } from "@/utils/builder.utils";
import TableSurfaceComparision from "../tables/TableSurfaceComparision";
import { FIELD_NAMES } from "@/components/forms/formBlocks/FormBlockSurfaceComparision";
import LayoutContainer from "../LayoutContainer";

export default function BlockSurfaceComparision({ block }) {
  const anchor = getBlockFieldValue("anchor", block, "");

  return (
    <LayoutContainer id={anchor || block.uuid}>
      <Stack component={Paper} gap={1}>
        <Typography p={5} variant="h3">
          {getBlockFieldValue(FIELD_NAMES.TITLE, block)}
        </Typography>
        <TableSurfaceComparision title={getBlockFieldValue(FIELD_NAMES.TITLE, block)} rows={getBlockFieldValue(FIELD_NAMES.ROWS, block)} />
      </Stack>
    </LayoutContainer>
  );
}

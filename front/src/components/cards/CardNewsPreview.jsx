import { Paper, Stack, styled, Typography, useMediaQuery, useTheme } from "@mui/material";
import FormatUtils from "@/utils/format.utils";
import Link from "next/link";
import placeholder from "@public/assets/images/placeholder_news.png";

const Content = styled(Typography)(() => ({
  width: "100%",
  overflow: "hidden",
  textOverflow: "ellipsis",
  WebkitLineClamp: 3,
  WebkitBoxOrient: "vertical",
  display: "-webkit-box",
  "> *": {
    overflow: "hidden",
    textOverflow: "ellipsis",
    WebkitLineClamp: 3,
    WebkitBoxOrient: "vertical",
    display: "-webkit-box",
  },
}));

const MediaContainer = styled("img", {
  shouldForwardProp: (prop) => prop !== "url" && prop !== "direction",
})(({ direction }) => ({
  maxHeight: "230px",
  width: direction === "row" ? "50%" : "auto",
  objectFit: "contain",
  aspectRatio: "16/9",
}));

export default function CardNewsPreview({ news, direction }) {
  const theme = useTheme();
  const isXs = useMediaQuery(theme.breakpoints.down("md"));

  // TODO: The layout & logic was already using js for breakpoints, so i went along but if someone has time to refactor it natively, it would be great
  const effectiveDirection = isXs ? "column" : direction;

  return (
    <Link href={`/actualites${news.url}`} style={{ width: "100%", maxWidth: direction === "row" ? "100%" : "400px" }}>
      <Stack borderRadius={theme.spacing(2)} component={Paper} direction={effectiveDirection} height="100%" overflow="hidden">
        <MediaContainer src={news.upload ? FormatUtils.binary(news.upload) : placeholder.src} direction={effectiveDirection} />
        <Stack p={2} gap={1} width={effectiveDirection === "row" ? "50%" : "100%"} height="100%" justifyContent="space-between">
          <Stack gap={1} height="100%">
            <Typography variant="h6" component="h3" color="primary">
              {news.title}
            </Typography>
            <Content>{news.description}</Content>
          </Stack>
          <Typography variant="h6" component="p">{FormatUtils.date(news.createdAt)}</Typography>
        </Stack>
      </Stack>
    </Link>
  );
}

import theme from "@/lib/theme";
import { Box, Paper, Stack, styled, Typography, useMediaQuery, useTheme } from "@mui/material";
import FormatUtils from "@/utils/format.utils";
import Link from "next/link";
import placeholder from "@public/assets/images/placeholder_news.png";

const Content = styled(Typography)(() => ({
  width: "100%",
  overflow: "hidden",
  textOverflow: "ellipsis",
  WebkitLineClamp: 3,
  WebkitBoxOrient: "vertical",
  display: "-webkit-box",
  "> *": {
    overflow: "hidden",
    textOverflow: "ellipsis",
    WebkitLineClamp: 3,
    WebkitBoxOrient: "vertical",
    display: "-webkit-box",
  },
}));

const MediaContainer = styled("img", {
  shouldForwardProp: (prop) => prop !== "url" && prop !== "direction",
})(({ direction }) => ({
  height: direction === "row" ? "100%" : "50%",
  maxHeight: direction === "row" ? "350px" : "250px",
  width: "100%",
  minWidth: direction === "row" ? "40%": "100px",
  objectFit: "contain",
  aspectRatio: "16/10",
}));

export default function CardNews({ news, direction, index }) {
  const theme = useTheme();
  const isXs = useMediaQuery(theme.breakpoints.down("md"));

  // TODO: The layout & logic was already using js for breakpoints, so i went along but if someone has time to refactor it natively, it would be great
  const effectiveDirection = isXs ? "column" : direction;

  return (
    <Stack component={Link} href={`/actualites${news.url}`}>
      <Stack borderRadius={theme.spacing(2)} component={Paper} direction={effectiveDirection} height="100%" overflow="hidden">
        <MediaContainer src={news.upload ? FormatUtils.binary(news.upload) : placeholder.src} direction={effectiveDirection} />
        <Stack
          p={index === 0 && effectiveDirection !== "column" ? 5 : 2}
          gap={1}
          justifyContent="space-between"
          height={effectiveDirection === "column" ? "50%" : "100%"}
        >
          <Stack gap={1}>
            <Typography variant={index === 0 ? "h3" : "h6"} component="h2" color="primary">
              {news.title}
            </Typography>
            <Content variant="body1">{news.description}</Content>
          </Stack>
          <Typography variant="h6" component="p">{FormatUtils.date(news.createdAt)}</Typography>
        </Stack>
      </Stack>
    </Stack>
  );
}

"use client";

import EmailIcon from "@mui/icons-material/Email";
import { Button, Paper, Stack, Typography } from "@mui/material";
import { getPhoneFormat, slugify } from "@/utils/string.utils";
import FormatUtils from "@/utils/format.utils";
import Image from "next/image";
import AccountCircleRoundedIcon from "@mui/icons-material/AccountCircleRounded";
import Link from "next/link";

export default function CardCabinetDevis({ agency, padding = 4 }) {
  const contact = agency?.contact;

  if (!contact) return null;
  return (
    <Stack
      component={Paper}
      p={padding}
      height="100%"
      overflow="auto"
      direction={{ xs: "column", md: "row" }}
      justifyContent="center"
      alignItems="center"
      textAlign="center"
      gap={2}
    >
      {contact?.upload ? (
        <Image
          src={FormatUtils.binary(contact?.upload)}
          height={150}
          width={150}
          alt={contact.name || ""}
          style={{
            objectFit: "contain",
          }}
        />
      ) : (
        <AccountCircleRoundedIcon sx={{ fontSize: 150, color: "grey.light" }} />
      )}
      <Stack>
        <Stack>
          <Typography fontWeight="bold">{contact.name}</Typography>
          <Typography variant="subtitle1" color="primary">
            Diagnostiqueur certifié
          </Typography>
        </Stack>
        <Stack gap={0} alignItems="center">
          <Typography>
            {agency?.location.address1}, {agency?.location.postcode} {agency?.location.city}
          </Typography>
          <Typography component="a" href={`tel:${agency?.contact.phone}`}>
            {getPhoneFormat(agency?.contact.phone)}
          </Typography>
          <Typography
            sx={{
              display: "flex",
              alignItems: "center",
              textAlign: "center",
              gap: 1,
              fontWeight: "bold",
              color: "primary.main",
            }}
            component="a"
            href={`mailto:${contact.email}`}
            target="_blank"
          >
            <EmailIcon color="primary" fontSize="inherit" />
            Contacter par mail
          </Typography>
          <Button
            component={Link}
            href={`/detail-cabinet/${slugify(agency?.name)}/${agency?.location?.postcode?.slice(0, 2)}/${slugify(agency?.location?.city)}/${
              agency?.uuid
            }`}
            sx={{ mt: 1 }}
          >
            Accéder à la fiche franchisé
          </Button>
        </Stack>
      </Stack>
    </Stack>
  );
}

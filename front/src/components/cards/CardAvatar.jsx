"use client";

import PhoneIcon from "@mui/icons-material/Phone";
import EmailIcon from "@mui/icons-material/Email";
import { Paper, Stack, Typography } from "@mui/material";
import Rating from "@/components/inputs/Rating";
import { getPhoneFormat } from "@/utils/string.utils";
import FormatUtils from "@/utils/format.utils";
import Image from "next/image";
import AccountCircleRoundedIcon from "@mui/icons-material/AccountCircleRounded";
import { useApi } from "@/context/ApiProvider";
import { useEffect, useState } from "react";

export default function CardAvatar({ contact, cabinet }) {
  const { get } = useApi();
  const [media, setMedia] = useState(null);

  const getMedia = async () => {
    const media = await get(`/uploads/${contact.upload?.replace("/api/uploads/", "")}`);
    setMedia(media);
  };

  useEffect(() => {
    if (contact.upload) {
      getMedia();
    }
  }, [contact.upload]);

  if (!contact) return null;
  return (
    <Stack component={Paper} p={4} spacing={3} height="100%">
      <Stack justifyContent="center" alignItems="center" textAlign="center" spacing={2}>
        <Typography variant="h5">Contact</Typography>
        {media ? (
          <Image
            src={FormatUtils.binary(media?.["@id"])}
            height={150}
            width={150}
            alt={media?.alt}
            style={{
              objectFit: "contain",
            }}
          />
        ) : (
          <AccountCircleRoundedIcon sx={{ fontSize: 150, color: "grey.light" }} />
        )}

        <Stack>
          <Typography fontWeight="bold">{contact.name}</Typography>
          <Typography variant="subtitle1" color="primary">
            Diagnostiqueur certifié
          </Typography>
        </Stack>
        <Stack gap={1}>
          <Typography
            sx={{
              display: "flex",
              alignItems: "center",
              textAlign: "center",
              gap: 1,
              fontWeight: "bold",
              color: "primary.main",
            }}
            component="a"
            href={`/contact?agency=${cabinet.legacyId}`}
          >
            <EmailIcon color="primary" fontSize="inherit" />
            Contacter par mail
          </Typography>
          <Typography
            sx={{
              display: "flex",
              alignItems: "center",
              textAlign: "center",
              gap: 1,
              fontWeight: "bold",
              color: "primary.main",
            }}
            component="a"
            href={`tel:${contact.phone}`}
            target="_blank"
          >
            <PhoneIcon color="primary" fontSize="inherit" />
            {getPhoneFormat(contact.phone)}
          </Typography>
          <Rating name="simple-controlled" value={5} precision={0.5} readOnly />
        </Stack>
      </Stack>
    </Stack>
  );
}

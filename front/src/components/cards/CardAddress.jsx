"use client";

import { <PERSON>, <PERSON><PERSON>, Chip, Grid2, Link, Paper, Stack, Typography } from "@mui/material";
import ArticleRoundedIcon from "@mui/icons-material/ArticleRounded";
import PhoneRoundedIcon from "@mui/icons-material/PhoneRounded";
import CalendarMonthRoundedIcon from "@mui/icons-material/CalendarMonthRounded";
import LaunchIcon from "@mui/icons-material/Launch";
import ScheduleLine from "../admin/ScheduleLine";
import { useState } from "react";
import { useSettings } from "@/context/SettingsProvider";
import AgencySocials from "../buttons/Socials";

export default function CardAddress({ cabinet }) {
  const { settings } = useSettings();
  const [showPhone, setShowPhone] = useState(false);
  const certifications = cabinet?.certifications?.split(",");

  if (!cabinet) return null;

  return (
    <Stack component={Paper} p={4} height={1} spacing={2}>
      <Grid2 container spacing={2}>
        <Grid2 size={{ xs: 12, sm: 6 }}>
          <Stack spacing={2}>
            <Typography variant="h5">Adresse</Typography>
            <Typography variant="body1">
              {cabinet.location?.address1}, <br />
              <span> France, </span>
              <span>{cabinet.location?.postcode} </span>
              <span>{cabinet.location?.city}</span>
            </Typography>
            <Typography variant="h5">{"Horaires d'ouverture"}</Typography>
            <Stack>
              {cabinet?.schedule
                ? Object.keys(cabinet?.schedule)
                    .filter((e) => "object" === typeof cabinet.schedule[e])
                    .map((e, i) => <ScheduleLine key={i} label={e} schedule={cabinet.schedule[e]} />)
                : null}
            </Stack>
          </Stack>
        </Grid2>
        <Grid2 size={{ xs: 12, sm: 6 }}>
          <Stack spacing={4}>
            <Stack spacing={1}>
              <Box>
                <Button href={settings.quoteLink} color="secondary" variant="contained" startIcon={<ArticleRoundedIcon />}>
                  Demander un devis gratuit
                </Button>
              </Box>
              <Stack width={"fit-content"} gap={1}>
                <Button color="primary" variant="contained" startIcon={<PhoneRoundedIcon />} onClick={() => setShowPhone(!showPhone)}>
                  Nous contacter
                </Button>
                {showPhone && <a href={`tel:${cabinet?.contact?.phone}`}>{cabinet?.contact?.phone}</a>}
              </Stack>
              <Box>
                <Button href={`/contact?agency=${cabinet.legacyId}`} color="primary" variant="contained" startIcon={<CalendarMonthRoundedIcon />}>
                  Prendre rendez-vous
                </Button>
              </Box>
              <Box>
                {cabinet.linkToOpen ? (
                  <Button href={cabinet.linkToOpen} target="_blank" color="primary" variant="contained" startIcon={<LaunchIcon />}>
                    Lien vers la plateforme
                  </Button>
                ) : (
                  <Button disabled={true} startIcon={<LaunchIcon />}>
                    Lien vers la plateforme
                  </Button>
                )}
              </Box>
              <AgencySocials agency={cabinet} />
            </Stack>
          </Stack>
        </Grid2>
      </Grid2>
      <Stack spacing={2}>
        <Typography variant="h5">Certifications</Typography>
        <Stack direction="row" gap={1} my={4} flexWrap="wrap">
          {certifications?.map((c, i) => (
            <Chip
              key={`${c}-${i}`}
              label={
                <Typography fontWeight="bold" variant="body2">
                  {c}
                </Typography>
              }
              color="primary"
            />
          ))}
        </Stack>
      </Stack>
      <Typography variant="subtitle1" my={1}>
        <span>Information: </span>
        <span>
          <Link color="primary.main" href="https://diagnostiqueurs.din.developpement-durable.gouv.fr/index.action">
            Annuaire des diagostiqueurs immobiliers certifiés
          </Link>
        </span>
      </Typography>
    </Stack>
  );
}

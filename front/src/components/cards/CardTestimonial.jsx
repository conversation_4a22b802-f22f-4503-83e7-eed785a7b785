import { IconButton, Stack, Typography } from "@mui/material";
import Image from "next/image";
import theme from "@/lib/theme";
import FormatUtils from "@/utils/format.utils";
import DeleteIcon from "@mui/icons-material/Delete";
import { useApi } from "@/context/ApiProvider";
import { useEffect, useState } from "react";

export default function CardTestimonial({ card, remove }) {
  const { get } = useApi();
  const [media, setMedia] = useState(null);

  const getMedia = async () => {
    const media = await get(`/uploads/${card?.file?.replace("/api/uploads/", "")}`);
    setMedia(media);
  };

  useEffect(() => {
    if (card?.file) {
      getMedia();
    }
  }, [card?.file]);
  return (
    <Stack border={`solid 1px ${theme.palette.grey.light}`} direction={{
      xs: "column",
      md : "row"
    }} gap={4} p={2}>
      <Image src={FormatUtils.binary(card?.file?.["@id"])} alt={card?.file?.alt} height={150} width={150} />
      <Stack gap={1}>
        <Typography variant="bold">{card.title}</Typography>
        <Typography
          variant="body1"
          component="div"
          dangerouslySetInnerHTML={{
            __html: card.text,
          }}
        />
      </Stack>
      {remove ? (
        <Stack>
          <IconButton onClick={remove}>
            <DeleteIcon />
          </IconButton>
        </Stack>
      ) : null}
    </Stack>
  );
}

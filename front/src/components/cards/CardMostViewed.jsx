import FormatUtils from "@/utils/format.utils";
import { IconButton, Stack, styled, Typography } from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";

const StyledCardContainer = styled(Stack, {
  shouldForwardProp: (prop) => prop !== "image",
})(({ theme, image }) => ({
  backgroundImage: `url(${FormatUtils.binary(image)})`,
  backgroundSize: "cover",
  backgroundPosition: "center",
  height: 200,
  width: 200,
  overflow: "hidden",
  borderRadius: theme.spacing(1),
  "&:hover": {
    ">div": {
      opacity: "1",
    },
  },
  position: "relative",
  "& button": {
    top: "5px",
    right: "5px",
    position: "absolute",
  },
}));

const StyledOverlay = styled(Stack)(() => ({
  transition: "opacity 0.5s ease",
  opacity: "0",
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  height: "100%",
  width: "100%",
}));

export default function CardMostViewed({ item, remove }) {
  return (
    <StyledCardContainer image={item.file}>
      {remove ? (
        <IconButton onClick={remove}>
          <DeleteIcon />
        </IconButton>
      ) : null}
      <StyledOverlay justifyContent="center" alignItems="center">
        <Typography color="white" variant="h5">
          {item.text}
        </Typography>
      </StyledOverlay>
    </StyledCardContainer>
  );
}

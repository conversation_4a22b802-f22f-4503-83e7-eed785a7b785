"use client";

import { createTheme } from "@mui/material";
import colors from "./colors";
import { colors as muiColors } from "@mui/material";

export const palette = {
  white: colors.white,
  black: colors.black,
  blue: colors.blue,
  red: colors.red,
  blueGradient: colors.blueGradient,
  redGradient: colors.redGradient,
  primary: {
    main: colors.blue,
    opacity: colors.blueTransparent,
  },
  secondary: {
    main: colors.red,
  },
  grey: {
    main: muiColors.grey[500],
    light: muiColors.grey[200],
    extraLight: "#FAFAFA",
    dark: muiColors.grey[800],
  },
  text: {
    primary: "#262A31",
    secondary: muiColors.grey[600],
    light: muiColors.grey[400],
  },
  yellow: {
    main: muiColors.yellow[600],
  },
  green: {
    main: muiColors.green[600],
  },
};

const spacing = 8;
const shape = {
  borderRadius: 5,
};

const theme = createTheme({
  spacing,
  shape,
  palette,
  typography: {
    fontFamily: "var(--font-montserrat-regular), sans-serif",
    fontWeight: "normal",
    lineHeight: "normal",
    h1: {
      fontFamily: "var(--font-montserrat-bold), sans-serif",
      fontSize: 42,
    },
    h2: {
      fontFamily: "var(--font-montserrat-bold), sans-serif",
      fontSize: 36,
      "@media (max-width:600px)": {
        fontSize: 28,
      },
    },
    h3: {
      fontFamily: "var(--font-montserrat-bold), sans-serif",
      fontSize: 20,
    },
    h6: {
      fontWeight: "bold",
      fontSize: 16,
    },
    body1: {
      fontWeight: "normal",
      fontSize: 14,
    },
    body2: {
      fontWeight: "normal",
      fontSize: 12,
    },
    subtitle1: {
      fontWeight: "normal",
      fontSize: 12,
      color: palette.text.secondary,
    },
    subtitle2: {
      fontWeight: "normal",
      fontSize: 12,
    },
    link: {
      fontWeight: "normal",
      fontSize: 12,
      textDecoration: "underline",
      cursor: "pointer",
    },
    bold: {
      fontFamily: "var(--font-montserrat-bold), sans-serif",
      fontWeight: "bold",
      fontSize: 16,
    },
    disabled: {
      fontWeight: "normal",
      fontSize: 14,
      color: palette.text.secondary,
    },
  },
  components: {
    MuiButton: {
      defaultProps: {
        variant: "contained",
        disableElevation: true,
      },
      styleOverrides: {
        root: {
          borderRadius: 5,
          textTransform: "none",
          boxShadow: "none",
          fontWeight: "bold",
        },
      },
      variants: [
        {
          props: { variant: "outlined" },
          style: {
            backgroundColor: palette.white,
            borderColor: "currentcolor",
          },
        },
        {
          props: { color: "white" },
          style: {
            backgroundColor: palette.white,
            color: palette.text.primary,
            "&:hover": {
              backgroundColor: palette.grey.light,
            },
          },
        },
      ],
    },
    MuiToolbar: {
      defaultProps: {
        disableGutters: true,
      },
    },
    MuiIconButton: {
      styleOverrides: {
        root: {
          padding: 0,
        },
      },
    },
    MuiLink: {
      styleOverrides: {
        root: {
          color: "inherit",
          textDecoration: "none",
        },
      },
    },
    MuiOutlinedInput: {
      styleOverrides: {
        input: {
          padding: "0",
        },
        root: {
          padding: "7px 9px",
        },
      },
      variants: [
        {
          props: { type: "hidden" },
          style: {
            padding: "0",
            fieldset: {
              display: "none",
            },
          },
        },
      ],
    },
    MuiTypography: {
      styleOverrides: {
        root: ({ ownerState }) => ({
          ...(ownerState.component === "div" && {
            "& a": {
              textDecoration: "underline !important",
            },
          }),
        }),
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          "& .MuiOutlinedInput-root": {
            backgroundColor: palette.white,
            "&.Mui-focused": {
              "& .MuiOutlinedInput-notchedOutline": {
                borderColor: "black",
                borderWidth: "1px",
              },
            },
            "&:hover:not(.Mui-focused)": {
              "& .MuiOutlinedInput-notchedOutline": {
                borderColor: "#ccc",
              },
            },
          },
        },
      },
    },
    MuiPaper: {
      defaultProps: {
        elevation: 0,
      },
      styleOverrides: {
        root: {
          "& .Mui-expanded": {
            margin: "0px !important",
            "&:before": {
              opacity: "1 !important",
            },
          },
        },
      },
    },
    MuiFormLabel: {
      styleOverrides: {
        root: {
          color: palette.black,
          fontWeight: "bold",
          width: "100%",
        },
      },
    },
    MuiBreadcrumbs: {
      styleOverrides: {
        root: {
          color: palette.white,
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          boxShadow: "none",
          backgroundColor: palette.red,
        },
      },
    },
    MuiAutocomplete: {
      styleOverrides: {
        inputRoot: {
          padding: 0,
          paddingLeft: 10,
        },
        listbox: {
          padding: 0,
        },
      },
    },
    MuiPopper: {
      styleOverrides: {
        root: {
          padding: 0,
        },
      },
    },
    MuiAccordion: {
      styleOverrides: {
        root: {
          "&:before": {
            content: "none",
          },
        },
      },
    },
    MuiAccordionSummary: {
      styleOverrides: {
        root: {
          "&.Mui-expanded": {
            minHeight: "48px",
          },
        },
      },
    },
    MuiAccordionDetails: {
      styleOverrides: {
        root: {
          border: "1px solid #ccc",
        },
      },
    },
    MuiTableHead: {
      styleOverrides: {
        root: {
          backgroundColor: palette.red,
          "& .MuiTableCell-root": {
            color: palette.white,
            padding: "8px 16px",
          },
          "& .MuiTableRow-root": {
            borderLeft: `1px solid ${palette.red}`,
            borderRight: `1px solid ${palette.red}`,
          },
        },
      },
    },
    MuiTableSortLabel: {
      styleOverrides: {
        root: {
          "&.Mui-active": {
            color: palette.white,
            "& .MuiTableSortLabel-icon": {
              color: palette.white,
            },
          },
        },
      },
    },
    MuiTableBody: {
      styleOverrides: {
        root: {
          background: palette.white,
          "& .MuiTableCell-root": {
            borderRight: "1px solid rgba(224, 224, 224, 1)",
            "&:last-child": {
              borderRight: "none",
            },
          },
        },
      },
    },
    MuiCardContent: {
      styleOverrides: {
        root: {
          "&:last-child": {
            paddingBottom: 16,
          },
        },
      },
    },
    MuiTableRow: {
      styleOverrides: {
        root: {
          borderLeft: "1px solid rgba(224, 224, 224, 1)",
          borderRight: "1px solid rgba(224, 224, 224, 1)",
        },
      },
    },
    MuiList: {
      styleOverrides: {
        root: {
          padding: 0,
          borderRadius: 0,
        },
      },
    },
  },
});

export default theme;

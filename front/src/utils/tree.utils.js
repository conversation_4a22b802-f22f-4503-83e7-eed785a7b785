const populateChildren = (items, parent = null) => {
  parent.children = items
    .filter((e) => (parent?.["uuid"] !== "root" ? e.parent === parent["uuid"] || e.parent === parent?.["@id"] : !e.parent))
    .map((e) => ({ ...e, id: e.uuid }))
    .sort(sortCategories);

  for (const child of parent.children) {
    populateChildren(items, child);
  }
};

export const createTree = (items) => {
  const parents = items
    .filter((e) => !e.parent)
    .map((e) => ({ ...e, id: e.uuid }))
    .sort(sortCategories);

  for (const parent of parents) {
    populateChildren(items, parent);
  }

  return parents;
};


export const removeItem = (items, id) => {
  if (items.find((item) => item.uuid === id)) {
    items = items.filter((item) => item.uuid !== id);
  } else {
    items = items.map((item) => {
      item.children = removeItem(item.children, id);
      return item;
    });
  }
  return items;
};

export const getFlatTree = (tree) => {
  const flat = [];

  const populateFlat = (children) => {
    let i = 0;
    for (const child of children) {
      child.index = i;
      flat.push(child);
      populateFlat(child.children);
      i++;
    }
  };

  populateFlat(tree);

  return flat;
};
const sortCategories = (c1, c2) => {
  if (c1.position < c2.position) return -1;
  if (c1.position > c2.position) return 1;
  return 0;
};

export const addId = (array) =>
  array?.map((e) => ({ ...e, id: e?.uuid, children: addId(e?.children), collapsed: e?.collapsed === false ? false : true })) || [];

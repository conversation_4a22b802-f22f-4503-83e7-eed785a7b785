import { cookies } from "next/headers";
import FormatUtils from "./format.utils";

export async function fetchApi(endPoint, params = {}, options = {}) {
  // Remove first order parameter if multiple exist
  const orderParams = Object.keys(params).filter((key) => key.startsWith("order["));
  if (orderParams.length > 1) {
    delete params[orderParams[0]];
  }
  const url = `${process.env.NEXT_PUBLIC_API_URL}/api/${endPoint}${Object.keys(params).length > 0 ? FormatUtils.formatUrl(params) : ""}`;
  const token = cookies().get("token")?.value;
  const headers = {};

  if (token) {
    headers["X-Sinfin-Token"] = token;
  }

  const response = await fetch(url, {
    ...options,
    headers,
    cache: "no-store",
  });

  if (!response.ok) {
    console.error(response);
  }

  return response.json();
}

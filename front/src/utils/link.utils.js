import { Typography } from "@mui/material";
import Link from "next/link";

export const generatePath = (link) => {
  if (link.url) {
    return link.url;
  }
  if (link.content) {
    return link.content.breadcrumb.slugTrail + link.content.slug + (link?.anchor ? "#" + link.anchor : "");
  }
  return "/";
};

export const generateTag = (link) => {
  if (link?.url || link?.content) {
    return Link;
  }
  return Typography;
};

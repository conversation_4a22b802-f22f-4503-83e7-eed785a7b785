class FormatUtilsWrapper {
  image(url) {
    if (url) {
      return `${process.env.NEXT_PUBLIC_API_URL}${url}/binary`;
    }
  }

  binary(url) {
    if (url) {
      return `${process.env.NEXT_PUBLIC_API_URL}${url}/binary`;
    }
  }

  download(url) {
    if (url) {
      return `${process.env.NEXT_PUBLIC_API_URL}${url}/binary?download=1`;
    }
  }

  spaceOnNumbers(nb) {
    if (!nb) {
      return 0;
    }
    if ("number" === typeof nb) {
      return nb?.toString()?.replace(/\B(?=(\d{3})+(?!\d))/g, " ");
    }

    return nb?.replace(/\B(?=(\d{3})+(?!\d))/g, " ");
  }

  formatUrl(params) {
    if (!params) {
      return "";
    }
    const newParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      newParams.set(key, value);
    });

    return "?" + decodeURIComponent(newParams.toString());
  }

  date(date) {
    return date ? new Date(date).toLocaleDateString("fr-FR") : "-";
  }

  datetime(date) {
    return new Date(date).toLocaleString("fr-FR");
  }

  formatDate(date) {
    let d = new Date(date),
      month = "" + (d.getMonth() + 1),
      day = "" + d.getDate(),
      year = d.getFullYear();

    if (month.length < 2) {
      month = "0" + month;
    }
    if (day.length < 2) {
      day = "0" + day;
    }

    return [day, month, year].join("/");
  }

  formatDateEn(date) {
    let d = new Date(date),
      month = "" + (d.getMonth() + 1),
      day = "" + d.getDate(),
      year = d.getFullYear();

    if (month.length < 2) {
      month = "0" + month;
    }
    if (day.length < 2) {
      day = "0" + day;
    }

    return [year, month, day].join("-");
  }

  formatTime(date) {
    let d = new Date(date),
      hour = "" + d.getHours(),
      minute = "" + d.getMinutes();

    if (hour.length < 2) {
      hour = "0" + hour;
    }
    if (minute.length < 2) {
      minute = "0" + minute;
    }

    return [hour, minute].join(":");
  }
}

const FormatUtils = new FormatUtilsWrapper();

export default FormatUtils;

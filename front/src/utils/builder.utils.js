import { BLOC_FORMS } from "@/enums/BLOC_FORMS";

export const getBlockFieldValue = (field, block, defaultValue = null) => {
  return block?.parameters.find((e) => e.type === field)?.value || defaultValue;
};

export const blockIsEditable = (blockType) => {
  return BLOC_FORMS[blockType] !== null;
};

export const formatContent = (page, infos, stack) => {
  return {
    ...page,
    name: infos.name,
    slug: infos.slug,
    status: infos.status,
    seo: {
      ...page?.seo,
      title: infos.seo_title,
      description: infos.seo_description,
    },
    page: {
      ...page?.page,
      blocks: stack.map((block, i) => ({
        ...block,
        position: i,
      })),
    },
  };
};

export const removeId = (page, position) => {
  return {
    name: page.name + " (2)",
    slug: page.slug + "-2",
    status: page.status,
    position: position,
    seo: {
      title: page.seo.title,
      description: page.seo.description,
    },
    page: {
      blocks: page.page.blocks.map((block) => {
        const obj = { ...block };
        delete obj["@id"];
        delete obj.uuid;
        return obj;
      }),
    },
  };
};

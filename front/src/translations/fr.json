{"account": {"email": "Email", "firstname": "Prénom", "lastname": "Nom", "title": "Mon compte", "update": "Compte mis à jour"}, "agencies": {"_": "Cabinet", "_back": "Revenir aux cabinets", "_plural": "Cabinets", "address": "<PERSON><PERSON><PERSON>", "cantons": "Cantons", "certifications": "Certifications", "city": "Ville", "contactEmail": "<PERSON><PERSON>", "contactImage": "Photo du contact", "contactName": "Nom du contact", "contactPhone": "Téléphone du contact", "description": "Description", "displayAppointment": "Afficher l'encart rendez-vous", "friday": "<PERSON><PERSON><PERSON><PERSON>", "html": "Balise HTML", "image": "Photo du cabinet", "info": "Informations du cabinet", "js": "Balise JS", "list": "Liste des cabinets", "lyraSeller": "<PERSON><PERSON>", "metaDescription": "Meta description", "metaTitle": "Meta title", "monday": "<PERSON><PERSON>", "morning": "<PERSON>in", "name": "Nom", "paymentEnabled": "Activer le paiement en 3 fois sans frais", "posts": {"_": "Actualité", "_back": "Revenir aux informations du cabinet", "_plural": "Actualités (Cabinets)", "add": "Ajouter une actualité", "content": "Contenu", "edit": "Modifier l'actualité", "title": "Titre", "url": "<PERSON><PERSON>"}, "saturday": "<PERSON><PERSON>", "schedule": "<PERSON><PERSON><PERSON>", "sunday": "<PERSON><PERSON><PERSON>", "superAdmin": "Section Super Admin", "thursday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON>", "zip": "Code postal"}, "areas": {"_": "Canton", "_plural": "Cantons"}, "blocks": {"blockCards": {"form": {"title": "Titre", "cards": "<PERSON><PERSON>", "text": "Texte"}}, "blockSurfaceComparision": {"header": {"surface": "Surface", "surfaceHabitable": "Surface habitable (Boutin)", "surfacePrivative": "Surface privative (Carrez)"}, "form": {"title": "<PERSON><PERSON>re du tableau", "rows": "Lignes du tableau", "addRow": "Ajouter une ligne"}}}, "cities": {"_": "<PERSON><PERSON><PERSON>", "_plural": "Communes"}, "contents": {"_": "Contenu", "_plural": "Pages", "_add": "Ajouter une page", "_delete": "Supprimer la page", "continue_without_saving": "Continuer sans enregistrer", "continue_and_save": "Enregistrer et continuer", "lose_title": "Vous allez perdre toutes les modifications non enregistrées.", "lose_description": "Êtes-vous sûr de vouloir quitter cette page ?", "name": "Nom", "slug": "Slug", "seo": "SEO", "seo_title": "Meta title", "seo_description": "Meta description", "blocks": {"search": "Rechercher un bloc", "_": "Blocs", "_favorite": "Blocs partagés"}}, "departments": {"_": "Département", "_plural": "Départements"}, "emails": {"_": "Email", "_plural": "Emails", "type": "Type"}, "global": {"add": "Ajouter", "cancel": "Annuler", "copy": "<PERSON><PERSON><PERSON><PERSON>", "copyConfirmation": "Voulez-vous vraiment dupliquer cet élément ?", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleteConfirmation": "Voulez-vous vraiment supprimer cet élément ?", "edit": "É<PERSON>er", "error": "Une erreur est survenue", "logout": "Se déconnecter", "manage": "<PERSON><PERSON><PERSON>", "name": "Nom", "noResult": "Aucun élément", "openSite": "Ouv<PERSON>r le site", "preview": "<PERSON><PERSON><PERSON><PERSON>", "save": "Enregistrer", "saveInfos": "Enregistrer les informations", "search": "<PERSON><PERSON><PERSON>", "validate": "Valider", "back": "Retour", "download": "Télécharger", "see": "Voir", "modify": "Modifier"}, "inputs": {"upload": {"add": "Ajouter un média"}}, "jobs": {"_": "Offre d'emploi", "_back": "Revenir aux offres d'emploi", "_plural": "Offres d'emploi", "_add": "Ajouter une offre d'emploi", "reference": "Référence"}, "jobApplications": {"_": "Candidature", "_plural": "Candidatures", "_free": "Candidatures spontanées", "firstname": "Prénom", "lastname": "Nom", "address": "<PERSON><PERSON><PERSON>", "zipCode": "Code postal", "city": "Ville", "phone": "Téléphone", "email": "Email", "training": "Formation", "drivingLicense": "Permis de conduire", "professionalExperience": "Expérience professionnelle", "computerLevel": "Niveau informatique", "situation": "Situation", "workplace": "Lieu de travail", "availability": "Disponibilité", "salary": "Salaire", "cv": "CV", "coverLetter": "<PERSON>tre de motivation", "createdAt": "Date de candidature", "reference": "Référence de l'offre"}, "posts": {"_": "Actualité", "_plural": "Actualités"}, "partners": {"_": "Partenaire", "_plural": "Partenaires"}, "franchises": {"_": "Fr<PERSON><PERSON><PERSON><PERSON>", "_plural": "Franchisés"}, "medias": {"_": "Media", "_plural": "Medias"}, "navs": {"builder": "Pages", "footer": "Footer", "header": "Header", "manage": "Gestion", "account": "Espace personnel", "stats": "Statistiques", "myaccount": "Mon compte", "myprices": "<PERSON><PERSON><PERSON> mes tarifs", "mystores": "Mes cabinets", "mydevis": "<PERSON><PERSON> devis", "kiosque": "Accéder au kiosque", "cms": "Superadmin", "settings": "Paramètres"}, "prices": {"house": "<PERSON><PERSON>", "flat": "Appartement", "modify": "Modifier", "activity_type": {"TRANS_AMIANTE": "Amiante - Transaction", "TRANS_ELECTIRICITE": "Électricité - Transaction", "TRANS_ERP": "ERP - Transaction", "LOC_ERP": "ERP - Location", "LOC_ETAT_LIEUX": "État des lieux - Location", "LOC_ETAT_LIEUX_SORTIE": "État des lieux sortant - Location", "TRANS_GAZ": "Gaz - Transaction", "LOC_DECENT": "Logement décent - Location", "TRANS_CARREZ": "<PERSON><PERSON> - Transaction", "LOC_SURFACE": "Surface habitable - Location", "TRANS_PERF_ENERGETIQUE": "Performance énergétique - Transaction", "LOC_PERF_ENERGETIQUE": "Performance énergétique - Location", "TRANS_PLOMB": "Plomb - Transaction", "LOC_PLOMB": "Plomb - Location", "TRANS_TERMITES": "Termites - Transaction", "LOC_ELECTRICITE": "Électricité - Location", "LOC_AMIANTE": "Amiante - Location", "LOC_GAZ": "Gaz - Location", "TRANS_ASSAINISSEMENT": "Assainissement - Transaction", "LOC_ASSAINISSEMENT": "Assainissement - Location", "AUDIT": "Audit énergétique"}, "auditPrice": "Audit énergétique", "erpPrice": "ERP"}, "ROLE_SUPER_ADMIN": "Superadmin", "ROLE_ADMIN": "Administrateur", "users": {"_": "Utilisa<PERSON>ur", "_plural": "Utilisateurs"}, "quote_missions": {"NATURAL_RISK_AND_TECHNICAL": "Risques naturels et techniques", "ENERGETIC_PERFORMANCE": "Performance énergétique", "GLOBAL_TECHNICAL_DIAGNOSTIC": "Diagnostic technique global", "COPRO_MILLIEME": "Millièmes de copropriété", "TECHNICAL_DIAGNOSTIC": "Diagnostic technique", "DIAGNOSTIC_BEFORE_DEMOLITION": "Diagnostics avant travaux ou avant démolition", "DECENT_ACCOMMODATION": "Logement décent", "ELECTRICITY": "Électricité", "LIVING_PLACE": "Surface habitable", "TERMITES": "Termites", "AMIANTE": "Amiante", "PLOMB": "Plomb", "GAZ": "Gaz", "LOI_CARREZ": "<PERSON><PERSON>", "DISABLED_ACCESSIBILITY": "Accessibilité handicapé", "AUTONOMOUS_SANITATION": "Assainissement autonome", "INVENTORY": "État des lieux", "BLACKBERRIES": "<PERSON><PERSON><PERSON>", "ETAT_LIEU_ENTRANT": "État des lieux entrant"}, "estimates": {"_": "<PERSON><PERSON>", "_plural": "<PERSON><PERSON>"}, "pro_estimates": {"_": "<PERSON>s pro", "_plural": "<PERSON>s pro"}, "particulier_estimates": {"_": "<PERSON><PERSON> partic<PERSON>", "_plural": "<PERSON><PERSON> partic<PERSON>"}, "civility": {"MISTER": "<PERSON>", "MRS": "Madame", "MISS": "<PERSON><PERSON><PERSON>", "MASTER": "<PERSON><PERSON><PERSON>", "DOCTOR": "<PERSON><PERSON><PERSON>", "PROFESSOR": "Professeur"}, "activity": {"notaire": "Notaire", "agento_immobilier": "Agent immobilier", "particulier_vendeur": "Particulier vendeur", "particulier_acheteur": "<PERSON><PERSON><PERSON><PERSON> ache<PERSON>", "syndic_de_copropritete": "Syndic de copropriété", "organisme_financier": "Organisme financier", "autre": "<PERSON><PERSON>"}, "situation": {"IN_OFFICE": "En poste", "IN_TRAING": "En formation", "UNEMPLOYED": "Sans emploi"}, "all_emails": "Tous les emails", "big_account": "Grand compte", "contact": "Contact (rendez-vous)", "information": "Information", "job": "Candidature", "pro_estimate": "<PERSON>s pro", "wantedJobs": {"diagnostiqueur": "Diagnostiqueur (H/F)", "assistant": "Assistant(e)", "commercial": "Commercial(e)", "autre": "Autres"}}
import { BLOC_TYPES } from "./BLOC_TYPES";

export const BLOC_LABEL = {
  [BLOC_TYPES.TEXT]: "Texte",
  [BLOC_TYPES.IMAGE_BANNER]: "Bannière avec image",
  [BLOC_TYPES.BANNER]: "Ban<PERSON>ère",
  [BLOC_TYPES.TEXT_MEDIA]: "Texte + média",
  [BLOC_TYPES.BLOCK_DOUBLE_TEXT]: "Double texte",
  [BLOC_TYPES.BLOCK_FILES]: "Fichiers",
  [BLOC_TYPES.BLOCK_CARDS]: "Cartes",
  [BLOC_TYPES.BLOCK_CARD_SUMMARY]: "Cartes résumé",
  [BLOC_TYPES.BLOCK_SURFACE_COMPARISION]: "Surface comparée",
  [BLOC_TYPES.BLOCK_DOUBLE_BLOCK]: "Double bloc",
  [BLOC_TYPES.BLOCK_TESTIMONIALS]: "Témoignages",
  [BLOC_TYPES.BLOCK_QUOTE]: "Bannière devis",
  [BLOC_TYPES.BLOCK_QUOTE_SINGLE]: "Devis",
  [BLOC_TYPES.BLOCK_FIND_MAP]: "Lien vers carte",
  [BLOC_TYPES.BLOCK_CATEGORIES]: "Catégories",
  [BLOC_TYPES.BLOCK_MAP]: "Carte",
  [BLOC_TYPES.BLOCK_NEWS_PREVIEW]: "Présentation actualités",
  [BLOC_TYPES.BLOCK_NEWS]: "Actualités",
  [BLOC_TYPES.BLOCK_FIND_PARTNERS]: "Partenaires",
  [BLOC_TYPES.BLOCK_MOST_VIEWED]: "Les plus vues",
  [BLOC_TYPES.BLOCK_CAROUSEL]: "Carousel",
  [BLOC_TYPES.BLOCK_JOB]: "Offres d'emploi",
  [BLOC_TYPES.BLOCK_DIAGNOSTIC]: "Diagnostic",
  [BLOC_TYPES.BLOCK_REVIEW]: "Avis",

  [BLOC_TYPES.BLOCK_FORM_NEWSLETTER]: "Formulaire général",
  [BLOC_TYPES.BLOCK_PRO_QUOTE]: "Devis pro",
  [BLOC_TYPES.BLOCK_FORM_JOB]: "Formulaire emploi",
  [BLOC_TYPES.BLOCK_FORM_INFOS]: "Formulaire information",
  [BLOC_TYPES.BLOCK_FORM_GRAND_COMPTE]: "Form. grand compte",
  [BLOC_TYPES.BLOCK_NEWSLETTER]: "Formulaire newsletter",
  [BLOC_TYPES.BLOCK_FORM_DIAG_MAG]: "Form. diag. mag.",
  [BLOC_TYPES.BLOCK_FORM_DIAG_ASSIST]: "Form. diag. assist.",
};

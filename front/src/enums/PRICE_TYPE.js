export const PRICE_TYPE = {
  GRID_1: 1,
  GRID_2: 2,
  GRID_3: 3,
};

const getDefaultType1 = (agency, area = null, name = "Grille globale 1", isActive = true) => ({
  agency: agency,
  area: area,
  type: 1,
  gridData: {
    discountType: "percent",
    discountFor2: 0,
    discountFor3: 0,
    discountFor4: 0,
    discountFor5: 0,
    discountFor6: 0,
    discountFor7AndMore: 0,
    amiantePrice: 0,
    appointmentDiscountPercent: 0,
    cgv: null,
    pricings: [],
  },
  name: name,
  isActive: isActive,
  advantageCode: null,
  advantagePercent: 0,
});

const getDefaultType2 = (agency, area = null, name = "Grille globale 2", isActive = true) => ({
  agency: agency,
  area: area,
  type: 2,
  gridData: {
    audit1Room: 0,
    audit2Room: 0,
    audit3Room: 0,
    audit4Room: 0,
    audit5Room: 0,
    audit6Room: 0,
    audit7Room: 0,
    audit8Room: 0,
    basePrice: 0,
    apartmentRatio: 0,
    priceRoom2: 0,
    priceRoom3: 0,
    priceRoom4: 0,
    priceRoom5: 0,
    priceRoom6: 0,
    priceRoom7: 0,
    priceRoom8: 0,
    decrementActivity2: 0,
    decrementActivity3: 0,
    decrementActivity4: 0,
    decrementActivity5: 0,
    decrementActivity6: 0,
    decrementActivity7: 0,
    decrementActivity8: 0,
    crepRatio: 1,
    amiantePrice: 0,
    erpPrice: 0,
    appointmentDiscount: 0,
    cgv: null,
  },
  name: name,
  isActive: isActive,
  advantageCode: null,
  advantagePercent: 0,
});

const getDefaultType3 = (agency, area = null, name = "Grille globale 3", isActive = true) => ({
  agency: agency,
  area: area,
  type: 3,
  gridData: {
    cgv: null,
    activity1: {
      f2: 0,
      f3: 0,
      f4: 0,
      f5: 0,
      f6: 0,
      f7: 0,
      t2: 0,
      t3: 0,
      t4: 0,
      t5: 0,
      t6: 0,
      t7: 0,
    },
    activity2: {
      f2: 0,
      f3: 0,
      f4: 0,
      f5: 0,
      f6: 0,
      f7: 0,
      t2: 0,
      t3: 0,
      t4: 0,
      t5: 0,
      t6: 0,
      t7: 0,
    },
    activity3: {
      f2: 0,
      f3: 0,
      f4: 0,
      f5: 0,
      f6: 0,
      f7: 0,
      t2: 0,
      t3: 0,
      t4: 0,
      t5: 0,
      t6: 0,
      t7: 0,
    },
    activity4: {
      f2: 0,
      f3: 0,
      f4: 0,
      f5: 0,
      f6: 0,
      f7: 0,
      t2: 0,
      t3: 0,
      t4: 0,
      t5: 0,
      t6: 0,
      t7: 0,
    },
    activity5: {
      f2: 0,
      f3: 0,
      f4: 0,
      f5: 0,
      f6: 0,
      f7: 0,
      t2: 0,
      t3: 0,
      t4: 0,
      t5: 0,
      t6: 0,
      t7: 0,
    },
    activity6: {
      f2: 0,
      f3: 0,
      f4: 0,
      f5: 0,
      f6: 0,
      f7: 0,
      t2: 0,
      t3: 0,
      t4: 0,
      t5: 0,
      t6: 0,
      t7: 0,
    },
    activity7: {
      f2: 0,
      f3: 0,
      f4: 0,
      f5: 0,
      f6: 0,
      f7: 0,
      t2: 0,
      t3: 0,
      t4: 0,
      t5: 0,
      t6: 0,
      t7: 0,
    },
    auditPrice: {
      f2: 0,
      f3: 0,
      f4: 0,
      f5: 0,
      f6: 0,
      f7: 0,
      t2: 0,
      t3: 0,
      t4: 0,
      t5: 0,
      t6: 0,
      t7: 0,
    },
    erpPrice: {
      f2: 0,
      f3: 0,
      f4: 0,
      f5: 0,
      f6: 0,
      f7: 0,
      t2: 0,
      t3: 0,
      t4: 0,
      t5: 0,
      t6: 0,
      t7: 0,
    },
    supplementCrep: 0,
    discountCode: null, // TODO: remove
    discountPercent: 0, // TODO: remove
    appointmentDiscount: 0,
  },
  name: name,
  isActive: isActive,
  advantageCode: null,
  advantagePercent: 0,
});

export const DEFAULT_PRICE_DATA = {
  [1]: getDefaultType1,
  [2]: getDefaultType2,
  [3]: getDefaultType3,
};

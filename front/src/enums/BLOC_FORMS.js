import FormBlockCategories from "@/components/forms/formBlocks/FormBlockCategories";
import FormBlockQuote from "@/components/forms/formQuote/FormBlockQuote";
import FormBlocBanner from "@/components/forms/formBlocks/FormBlocBanner";
import FormBlocDoubleText from "@/components/forms/formBlocks/FormBlocDoubleText";
import FormBlocImageBanner from "@/components/forms/formBlocks/FormBlocImageBanner";
import FormBlockCards from "@/components/forms/formBlocks/FormBlockCards";
import FormBlockDoubleBlock from "@/components/forms/formBlocks/FormBlockDoubleBlock";
import FormBlocFiles from "@/components/forms/formBlocks/FormBlockFiles";
import FormBlockSurfaceComparision from "@/components/forms/formBlocks/FormBlockSurfaceComparision";
import FormBlockTestimonials from "@/components/forms/formBlocks/FormBlockTestimonials";
import FormBlockTextMedia from "@/components/forms/formBlocks/FormBlockTextMedia";
import FormBlocFindMap from "@/components/forms/formBlocks/FormBlocFindMap";
import FormBlockNewsPreview from "@/components/forms/formBlocks/FormBlockNewsPreview";
import FormBlockMostViewed from "@/components/forms/formBlocks/FormBlockMostViewed";
import FormBlockCarousel from "@/components/forms/formBlocks/FormBlockCarousel";
import FormBlocText from "../components/forms/formBlocks/FormBlockText";
import { BLOC_TYPES } from "./BLOC_TYPES";
import FormEmpty from "@/components/forms/formBlocks/FormEmpty";
import FormBlockCardSummary from "@/components/forms/formBlocks/FormBlockCardSummary";
import FormBlockQuoteSingle from "@/components/forms/formBlocks/FormBlockQuoteSingle";
import FormBlockDiagnostic from "@/components/forms/formBlocks/FormBlockDiagnostic";
import FormBlockReviews from "@/components/forms/formBlocks/FormBlockReviews";
import FormBlockForm from "@/components/forms/formBlocks/FormBlockForm";

export const BLOC_FORMS = {
  [BLOC_TYPES.TEXT]: FormBlocText,
  [BLOC_TYPES.IMAGE_BANNER]: FormBlocImageBanner,
  [BLOC_TYPES.BANNER]: FormBlocBanner,
  [BLOC_TYPES.TEXT_MEDIA]: FormBlockTextMedia,
  [BLOC_TYPES.BLOCK_DOUBLE_TEXT]: FormBlocDoubleText,
  [BLOC_TYPES.BLOCK_FILES]: FormBlocFiles,
  [BLOC_TYPES.BLOCK_CARD_SUMMARY]: FormBlockCardSummary,
  [BLOC_TYPES.BLOCK_CARDS]: FormBlockCards,
  [BLOC_TYPES.BLOCK_SURFACE_COMPARISION]: FormBlockSurfaceComparision,
  [BLOC_TYPES.BLOCK_DOUBLE_BLOCK]: FormBlockDoubleBlock,
  [BLOC_TYPES.BLOCK_TESTIMONIALS]: FormBlockTestimonials,
  [BLOC_TYPES.BLOCK_QUOTE]: FormBlockQuote,
  [BLOC_TYPES.BLOCK_QUOTE_SINGLE]: FormBlockQuoteSingle,
  [BLOC_TYPES.BLOCK_FIND_MAP]: FormBlocFindMap,
  [BLOC_TYPES.BLOCK_CATEGORIES]: FormBlockCategories,
  [BLOC_TYPES.BLOCK_MAP]: FormEmpty,
  [BLOC_TYPES.BLOCK_NEWS_PREVIEW]: FormBlockNewsPreview,
  [BLOC_TYPES.BLOCK_NEWS]: FormEmpty,
  [BLOC_TYPES.BLOCK_FIND_PARTNERS]: FormEmpty,
  [BLOC_TYPES.BLOCK_MOST_VIEWED]: FormBlockMostViewed,
  [BLOC_TYPES.BLOCK_CAROUSEL]: FormBlockCarousel,
  [BLOC_TYPES.BLOCK_JOB]: FormEmpty,
  [BLOC_TYPES.BLOCK_DIAGNOSTIC]: FormBlockDiagnostic,
  [BLOC_TYPES.BLOCK_REVIEW]: FormBlockReviews,

  [BLOC_TYPES.BLOCK_FORM_NEWSLETTER]: FormBlockForm,
  [BLOC_TYPES.BLOCK_PRO_QUOTE]: FormBlockForm,
  [BLOC_TYPES.BLOCK_FORM_JOB]: FormBlockForm,
  [BLOC_TYPES.BLOCK_FORM_INFOS]: FormBlockForm,
  [BLOC_TYPES.BLOCK_FORM_GRAND_COMPTE]: FormBlockForm,
  [BLOC_TYPES.BLOCK_NEWSLETTER]: FormBlockForm,
  [BLOC_TYPES.BLOCK_FORM_DIAG_MAG]: FormBlockForm,
  [BLOC_TYPES.BLOCK_FORM_DIAG_ASSIST]: FormBlockForm,
};

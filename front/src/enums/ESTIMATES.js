export const ESTIMATE_TYPE = {
  AUDIT: "audit",
  RENT: "rent",
  SELL: "sell",
};

export const PROPERTY_TYPE = {
  APARTMENT: "apartment",
  CO_OWNED_HOUSE: "co_owned_house",
  DETACHED_HOUSE: "detached_house",
  OTHER: "other",
};

const BUILD_YEAR = {
  PRE_1949: "pre_1949",
  BETWEEN_1949_1997: "between_1949_1997",
  PAST_1997: "past_1997",
};

export const ROOM_NUMBER = {
  ROOM_1: 1,
  ROOM_2: 2,
  ROOM_3: 3,
  ROOM_4: 4,
  ROOM_5: 5,
  ROOM_6: 6,
  ROOM_7_OR_MORE: 7,
};

export const OPPORTUNITY_WANTED = {
  AMIANTE: "amiante",
  CARREZ: "carrez",
  DECENT: "decent",
  DPE: "dpe",
  ELECTRECITE: "electricite",
  ERP: "erp",
  ETAT_LIEUX: "etatLieux",
  GAZ: "gaz",
  MESURAGE: "mesurage",
  PLOMB: "plomb",
  TERMITES: "termites",
};

export const OPPORTUNITY_WANTED_SELL = {
  AMIANTE: "amiante",
  CARREZ: "carrez",
  DPE: "dpe",
  ELECTRECITE: "electricite",
  ERP: "erp",
  ETAT_LIEUX: "etatLieux",
  GAZ: "gaz",
  MESURAGE: "mesurage",
  PLOMB: "plomb",
  TERMITES: "termites",
};

export const OPPORTUNITY_WANTED_AUDIT = {
  AUDIT: "audit",
};

export const ESTIMATE_TYPE_TRANSLATED = {
  [ESTIMATE_TYPE.AUDIT]: "Audit",
  [ESTIMATE_TYPE.RENT]: "Louer",
  [ESTIMATE_TYPE.SELL]: "Vendre",
};

export const PROPERTY_TYPE_TRANSLATED = {
  [PROPERTY_TYPE.APARTMENT]: "Appartement",
  [PROPERTY_TYPE.CO_OWNED_HOUSE]: "Maison en copropriété",
  [PROPERTY_TYPE.DETACHED_HOUSE]: "Maison",
  [PROPERTY_TYPE.OTHER]: "Autre",
};

export const BUILD_YEAR_TRANSLATED = {
  [BUILD_YEAR.PRE_1949]: "Avant 1949",
  [BUILD_YEAR.BETWEEN_1949_1997]: "Entre 1949 et 1997",
  [BUILD_YEAR.PAST_1997]: "Après 1997",
};

export const OPPORTUNITY_WANTED_TRANSLATED = {
  [OPPORTUNITY_WANTED.AMIANTE]: "Amiante",
  [OPPORTUNITY_WANTED_AUDIT.AUDIT]: "Audit",
  [OPPORTUNITY_WANTED.CARREZ]: "Carrez",
  [OPPORTUNITY_WANTED.DECENT]: "Logement décent",
  [OPPORTUNITY_WANTED.DPE]: "DPE",
  [OPPORTUNITY_WANTED.ELECTRECITE]: "Électricité",
  [OPPORTUNITY_WANTED.ERP]: "ERP",
  [OPPORTUNITY_WANTED.ETAT_LIEUX]: "État des lieux",
  [OPPORTUNITY_WANTED.GAZ]: "Gaz",
  [OPPORTUNITY_WANTED.MESURAGE]: "Mesurage",
  [OPPORTUNITY_WANTED.PLOMB]: "Plomb",
  [OPPORTUNITY_WANTED.TERMITES]: "Termites",
};

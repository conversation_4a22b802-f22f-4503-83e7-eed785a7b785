import { BLOC_TYPES } from "./BLOC_TYPES";
import blockTextPicto from "@public/assets/block-pictos/picto-text.svg";
import blockTextMediaPicto from "@public/assets/block-pictos/picto-texte-media.svg";
import blockBannerPicto from "@public/assets/block-pictos/picto-banniere.svg";
import blockImageBannerPicto from "@public/assets/block-pictos/picto-banniere-image.svg";
import blockDoubleTextPicto from "@public/assets/block-pictos/picto-double-texte.svg";
import blockFilePicto from "@public/assets/block-pictos/picto-fichiers.svg";
import blockCardsPicto from "@public/assets/block-pictos/picto-cartes.svg";
import blockNewsletterPicto from "@public/assets/block-pictos/picto-newsletter.svg";
import blockSurfaceComparisionPicto from "@public/assets/block-pictos/picto-surface-comparee.svg";
import blockDoubleBlockPicto from "@public/assets/block-pictos/picto-double-bloc.svg";
import blockTestimonialsPicto from "@public/assets/block-pictos/picto-avis.svg";
import blockCarouselPicto from "@public/assets/block-pictos/picto-caroussel.svg";
import blockMostViewedPicto from "@public/assets/block-pictos/picto-les-plus-vues.svg";
import blockPartnersPicto from "@public/assets/block-pictos/picto-partenaires.svg";
import blockNewsPicto from "@public/assets/block-pictos/picto-actualites.svg";
import blockQuotePicto from "@public/assets/block-pictos/picto-citation.svg";
import blockCategoriesPicto from "@public/assets/block-pictos/picto-categories.svg";
import blockMapPicto from "@public/assets/block-pictos/picto-carte.svg";
import blockNewsPreviewPicto from "@public/assets/block-pictos/picto-presentation-actualites.svg";
import blockJobPicto from "@public/assets/block-pictos/picto-job.svg";
import blockCardSummaryPicto from "@public/assets/block-pictos/picto-card-summary.svg";
import blockDiagnosticPicto from "@public/assets/block-pictos/pictos-diagnostic.svg";
import blockReviewPicto from "@public/assets/block-pictos/picto-reviews.svg";
import blockInfosPicto from "@public/assets/block-pictos/picto-infos.svg";

export const BLOC_ICONS = {
  [BLOC_TYPES.TEXT]: blockTextPicto,
  [BLOC_TYPES.IMAGE_BANNER]: blockImageBannerPicto,
  [BLOC_TYPES.BANNER]: blockBannerPicto,
  [BLOC_TYPES.TEXT_MEDIA]: blockTextMediaPicto,
  [BLOC_TYPES.BLOCK_DOUBLE_TEXT]: blockDoubleTextPicto,
  [BLOC_TYPES.BLOCK_FILES]: blockFilePicto,
  [BLOC_TYPES.BLOCK_CARDS]: blockCardsPicto,
  [BLOC_TYPES.BLOCK_CARD_SUMMARY]: blockCardSummaryPicto,
  [BLOC_TYPES.BLOCK_SURFACE_COMPARISION]: blockSurfaceComparisionPicto,
  [BLOC_TYPES.BLOCK_DOUBLE_BLOCK]: blockDoubleBlockPicto,
  [BLOC_TYPES.BLOCK_TESTIMONIALS]: blockTestimonialsPicto,
  [BLOC_TYPES.BLOCK_QUOTE]: blockQuotePicto,
  [BLOC_TYPES.BLOCK_QUOTE_SINGLE]: blockQuotePicto,
  [BLOC_TYPES.BLOCK_FIND_MAP]: blockMapPicto,
  [BLOC_TYPES.BLOCK_CATEGORIES]: blockCategoriesPicto,
  [BLOC_TYPES.BLOCK_MAP]: blockMapPicto,
  [BLOC_TYPES.BLOCK_NEWS_PREVIEW]: blockNewsPreviewPicto,
  [BLOC_TYPES.BLOCK_NEWS]: blockNewsPicto,
  [BLOC_TYPES.BLOCK_FIND_PARTNERS]: blockPartnersPicto,
  [BLOC_TYPES.BLOCK_MOST_VIEWED]: blockMostViewedPicto,
  [BLOC_TYPES.BLOCK_CAROUSEL]: blockCarouselPicto,
  [BLOC_TYPES.BLOCK_JOB]: blockJobPicto,
  [BLOC_TYPES.BLOCK_DIAGNOSTIC]: blockDiagnosticPicto,
  [BLOC_TYPES.BLOCK_REVIEW]: blockReviewPicto,

  [BLOC_TYPES.BLOCK_FORM_NEWSLETTER]: blockNewsletterPicto,
  [BLOC_TYPES.BLOCK_PRO_QUOTE]: blockQuotePicto,
  [BLOC_TYPES.BLOCK_FORM_JOB]: blockJobPicto,
  [BLOC_TYPES.BLOCK_FORM_INFOS]: blockInfosPicto,
  [BLOC_TYPES.BLOCK_FORM_GRAND_COMPTE]: blockQuotePicto,
  [BLOC_TYPES.BLOCK_NEWSLETTER]: blockNewsletterPicto,
  [BLOC_TYPES.BLOCK_FORM_DIAG_MAG]: blockQuotePicto,
  [BLOC_TYPES.BLOCK_FORM_DIAG_ASSIST]: blockQuotePicto,
};

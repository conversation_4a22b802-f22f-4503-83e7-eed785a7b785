const LIST_OPTIONS = {
  COMMUNES: [
    { label: "Nom", variable: "order[name]", property: "name" },
    { label: "Code postal", variable: "order[zip]", property: "zip" },
    { label: "Canton", variable: "order[area.name]", property: "area" },
    { label: "Termites", property: "hasTermite" },
  ],
  CANTONS: [
    { label: "Nom", variable: "order[name]", property: "name" },
    {
      label: "Cabinet",
      variable: "order[agency.name]",
      property: "agency",
    },
    {
      label: "Département",
      variable: "order[department.name]",
      property: "department",
    },
  ],
  CABINET: [
    { label: "Nom", variable: "order[name]", property: "name" },
    {
      label: "Ville",
      variable: "order[location.city]",
      property: ["location", "city"],
    },
    {
      label: "Code postal",
      variable: "order[location.postcode]",
      property: ["location", "postcode"],
    },
    {
      label: "Nom du contact",
      variable: "order[contact.name]",
      property: ["contact", "name"],
    },
  ],
  AGENCIES_ACTUS: [
    { label: "Titre", variable: "order[title]", property: "title" },
    { label: "Slug", property: "url" },
    {
      label: "Cabinet",
      variable: "order[agency.name]",
      property: "agency",
    },
    {
      label: "Date de création",
      variable: "order[createdAt]",
      property: "createdAt",
    },
  ],
  ACTUS: [
    { label: "Titre", variable: "order[title]", property: "title" },
    { label: "Slug", property: "url" },
    {
      label: "Date de création",
      variable: "order[createdAt]",
      property: "createdAt",
    },
  ],
  DEPARTMENTS: [
    { label: "Nom", variable: "order[name]", property: "name" },
    { label: "Code postal", variable: "order[code]", property: "code" },
  ],
  USERS: [
    { label: "Nom", variable: "order[lastname]", property: "lastname" },
    {
      label: "Prénom",
      variable: "order[firstname]",
      property: "firstname",
    },
    { label: "Email", variable: "order[email]", property: "email" },
    { label: "Rôle", property: "role" },
  ],
  JOB_OFFERS: [
    { label: "Titre", variable: "order[title]", property: "title" },
    { label: "Description", property: "description" },
    { label: "Lieu", property: "location", variable: "order[location]" },
    { label: "Réference", property: "reference", variable: "order[reference]" },
    { label: "Poste", property: "jobName", variable: "order[jobName]" },
    { label: "Agence", property: "agency" },
    { label: "Date de publication", variable: "order[publicationDate]", property: "publicationDate" },
    { label: "Publié", property: "status" },
  ],
  JOB_APPLICATIONS: [
    { label: "Nom", property: "lastname", variable: "order[lastname]" },
    { label: "Prénom", property: "firstname", variable: "order[firstname]" },
    { label: "Email", property: "email", variable: "order[email]" },
    { label: "Téléphone", property: "phone", variable: "order[phone]" },
    { label: "Adresse", property: "address", variable: "order[address]" },
    { label: "Code postal", property: "zipCode", variable: "order[zipCode]" },
    { label: "Ville", property: "city", variable: "order[city]" },
    { label: "Envoyé le", variable: "order[createdAt]", property: "createdAt" },
  ],
  PARTNERS: [
    { label: "Image", property: "upload" },
    { label: "Nom", variable: "order[name]", property: "name" },
    { label: "Activité", variable: "order[activity]", property: "activity" },
    { label: "Slug", variable: "order[link]", property: "link" },
    { label: "Département", variable: "order[department.name]", property: "department", emptyValue: "France entière" },
    { label: "Date de création", variable: "order[createdAt]", property: "createdAt" },
  ],
  EMAILS: [
    { label: "Utilisateur", property: "email", variable: "order[email]" },
    { label: "Type", property: "code" },
    { label: "Date de création", variable: "order[createdAt]", property: "createdAt" },
  ],
  MEDIAS: [
    { label: "Image", property: "@id" },
    { label: "Nom", property: "name" },
    { label: "Lien", property: "@id", action: "download" },
    { label: "Type", property: "type" },
    { label: "Poids", property: "size" },
    { label: "Date de création", property: "createdAt" },
  ],
  PRO_ESTIMATES: [
    { label: "Nom", property: "lastname" },
    { label: "Prénom", property: "firstname" },
    { label: "Email", property: "email" },
    { label: "Date de création", variable: "order[createdAt]", property: "createdAt" },
  ],
  ESTIMATES: [
    { label: "Nom", property: "customerLastname" },
    { label: "Prénom", property: "customerFirstname" },
    { label: "Email", property: "customerEmail", variable: "order[customerEmail]" },
    { label: "Téléphone", property: "customerPhone", variable: "order[customerPhone]" },
    { label: "Agence", property: ["agency", "name"] },
    { label: "Date de création", variable: "order[createdAt]", property: "createdAt" },
  ],
};

export default LIST_OPTIONS;

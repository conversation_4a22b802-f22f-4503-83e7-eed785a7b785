{"name": "website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "knip": "knip"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@mapbox/mapbox-gl-geocoder": "^5.0.3", "@mui/icons-material": "^6.1.4", "@mui/material": "^6.1.4", "@mui/x-charts": "^7.21.0", "@next/third-parties": "^15.0.3", "@tiptap/extension-color": "^2.8.0", "@tiptap/extension-hard-break": "^2.10.4", "@tiptap/extension-image": "^2.8.0", "@tiptap/extension-link": "^2.8.0", "@tiptap/extension-placeholder": "^2.8.0", "@tiptap/extension-superscript": "^2.10.3", "@tiptap/extension-text-align": "^2.8.0", "@tiptap/extension-text-style": "^2.8.0", "@tiptap/extension-underline": "^2.8.0", "@tiptap/pm": "^2.8.0", "@tiptap/react": "^2.8.0", "@tiptap/starter-kit": "^2.8.0", "cookies-next": "^4.3.0", "dnd-kit-sortable-tree": "^0.1.73", "mapbox-gl": "^3.7.0", "next": "14.2.25", "next-navigation-guard": "^0.1.1", "react": "^18", "react-dom": "^18", "react-dropzone": "^14.3.5", "swiper": "^11.1.14", "uuid": "^10.0.0"}, "devDependencies": {"@types/node": "^22.8.1", "eslint": "^8", "eslint-config-next": "14.2.15", "knip": "^5.34.0", "typescript": "^5.6.3"}}
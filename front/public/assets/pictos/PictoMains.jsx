export default function PictoMains({
  fill = "black",
  width = 25,
  height = 25,
}) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      fill={fill}
      viewBox="0 0 41.086 37.334"
    >
      <path
        id="handshake_24dp_E8EAED_FILL0_wght400_GRAD0_opsz24"
        d="M59.919-846.4a.829.829,0,0,0,.373-.093,1.087,1.087,0,0,0,.28-.187l15.307-15.307a3.449,3.449,0,0,0,.817-1.26,4.043,4.043,0,0,0,.257-1.4,3.979,3.979,0,0,0-.257-1.423,3.083,3.083,0,0,0-.817-1.19l-7.933-7.933a3.082,3.082,0,0,0-1.19-.817,3.979,3.979,0,0,0-1.423-.257,4.042,4.042,0,0,0-1.4.257,3.448,3.448,0,0,0-1.26.817l-.513.513,3.453,3.5a4.024,4.024,0,0,1,1.027,1.493,4.849,4.849,0,0,1,.327,1.773,4.469,4.469,0,0,1-1.33,3.29,4.469,4.469,0,0,1-3.29,1.33,5.035,5.035,0,0,1-1.8-.327,4.248,4.248,0,0,1-1.517-.98l-3.5-3.453-8.167,8.167a.968.968,0,0,0-.21.3.879.879,0,0,0-.07.35.974.974,0,0,0,.28.677.864.864,0,0,0,.653.3.828.828,0,0,0,.373-.093,1.084,1.084,0,0,0,.28-.187l6.347-6.347,2.613,2.613-6.3,6.347a.969.969,0,0,0-.21.3.879.879,0,0,0-.07.35.9.9,0,0,0,.28.653.9.9,0,0,0,.653.28.828.828,0,0,0,.373-.093,1.086,1.086,0,0,0,.28-.187l6.347-6.3L61.6-858.3l-6.3,6.347a.559.559,0,0,0-.21.28,1.056,1.056,0,0,0-.07.373.9.9,0,0,0,.28.653.9.9,0,0,0,.653.28.879.879,0,0,0,.35-.07.969.969,0,0,0,.3-.21l6.347-6.3,2.613,2.613-6.347,6.347a.97.97,0,0,0-.21.3.879.879,0,0,0-.07.35.864.864,0,0,0,.3.653A.974.974,0,0,0,59.919-846.4Zm-.047,3.733a4.553,4.553,0,0,1-3.057-1.143,4.455,4.455,0,0,1-1.563-2.87,4.6,4.6,0,0,1-2.66-1.307,4.6,4.6,0,0,1-1.307-2.66,4.432,4.432,0,0,1-2.637-1.33,4.747,4.747,0,0,1-1.283-2.637,4.438,4.438,0,0,1-2.893-1.54,4.582,4.582,0,0,1-1.12-3.08,4.738,4.738,0,0,1,.35-1.8,4.53,4.53,0,0,1,1-1.517l10.827-10.78,6.113,6.113a.56.56,0,0,0,.28.21,1.058,1.058,0,0,0,.373.07,1,1,0,0,0,.7-.257.874.874,0,0,0,.28-.677,1.057,1.057,0,0,0-.07-.373.56.56,0,0,0-.21-.28l-6.673-6.673a3.082,3.082,0,0,0-1.19-.817,3.979,3.979,0,0,0-1.423-.257,4.042,4.042,0,0,0-1.4.257,3.448,3.448,0,0,0-1.26.817l-6.58,6.627a3.737,3.737,0,0,0-.7.98,3.955,3.955,0,0,0-.373,1.12,3.525,3.525,0,0,0,0,1.143,3.393,3.393,0,0,0,.373,1.1l-2.707,2.707a7.422,7.422,0,0,1-1.167-2.357,7.333,7.333,0,0,1-.28-2.59,7.4,7.4,0,0,1,.653-2.543,7.475,7.475,0,0,1,1.54-2.217l6.58-6.58a7.876,7.876,0,0,1,2.5-1.633,7.429,7.429,0,0,1,2.823-.56,7.429,7.429,0,0,1,2.823.56,7.355,7.355,0,0,1,2.45,1.633l.513.513.513-.513a7.876,7.876,0,0,1,2.5-1.633,7.429,7.429,0,0,1,2.823-.56,7.429,7.429,0,0,1,2.823.56,7.355,7.355,0,0,1,2.45,1.633l7.887,7.887a7.3,7.3,0,0,1,1.633,2.473,7.6,7.6,0,0,1,.56,2.847,7.429,7.429,0,0,1-.56,2.823,7.356,7.356,0,0,1-1.633,2.45l-15.307,15.26a4.842,4.842,0,0,1-1.517,1.027A4.481,4.481,0,0,1,59.872-842.666ZM55.252-868.8Z"
        transform="translate(-39.6 880)"
      />
    </svg>
  );
}

module.exports = {
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
    NEXT_PUBLIC_MAP_API: process.env.NEXT_PUBLIC_MAP_API,
  },
  images: {
    dangerouslyAllowSVG: true,
    remotePatterns: [
      {
        protocol: "https",
        hostname: "www.agendadiagnostics.fr",
        pathname: "/api/uploads/**",
      },{
        protocol: "http",
        hostname: "127.0.0.1",
        pathname: "/api/uploads/**",
      },
    ],
  },
};

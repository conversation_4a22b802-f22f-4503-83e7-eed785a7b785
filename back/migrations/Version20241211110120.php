<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241211110120 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_footer ADD anchor VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE crm_footer_link ADD anchor VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE crm_footer_nav ADD anchor VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE crm_footer_social ADD anchor VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE crm_header ADD anchor VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE crm_header_link ADD anchor VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE crm_header_nav ADD anchor VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_footer DROP anchor');
        $this->addSql('ALTER TABLE crm_footer_link DROP anchor');
        $this->addSql('ALTER TABLE crm_footer_nav DROP anchor');
        $this->addSql('ALTER TABLE crm_footer_social DROP anchor');
        $this->addSql('ALTER TABLE crm_header DROP anchor');
        $this->addSql('ALTER TABLE crm_header_link DROP anchor');
        $this->addSql('ALTER TABLE crm_header_nav DROP anchor');
    }
}

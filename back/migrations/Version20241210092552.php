<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241210092552 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_user ADD upload_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE crm_user ADD CONSTRAINT FK_E24FF4F855FBD7DF FOREIGN KEY (upload_uuid) REFERENCES crm_upload (uuid) ON DELETE SET NULL');
        $this->addSql('CREATE INDEX IDX_E24FF4F855FBD7DF ON crm_user (upload_uuid)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_user DROP FOREIGN KEY FK_E24FF4F855FBD7DF');
        $this->addSql('DROP INDEX IDX_E24FF4F855FBD7DF ON crm_user');
        $this->addSql('ALTER TABLE crm_user DROP upload_uuid');
    }
}

<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241223135722 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE est_estimate CHANGE appointment_date appointment_date DATETIME DEFAULT NULL, CHANGE discount_price appointment_price DOUBLE PRECISION DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE est_estimate CHANGE appointment_date appointment_date VARCHAR(200) DEFAULT NULL, CHANGE appointment_price discount_price DOUBLE PRECISION DEFAULT NULL');
    }
}

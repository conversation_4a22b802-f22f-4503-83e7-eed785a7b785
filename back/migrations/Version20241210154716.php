<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241210154716 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE crm_post (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', upload_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', title VARCHAR(255) NOT NULL, long_title VARCHAR(255) NOT NULL, description VARCHAR(255) NOT NULL, content LONGTEXT NOT NULL, url VARCHAR(255) NOT NULL, is_published TINYINT(1) NOT NULL, created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP, seo_title VARCHAR(255) DEFAULT NULL, seo_description VARCHAR(1000) DEFAULT NULL, INDEX IDX_35564E3C55FBD7DF (upload_uuid), PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE crm_post ADD CONSTRAINT FK_35564E3C55FBD7DF FOREIGN KEY (upload_uuid) REFERENCES crm_upload (uuid)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_post DROP FOREIGN KEY FK_35564E3C55FBD7DF');
        $this->addSql('DROP TABLE crm_post');
    }
}

<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241105093452 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_agency ADD enable_payment_three_time_no_fee TINYINT(1) DEFAULT 0 NOT NULL, CHANGE display_appointment display_appointment TINYINT(1) DEFAULT 0 NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_agency DROP enable_payment_three_time_no_fee, CHANGE display_appointment display_appointment TINYINT(1) NOT NULL');
    }
}

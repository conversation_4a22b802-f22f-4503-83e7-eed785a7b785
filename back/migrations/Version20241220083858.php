<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241220083858 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE est_estimate CHANGE price price DOUBLE PRECISION DEFAULT NULL, CHANGE discount_price discount_price DOUBLE PRECISION DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE est_estimate CHANGE price price DOUBLE PRECISION NOT NULL, CHANGE discount_price discount_price DOUBLE PRECISION NOT NULL');
    }
}

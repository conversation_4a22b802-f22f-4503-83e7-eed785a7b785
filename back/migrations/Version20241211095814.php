<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241211095814 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_agency_post DROP FOREIGN KEY FK_AEABE5A155FBD7DF');
        $this->addSql('ALTER TABLE crm_agency_post CHANGE agency_uuid agency_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', CHANGE upload_uuid upload_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE crm_agency_post ADD CONSTRAINT FK_AEABE5A155FBD7DF FOREIGN KEY (upload_uuid) REFERENCES crm_upload (uuid) ON DELETE SET NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_agency_post DROP FOREIGN KEY FK_AEABE5A155FBD7DF');
        $this->addSql('ALTER TABLE crm_agency_post CHANGE agency_uuid agency_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\', CHANGE upload_uuid upload_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE crm_agency_post ADD CONSTRAINT FK_AEABE5A155FBD7DF FOREIGN KEY (upload_uuid) REFERENCES crm_upload (uuid) ON UPDATE NO ACTION ON DELETE NO ACTION');
    }
}

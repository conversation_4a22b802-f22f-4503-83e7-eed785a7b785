<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241217134505 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE est_estimate ADD agency_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE est_estimate ADD CONSTRAINT FK_4965DBAEC6784CAE FOREIGN KEY (agency_uuid) REFERENCES crm_agency (uuid) ON DELETE SET NULL');
        $this->addSql('CREATE INDEX IDX_4965DBAEC6784CAE ON est_estimate (agency_uuid)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE est_estimate DROP FOREIGN KEY FK_4965DBAEC6784CAE');
        $this->addSql('DROP INDEX IDX_4965DBAEC6784CAE ON est_estimate');
        $this->addSql('ALTER TABLE est_estimate DROP agency_uuid');
    }
}

<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241105114006 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_agency ADD contact_upload_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE crm_agency ADD CONSTRAINT FK_587E77273B576861 FOREIGN KEY (contact_upload_uuid) REFERENCES crm_upload (uuid) ON DELETE SET NULL');
        $this->addSql('CREATE INDEX IDX_587E77273B576861 ON crm_agency (contact_upload_uuid)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_agency DROP FOREIGN KEY FK_587E77273B576861');
        $this->addSql('DROP INDEX IDX_587E77273B576861 ON crm_agency');
        $this->addSql('ALTER TABLE crm_agency DROP contact_upload_uuid');
    }
}

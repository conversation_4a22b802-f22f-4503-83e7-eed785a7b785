<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250102080830 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_mailer_type DROP FOREIGN KEY FK_D2FD0D15ABFE1C6F');
        $this->addSql('DROP INDEX IDX_D2FD0D15ABFE1C6F ON crm_mailer_type');
        $this->addSql('ALTER TABLE crm_mailer_type ADD email VARCHAR(255) NOT NULL');

        $query = <<<SQL
        SELECT cmt.uuid, cu.email 
        FROM crm_mailer_type cmt
        INNER JOIN crm_user cu ON cmt.user_uuid = cu.uuid
        SQL;

        $currents = $this->connection->fetchAllAssociative($query);

        foreach ($currents as $current) {
            $this->addSql('UPDATE crm_mailer_type SET email = ? WHERE uuid = ?', [$current['email'], $current['uuid']]);
        }

        $this->addSql('ALTER TABLE crm_mailer_type DROP user_uuid');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_mailer_type ADD user_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\', DROP email');
        $this->addSql('ALTER TABLE crm_mailer_type ADD CONSTRAINT FK_D2FD0D15ABFE1C6F FOREIGN KEY (user_uuid) REFERENCES crm_user (uuid) ON UPDATE NO ACTION ON DELETE CASCADE');
        $this->addSql('CREATE INDEX IDX_D2FD0D15ABFE1C6F ON crm_mailer_type (user_uuid)');
    }
}

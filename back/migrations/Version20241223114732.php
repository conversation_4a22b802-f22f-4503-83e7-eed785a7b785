<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Symfony\Component\Uid\Uuid;

final class Version20241223114732 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_agency ADD lyra_seller_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE crm_agency ADD CONSTRAINT FK_587E77272C7C4D3D FOREIGN KEY (lyra_seller_uuid) REFERENCES crm_lyra_seller (uuid) ON DELETE SET NULL');
        $this->addSql('CREATE INDEX IDX_587E77272C7C4D3D ON crm_agency (lyra_seller_uuid)');

        foreach ($this->connection->fetchAllAssociative('SELECT uuid, lyra_seller as seller FROM crm_agency') as $item) {
            if (Uuid::isValid($item['seller'] ?? '')) {
                $uuid = (Uuid::fromRfc4122($item['seller']))->toBinary();
                $this->addSql("UPDATE crm_agency SET lyra_seller_uuid = ? WHERE uuid = ?", [
                    $uuid, $item['uuid']
                ]);
            }
        }
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_agency DROP FOREIGN KEY FK_587E77272C7C4D3D');
        $this->addSql('DROP INDEX IDX_587E77272C7C4D3D ON crm_agency');
        $this->addSql('ALTER TABLE crm_agency DROP lyra_seller_uuid');
    }
}

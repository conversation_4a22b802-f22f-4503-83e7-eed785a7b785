<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Symfony\Component\Uid\Uuid;

final class Version20241210102930 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_location_city DROP FOREIGN KEY FK_DCD2D23F133A7FB9');
        $this->addSql('ALTER TABLE crm_location_city CHANGE area_uuid area_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE crm_location_city ADD CONSTRAINT FK_DCD2D23F133A7FB9 FOREIGN KEY (area_uuid) REFERENCES crm_location_area (uuid) ON DELETE SET NULL');
        $this->addSql(
            'INSERT INTO crm_location_department (uuid, code, name) VALUES(?, ?, ?) ON DUPLICATE KEY UPDATE name = VALUES(name)',
            [(Uuid::V4())->toBinary(), 20, 'Corse']
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_location_city DROP FOREIGN KEY FK_DCD2D23F133A7FB9');
        $this->addSql('ALTER TABLE crm_location_city CHANGE area_uuid area_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE crm_location_city ADD CONSTRAINT FK_DCD2D23F133A7FB9 FOREIGN KEY (area_uuid) REFERENCES crm_location_area (uuid) ON UPDATE NO ACTION ON DELETE CASCADE');
    }
}

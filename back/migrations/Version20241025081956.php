<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241025081956 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_header_link ADD upload_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE crm_header_link ADD CONSTRAINT FK_780E765855FBD7DF FOREIGN KEY (upload_uuid) REFERENCES crm_upload (uuid) ON DELETE SET NULL');
        $this->addSql('CREATE INDEX IDX_780E765855FBD7DF ON crm_header_link (upload_uuid)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_header_link DROP FOREIGN KEY FK_780E765855FBD7DF');
        $this->addSql('DROP INDEX IDX_780E765855FBD7DF ON crm_header_link');
        $this->addSql('ALTER TABLE crm_header_link DROP upload_uuid');
    }
}

<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250114075236 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE est_price_grid ADD advantages JSON NOT NULL');
        $discounted = $this->connection->fetchAllAssociative('SELECT uuid, advantage_code, advantage_percent FROM est_price_grid WHERE advantage_code IS NOT NULL and advantage_code != "" AND advantage_percent IS NOT NULL');

        $json = json_encode([
            "#type" => "App\\Business\\Estimate\\Model\\Pricing\\Advantages",
            "advantages" => [
            ]
        ]);

        $this->addSql('UPDATE est_price_grid SET advantages = ?', [$json]);

        foreach ($discounted as $row) {
            $advantageCode = $row['advantage_code'];
            $advantagePercent = $row['advantage_percent'];

            $json = json_encode([
                "#type" => "App\\Business\\Estimate\\Model\\Pricing\\Advantages",
                "advantages" => [
                    [
                        "code" => $advantageCode,
                        "#type" => "App\\Business\\Estimate\\Model\\Pricing\\Advantage",
                        "percentage" => $advantagePercent
                    ]
                ]
            ]);

            $this->addSql('UPDATE est_price_grid SET advantages = ? WHERE uuid = ?', [$json, $row['uuid']]);
        }

        $this->addSql('ALTER TABLE est_price_grid DROP advantage_code, DROP advantage_percent');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE est_price_grid ADD advantage_code VARCHAR(20) DEFAULT NULL, ADD advantage_percent INT DEFAULT NULL, DROP advantages');
    }
}

<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241022102318 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE crm_agency (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', name VARCHAR(255) NOT NULL, created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP, PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE crm_agency_post (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', agency_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', upload_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', title VARCHAR(255) NOT NULL, content LONGTEXT NOT NULL, url VARCHAR(255) NOT NULL, created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP, INDEX IDX_AEABE5A1C6784CAE (agency_uuid), INDEX IDX_AEABE5A155FBD7DF (upload_uuid), PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE crm_content (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', parent_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\', created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP, published_at DATETIME DEFAULT NULL, INDEX IDX_128E7C3EEC9C6612 (parent_uuid), PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE crm_content_block (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP, published_at DATETIME DEFAULT NULL, PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE crm_content_page (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', title VARCHAR(255) NOT NULL, slug VARCHAR(255) NOT NULL, created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP, published_at DATETIME DEFAULT NULL, PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE crm_content_page_block (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', page_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', block_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP, position INT DEFAULT 0 NOT NULL, INDEX IDX_80B1700B56BB4C7C (page_uuid), INDEX IDX_80B1700B171102DD (block_uuid), PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE crm_footer (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', upload_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', content_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\', created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP, url VARCHAR(255) DEFAULT NULL, label VARCHAR(255) DEFAULT NULL, INDEX IDX_CA8FB49255FBD7DF (upload_uuid), INDEX IDX_CA8FB4921C1DBD63 (content_uuid), PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE crm_footer_link (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', footer_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', content_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\', created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP, url VARCHAR(255) DEFAULT NULL, label VARCHAR(255) DEFAULT NULL, position INT DEFAULT 0 NOT NULL, INDEX IDX_8923272E8DD67B5D (footer_uuid), INDEX IDX_8923272E1C1DBD63 (content_uuid), PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE crm_footer_nav (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', footer_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', parent_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\', content_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\', created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP, url VARCHAR(255) DEFAULT NULL, label VARCHAR(255) DEFAULT NULL, position INT DEFAULT 0 NOT NULL, INDEX IDX_275778E48DD67B5D (footer_uuid), INDEX IDX_275778E4EC9C6612 (parent_uuid), INDEX IDX_275778E41C1DBD63 (content_uuid), PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE crm_footer_social (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', footer_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', content_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\', upload_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP, position INT DEFAULT 0 NOT NULL, url VARCHAR(255) DEFAULT NULL, label VARCHAR(255) DEFAULT NULL, INDEX IDX_5B1819538DD67B5D (footer_uuid), INDEX IDX_5B1819531C1DBD63 (content_uuid), INDEX IDX_5B18195355FBD7DF (upload_uuid), PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE crm_header (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', upload_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', content_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\', created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP, url VARCHAR(255) DEFAULT NULL, label VARCHAR(255) DEFAULT NULL, INDEX IDX_46CC190055FBD7DF (upload_uuid), INDEX IDX_46CC19001C1DBD63 (content_uuid), PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE crm_header_link (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', header_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', content_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\', created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP, position INT DEFAULT 0 NOT NULL, url VARCHAR(255) DEFAULT NULL, label VARCHAR(255) DEFAULT NULL, INDEX IDX_780E76587CFB2A2B (header_uuid), INDEX IDX_780E76581C1DBD63 (content_uuid), PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE crm_header_nav (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', header_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', parent_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\', content_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\', created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP, position INT DEFAULT 0 NOT NULL, url VARCHAR(255) DEFAULT NULL, label VARCHAR(255) DEFAULT NULL, INDEX IDX_DEE44C357CFB2A2B (header_uuid), INDEX IDX_DEE44C35EC9C6612 (parent_uuid), INDEX IDX_DEE44C351C1DBD63 (content_uuid), PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE crm_location_area (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', department_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', agency_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\', name VARCHAR(255) NOT NULL, created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP, INDEX IDX_261DED63736537F3 (department_uuid), INDEX IDX_261DED63C6784CAE (agency_uuid), PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE crm_location_city (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', area_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', name VARCHAR(255) NOT NULL, zip VARCHAR(10) NOT NULL, has_termite TINYINT(1) DEFAULT 0 NOT NULL, created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP, INDEX IDX_DCD2D23F133A7FB9 (area_uuid), PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE crm_location_department (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', name VARCHAR(255) NOT NULL, code VARCHAR(255) NOT NULL, created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP, PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE crm_upload (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', name VARCHAR(255) NOT NULL, filename VARCHAR(255) NOT NULL, type VARCHAR(255) NOT NULL, size INT NOT NULL, created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP, PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE crm_user (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', email VARCHAR(255) NOT NULL, password VARCHAR(255) NOT NULL, role VARCHAR(255) NOT NULL, created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP, PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE crm_agency_post ADD CONSTRAINT FK_AEABE5A1C6784CAE FOREIGN KEY (agency_uuid) REFERENCES crm_agency (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_agency_post ADD CONSTRAINT FK_AEABE5A155FBD7DF FOREIGN KEY (upload_uuid) REFERENCES crm_upload (uuid)');
        $this->addSql('ALTER TABLE crm_content ADD CONSTRAINT FK_128E7C3EEC9C6612 FOREIGN KEY (parent_uuid) REFERENCES crm_content (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_content_page_block ADD CONSTRAINT FK_80B1700B56BB4C7C FOREIGN KEY (page_uuid) REFERENCES crm_content_page (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_content_page_block ADD CONSTRAINT FK_80B1700B171102DD FOREIGN KEY (block_uuid) REFERENCES crm_content_block (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_footer ADD CONSTRAINT FK_CA8FB49255FBD7DF FOREIGN KEY (upload_uuid) REFERENCES crm_upload (uuid)');
        $this->addSql('ALTER TABLE crm_footer ADD CONSTRAINT FK_CA8FB4921C1DBD63 FOREIGN KEY (content_uuid) REFERENCES crm_content (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_footer_link ADD CONSTRAINT FK_8923272E8DD67B5D FOREIGN KEY (footer_uuid) REFERENCES crm_footer (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_footer_link ADD CONSTRAINT FK_8923272E1C1DBD63 FOREIGN KEY (content_uuid) REFERENCES crm_content (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_footer_nav ADD CONSTRAINT FK_275778E48DD67B5D FOREIGN KEY (footer_uuid) REFERENCES crm_footer (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_footer_nav ADD CONSTRAINT FK_275778E4EC9C6612 FOREIGN KEY (parent_uuid) REFERENCES crm_footer_nav (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_footer_nav ADD CONSTRAINT FK_275778E41C1DBD63 FOREIGN KEY (content_uuid) REFERENCES crm_content (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_footer_social ADD CONSTRAINT FK_5B1819538DD67B5D FOREIGN KEY (footer_uuid) REFERENCES crm_footer (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_footer_social ADD CONSTRAINT FK_5B1819531C1DBD63 FOREIGN KEY (content_uuid) REFERENCES crm_content (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_footer_social ADD CONSTRAINT FK_5B18195355FBD7DF FOREIGN KEY (upload_uuid) REFERENCES crm_upload (uuid)');
        $this->addSql('ALTER TABLE crm_header ADD CONSTRAINT FK_46CC190055FBD7DF FOREIGN KEY (upload_uuid) REFERENCES crm_upload (uuid)');
        $this->addSql('ALTER TABLE crm_header ADD CONSTRAINT FK_46CC19001C1DBD63 FOREIGN KEY (content_uuid) REFERENCES crm_content (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_header_link ADD CONSTRAINT FK_780E76587CFB2A2B FOREIGN KEY (header_uuid) REFERENCES crm_header (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_header_link ADD CONSTRAINT FK_780E76581C1DBD63 FOREIGN KEY (content_uuid) REFERENCES crm_content (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_header_nav ADD CONSTRAINT FK_DEE44C357CFB2A2B FOREIGN KEY (header_uuid) REFERENCES crm_header (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_header_nav ADD CONSTRAINT FK_DEE44C35EC9C6612 FOREIGN KEY (parent_uuid) REFERENCES crm_header_nav (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_header_nav ADD CONSTRAINT FK_DEE44C351C1DBD63 FOREIGN KEY (content_uuid) REFERENCES crm_content (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_location_area ADD CONSTRAINT FK_261DED63736537F3 FOREIGN KEY (department_uuid) REFERENCES crm_location_department (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_location_area ADD CONSTRAINT FK_261DED63C6784CAE FOREIGN KEY (agency_uuid) REFERENCES crm_agency (uuid) ON DELETE SET NULL');
        $this->addSql('ALTER TABLE crm_location_city ADD CONSTRAINT FK_DCD2D23F133A7FB9 FOREIGN KEY (area_uuid) REFERENCES crm_location_area (uuid) ON DELETE CASCADE');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_agency_post DROP FOREIGN KEY FK_AEABE5A1C6784CAE');
        $this->addSql('ALTER TABLE crm_agency_post DROP FOREIGN KEY FK_AEABE5A155FBD7DF');
        $this->addSql('ALTER TABLE crm_content DROP FOREIGN KEY FK_128E7C3EEC9C6612');
        $this->addSql('ALTER TABLE crm_content_page_block DROP FOREIGN KEY FK_80B1700B56BB4C7C');
        $this->addSql('ALTER TABLE crm_content_page_block DROP FOREIGN KEY FK_80B1700B171102DD');
        $this->addSql('ALTER TABLE crm_footer DROP FOREIGN KEY FK_CA8FB49255FBD7DF');
        $this->addSql('ALTER TABLE crm_footer DROP FOREIGN KEY FK_CA8FB4921C1DBD63');
        $this->addSql('ALTER TABLE crm_footer_link DROP FOREIGN KEY FK_8923272E8DD67B5D');
        $this->addSql('ALTER TABLE crm_footer_link DROP FOREIGN KEY FK_8923272E1C1DBD63');
        $this->addSql('ALTER TABLE crm_footer_nav DROP FOREIGN KEY FK_275778E48DD67B5D');
        $this->addSql('ALTER TABLE crm_footer_nav DROP FOREIGN KEY FK_275778E4EC9C6612');
        $this->addSql('ALTER TABLE crm_footer_nav DROP FOREIGN KEY FK_275778E41C1DBD63');
        $this->addSql('ALTER TABLE crm_footer_social DROP FOREIGN KEY FK_5B1819538DD67B5D');
        $this->addSql('ALTER TABLE crm_footer_social DROP FOREIGN KEY FK_5B1819531C1DBD63');
        $this->addSql('ALTER TABLE crm_footer_social DROP FOREIGN KEY FK_5B18195355FBD7DF');
        $this->addSql('ALTER TABLE crm_header DROP FOREIGN KEY FK_46CC190055FBD7DF');
        $this->addSql('ALTER TABLE crm_header DROP FOREIGN KEY FK_46CC19001C1DBD63');
        $this->addSql('ALTER TABLE crm_header_link DROP FOREIGN KEY FK_780E76587CFB2A2B');
        $this->addSql('ALTER TABLE crm_header_link DROP FOREIGN KEY FK_780E76581C1DBD63');
        $this->addSql('ALTER TABLE crm_header_nav DROP FOREIGN KEY FK_DEE44C357CFB2A2B');
        $this->addSql('ALTER TABLE crm_header_nav DROP FOREIGN KEY FK_DEE44C35EC9C6612');
        $this->addSql('ALTER TABLE crm_header_nav DROP FOREIGN KEY FK_DEE44C351C1DBD63');
        $this->addSql('ALTER TABLE crm_location_area DROP FOREIGN KEY FK_261DED63736537F3');
        $this->addSql('ALTER TABLE crm_location_area DROP FOREIGN KEY FK_261DED63C6784CAE');
        $this->addSql('ALTER TABLE crm_location_city DROP FOREIGN KEY FK_DCD2D23F133A7FB9');
        $this->addSql('DROP TABLE crm_agency');
        $this->addSql('DROP TABLE crm_agency_post');
        $this->addSql('DROP TABLE crm_content');
        $this->addSql('DROP TABLE crm_content_block');
        $this->addSql('DROP TABLE crm_content_page');
        $this->addSql('DROP TABLE crm_content_page_block');
        $this->addSql('DROP TABLE crm_footer');
        $this->addSql('DROP TABLE crm_footer_link');
        $this->addSql('DROP TABLE crm_footer_nav');
        $this->addSql('DROP TABLE crm_footer_social');
        $this->addSql('DROP TABLE crm_header');
        $this->addSql('DROP TABLE crm_header_link');
        $this->addSql('DROP TABLE crm_header_nav');
        $this->addSql('DROP TABLE crm_location_area');
        $this->addSql('DROP TABLE crm_location_city');
        $this->addSql('DROP TABLE crm_location_department');
        $this->addSql('DROP TABLE crm_upload');
        $this->addSql('DROP TABLE crm_user');
    }
}

<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250102145508 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE est_estimate ADD customer_address VARCHAR(255) DEFAULT NULL, ADD customer_commentary VARCHAR(255) DEFAULT NULL, ADD email_object VARCHAR(255) DEFAULT NULL, ADD email_sign LONGTEXT DEFAULT NULL, ADD execution VARCHAR(255) DEFAULT NULL, ADD offer_consent TINYINT(1) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE est_estimate DROP customer_address, DROP customer_commentary, DROP email_object, DROP email_sign, DROP execution, DROP offer_consent');
    }
}

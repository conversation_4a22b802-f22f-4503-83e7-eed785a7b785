<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241224142455 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_job_application DROP INDEX UNIQ_E962B41CDBED2A72, ADD INDEX IDX_E962B41CDBED2A72 (cover_letter_uuid)');
        $this->addSql('ALTER TABLE crm_job_application DROP INDEX UNIQ_E962B41CB031B07A, ADD INDEX IDX_E962B41CB031B07A (cv_uuid)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_job_application DROP INDEX IDX_E962B41CB031B07A, ADD UNIQUE INDEX UNIQ_E962B41CB031B07A (cv_uuid)');
        $this->addSql('ALTER TABLE crm_job_application DROP INDEX IDX_E962B41CDBED2A72, ADD UNIQUE INDEX UNIQ_E962B41CDBED2A72 (cover_letter_uuid)');
    }
}

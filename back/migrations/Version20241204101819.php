<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241204101819 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('DROP INDEX `primary` ON crm_setting');
        $this->addSql('ALTER TABLE crm_setting ADD uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', DROP id');
        $this->addSql('ALTER TABLE crm_setting ADD PRIMARY KEY (uuid)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX `PRIMARY` ON crm_setting');
        $this->addSql('ALTER TABLE crm_setting ADD id INT NOT NULL, DROP uuid');
        $this->addSql('ALTER TABLE crm_setting ADD PRIMARY KEY (id)');
    }
}

<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241023124543 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_content ADD content_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\', ADD name VARCHAR(255) NOT NULL, ADD slug VARCHAR(255) NOT NULL');
        $this->addSql('ALTER TABLE crm_content ADD CONSTRAINT FK_128E7C3E1C1DBD63 FOREIGN KEY (content_uuid) REFERENCES crm_content_page (uuid) ON DELETE SET NULL');
        $this->addSql('CREATE INDEX IDX_128E7C3E1C1DBD63 ON crm_content (content_uuid)');
        $this->addSql('ALTER TABLE crm_content_block ADD name VARCHAR(255) NOT NULL, ADD type VARCHAR(255) NOT NULL, ADD parameters JSON NOT NULL');
        $this->addSql('ALTER TABLE crm_content_page DROP title, DROP slug');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_content DROP FOREIGN KEY FK_128E7C3E1C1DBD63');
        $this->addSql('DROP INDEX IDX_128E7C3E1C1DBD63 ON crm_content');
        $this->addSql('ALTER TABLE crm_content DROP content_uuid, DROP name, DROP slug');
        $this->addSql('ALTER TABLE crm_content_block DROP name, DROP type, DROP parameters');
        $this->addSql('ALTER TABLE crm_content_page ADD title VARCHAR(255) NOT NULL, ADD slug VARCHAR(255) NOT NULL');
    }
}

<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241129113500 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE crm_job_application (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', cv_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', cover_letter_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', job_offer_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', firstname VARCHAR(255) NOT NULL, lastname VARCHAR(255) NOT NULL, address VARCHAR(255) NOT NULL, zip_code VARCHAR(255) NOT NULL, city VARCHAR(255) NOT NULL, phone VARCHAR(255) NOT NULL, email VARCHAR(255) NOT NULL, training LONGTEXT NOT NULL, driving_license JSON NOT NULL, professional_experience LONGTEXT NOT NULL, computer_level LONGTEXT DEFAULT NULL, situation VARCHAR(255) DEFAULT NULL, workplace VARCHAR(255) DEFAULT NULL, availability VARCHAR(255) DEFAULT NULL, salary DOUBLE PRECISION DEFAULT NULL, created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP, UNIQUE INDEX UNIQ_E962B41CB031B07A (cv_uuid), UNIQUE INDEX UNIQ_E962B41CDBED2A72 (cover_letter_uuid), INDEX IDX_E962B41C45F0EEAC (job_offer_uuid), PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE crm_job_offer (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', agency_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', location VARCHAR(255) NOT NULL, title VARCHAR(255) NOT NULL, description VARCHAR(255) NOT NULL, content LONGTEXT NOT NULL, reference VARCHAR(255) NOT NULL, job_name VARCHAR(255) NOT NULL, created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP, UNIQUE INDEX UNIQ_69E0C763AEA34913 (reference), INDEX IDX_69E0C763C6784CAE (agency_uuid), PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE crm_job_application ADD CONSTRAINT FK_E962B41CB031B07A FOREIGN KEY (cv_uuid) REFERENCES crm_upload (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_job_application ADD CONSTRAINT FK_E962B41CDBED2A72 FOREIGN KEY (cover_letter_uuid) REFERENCES crm_upload (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_job_application ADD CONSTRAINT FK_E962B41C45F0EEAC FOREIGN KEY (job_offer_uuid) REFERENCES crm_job_offer (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_job_offer ADD CONSTRAINT FK_69E0C763C6784CAE FOREIGN KEY (agency_uuid) REFERENCES crm_agency (uuid) ON DELETE CASCADE');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_job_application DROP FOREIGN KEY FK_E962B41CB031B07A');
        $this->addSql('ALTER TABLE crm_job_application DROP FOREIGN KEY FK_E962B41CDBED2A72');
        $this->addSql('ALTER TABLE crm_job_application DROP FOREIGN KEY FK_E962B41C45F0EEAC');
        $this->addSql('ALTER TABLE crm_job_offer DROP FOREIGN KEY FK_69E0C763C6784CAE');
        $this->addSql('DROP TABLE crm_job_application');
        $this->addSql('DROP TABLE crm_job_offer');
    }
}

<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241226112330 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE est_pro_estimate (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', city_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', civility VARCHAR(30) DEFAULT NULL, firstname VARCHAR(255) NOT NULL, lastname VARCHAR(255) NOT NULL, company VARCHAR(255) NOT NULL, email VARCHAR(255) NOT NULL, custom_missions VARCHAR(255) DEFAULT NULL, risque_nat_techno TINYINT(1) NOT NULL, perf_energ TINYINT(1) NOT NULL, diag_tech_global TINYINT(1) NOT NULL, milliemes_copro TINYINT(1) NOT NULL, diag_tech TINYINT(1) NOT NULL, diag_avant_travaux TINYINT(1) NOT NULL, logement_decent TINYINT(1) NOT NULL, elec TINYINT(1) NOT NULL, etat_lieu_entrant TINYINT(1) NOT NULL, surface_habitable TINYINT(1) NOT NULL, termites TINYINT(1) NOT NULL, amiante TINYINT(1) NOT NULL, plomb TINYINT(1) NOT NULL, gaz TINYINT(1) NOT NULL, loi_carrez TINYINT(1) NOT NULL, access_handi TINYINT(1) NOT NULL, assainissement_auto TINYINT(1) NOT NULL, etat_lieu TINYINT(1) NOT NULL, merules TINYINT(1) NOT NULL, created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, INDEX IDX_DD7057F932316DCE (city_uuid), PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE est_pro_estimate ADD CONSTRAINT FK_DD7057F932316DCE FOREIGN KEY (city_uuid) REFERENCES crm_location_city (uuid) ON DELETE CASCADE');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE est_pro_estimate DROP FOREIGN KEY FK_DD7057F932316DCE');
        $this->addSql('DROP TABLE est_pro_estimate');
    }
}

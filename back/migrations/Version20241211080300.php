<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241211080300 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE crm_partner (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', upload_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\', department_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\', name VARCHAR(255) NOT NULL, link VARCHAR(255) NOT NULL, activity VARCHAR(255) NOT NULL, created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP, INDEX IDX_DD60728155FBD7DF (upload_uuid), INDEX IDX_DD607281736537F3 (department_uuid), PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE crm_partner ADD CONSTRAINT FK_DD60728155FBD7DF FOREIGN KEY (upload_uuid) REFERENCES crm_upload (uuid) ON DELETE SET NULL');
        $this->addSql('ALTER TABLE crm_partner ADD CONSTRAINT FK_DD607281736537F3 FOREIGN KEY (department_uuid) REFERENCES crm_location_department (uuid) ON DELETE SET NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_partner DROP FOREIGN KEY FK_DD60728155FBD7DF');
        $this->addSql('ALTER TABLE crm_partner DROP FOREIGN KEY FK_DD607281736537F3');
        $this->addSql('DROP TABLE crm_partner');
    }
}

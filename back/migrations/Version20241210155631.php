<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241210155631 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_post DROP FOREIGN KEY FK_35564E3C55FBD7DF');
        $this->addSql('ALTER TABLE crm_post CHANGE upload_uuid upload_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE crm_post ADD CONSTRAINT FK_35564E3C55FBD7DF FOREIGN KEY (upload_uuid) REFERENCES crm_upload (uuid) ON DELETE SET NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_post DROP FOREIGN KEY FK_35564E3C55FBD7DF');
        $this->addSql('ALTER TABLE crm_post CHANGE upload_uuid upload_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE crm_post ADD CONSTRAINT FK_35564E3C55FBD7DF FOREIGN KEY (upload_uuid) REFERENCES crm_upload (uuid) ON UPDATE NO ACTION ON DELETE NO ACTION');
    }
}

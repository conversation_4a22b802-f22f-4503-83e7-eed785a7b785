<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241105091812 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_agency ADD upload_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\', ADD meta_title VARCHAR(150) DEFAULT NULL, ADD meta_description VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE crm_agency ADD CONSTRAINT FK_587E772755FBD7DF FOREIGN KEY (upload_uuid) REFERENCES crm_upload (uuid) ON DELETE SET NULL');
        $this->addSql('CREATE INDEX IDX_587E772755FBD7DF ON crm_agency (upload_uuid)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_agency DROP FOREIGN KEY FK_587E772755FBD7DF');
        $this->addSql('DROP INDEX IDX_587E772755FBD7DF ON crm_agency');
        $this->addSql('ALTER TABLE crm_agency DROP upload_uuid, DROP meta_title, DROP meta_description');
    }
}

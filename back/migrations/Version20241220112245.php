<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241220112245 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_setting ADD search_link LONGTEXT DEFAULT NULL, ADD quote_link LONGTEXT DEFAULT NULL, ADD html LONGTEXT DEFAULT NULL, ADD js LONGTEXT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_setting DROP search_link, DROP quote_link, DROP html, DROP js');
    }
}

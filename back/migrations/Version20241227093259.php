<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241227093259 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE crm_lyra_order (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', estimate_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', status VARCHAR(255) NOT NULL, is_multiple TINYINT(1) NOT NULL, created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP, INDEX IDX_3CA8C095977E09A7 (estimate_uuid), PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE crm_lyra_order ADD CONSTRAINT FK_3CA8C095977E09A7 FOREIGN KEY (estimate_uuid) REFERENCES est_estimate (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE est_estimate ADD customer_civility VARCHAR(30) DEFAULT NULL, ADD customer_firstname VARCHAR(255) DEFAULT NULL, ADD customer_lastname VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_lyra_order DROP FOREIGN KEY FK_3CA8C095977E09A7');
        $this->addSql('DROP TABLE crm_lyra_order');
        $this->addSql('ALTER TABLE est_estimate DROP customer_civility, DROP customer_firstname, DROP customer_lastname');
    }
}

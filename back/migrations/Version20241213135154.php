<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241213135154 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE est_estimate (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', city_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', opportunity_wanted JSON NOT NULL COMMENT \'(DC2Type:json_document)\', price DOUBLE PRECISION NOT NULL, discount_price DOUBLE PRECISION NOT NULL, appointment_date VARCHAR(200) DEFAULT NULL, discount_code VARCHAR(20) DEFAULT NULL, customer_email VARCHAR(200) NOT NULL, customer_phone VARCHAR(20) NOT NULL, created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, INDEX IDX_4965DBAE32316DCE (city_uuid), PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE est_price_grid (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', agency_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', area_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\', type INT NOT NULL, grid_data JSON NOT NULL COMMENT \'(DC2Type:json_document)\', name VARCHAR(255) NOT NULL, is_active TINYINT(1) NOT NULL, advantage_code VARCHAR(20) DEFAULT NULL, advantage_percent INT DEFAULT NULL, created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP, INDEX IDX_A209585BC6784CAE (agency_uuid), INDEX IDX_A209585B133A7FB9 (area_uuid), UNIQUE INDEX UNIQ_A209585BC6784CAE133A7FB98CDE5729 (agency_uuid, area_uuid, type), PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE est_estimate ADD CONSTRAINT FK_4965DBAE32316DCE FOREIGN KEY (city_uuid) REFERENCES crm_location_city (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE est_price_grid ADD CONSTRAINT FK_A209585BC6784CAE FOREIGN KEY (agency_uuid) REFERENCES crm_agency (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE est_price_grid ADD CONSTRAINT FK_A209585B133A7FB9 FOREIGN KEY (area_uuid) REFERENCES crm_location_area (uuid) ON DELETE SET NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE est_estimate DROP FOREIGN KEY FK_4965DBAE32316DCE');
        $this->addSql('ALTER TABLE est_price_grid DROP FOREIGN KEY FK_A209585BC6784CAE');
        $this->addSql('ALTER TABLE est_price_grid DROP FOREIGN KEY FK_A209585B133A7FB9');
        $this->addSql('DROP TABLE est_estimate');
        $this->addSql('DROP TABLE est_price_grid');
    }
}

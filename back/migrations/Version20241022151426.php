<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241022151426 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE crm_agency_user (uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', user_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', agency_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, INDEX IDX_79B25F65ABFE1C6F (user_uuid), INDEX IDX_79B25F65C6784CAE (agency_uuid), PRIMARY KEY(uuid)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE crm_agency_user ADD CONSTRAINT FK_79B25F65ABFE1C6F FOREIGN KEY (user_uuid) REFERENCES crm_user (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_agency_user ADD CONSTRAINT FK_79B25F65C6784CAE FOREIGN KEY (agency_uuid) REFERENCES crm_agency (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_user ADD lastname VARCHAR(255) NOT NULL, ADD firstname VARCHAR(255) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_agency_user DROP FOREIGN KEY FK_79B25F65ABFE1C6F');
        $this->addSql('ALTER TABLE crm_agency_user DROP FOREIGN KEY FK_79B25F65C6784CAE');
        $this->addSql('DROP TABLE crm_agency_user');
        $this->addSql('ALTER TABLE crm_user DROP lastname, DROP firstname');
    }
}

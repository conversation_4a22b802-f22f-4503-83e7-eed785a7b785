<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250110072916 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_agency ADD legacy_id INT NOT NULL');
        $agencies = $this->connection->fetchAllAssociative('SELECT uuid FROM crm_agency');

        foreach ($agencies as $key => $agency) {
            $this->addSql('UPDATE crm_agency SET legacy_id = ? WHERE uuid = ?', [100000+$key, $agency['uuid']]);
        }

        $this->addSql('CREATE UNIQUE INDEX UNIQ_587E7727184998FC ON crm_agency (legacy_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX UNIQ_587E7727184998FC ON crm_agency');
        $this->addSql('ALTER TABLE crm_agency DROP legacy_id');
    }
}

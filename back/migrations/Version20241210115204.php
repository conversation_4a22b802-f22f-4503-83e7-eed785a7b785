<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241210115204 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_agency_location_area DROP FOREIGN KEY FK_98E80E49517BE5E6');
        $this->addSql('ALTER TABLE crm_agency_location_area DROP FOREIGN KEY FK_98E80E49C6784CAE');
        $this->addSql('DROP TABLE crm_agency_location_area');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('CREATE TABLE crm_agency_location_area (agency_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', location_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', INDEX IDX_98E80E49517BE5E6 (location_uuid), INDEX IDX_98E80E49C6784CAE (agency_uuid), PRIMARY KEY(agency_uuid, location_uuid)) DEFAULT CHARACTER SET utf8mb3 COLLATE `utf8mb3_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('ALTER TABLE crm_agency_location_area ADD CONSTRAINT FK_98E80E49517BE5E6 FOREIGN KEY (location_uuid) REFERENCES crm_location_area (uuid) ON UPDATE NO ACTION ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_agency_location_area ADD CONSTRAINT FK_98E80E49C6784CAE FOREIGN KEY (agency_uuid) REFERENCES crm_agency (uuid) ON UPDATE NO ACTION ON DELETE CASCADE');
    }
}

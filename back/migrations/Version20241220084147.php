<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241220084147 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE est_estimate ADD type VARCHAR(255) NOT NULL, ADD property_type VARCHAR(255) NOT NULL, ADD custom_property_type VARCHAR(255) DEFAULT NULL, ADD room_number INT NOT NULL, ADD build_year VARCHAR(255) NOT NULL, ADD gaz_older_than15_years TINYINT(1) NOT NULL, ADD electricity_older_than15_years TINYINT(1) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE est_estimate DROP type, DROP property_type, DROP custom_property_type, DROP room_number, DROP build_year, DROP gaz_older_than15_years, DROP electricity_older_than15_years');
    }
}

<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241105081849 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE crm_agency_location_area (agency_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', location_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', INDEX IDX_98E80E49C6784CAE (agency_uuid), INDEX IDX_98E80E49517BE5E6 (location_uuid), PRIMARY KEY(agency_uuid, location_uuid)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE crm_agency_location_area ADD CONSTRAINT FK_98E80E49C6784CAE FOREIGN KEY (agency_uuid) REFERENCES crm_agency (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_agency_location_area ADD CONSTRAINT FK_98E80E49517BE5E6 FOREIGN KEY (location_uuid) REFERENCES crm_location_area (uuid) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE crm_agency ADD free_call_id VARCHAR(20) NOT NULL, ADD certifications VARCHAR(255) NOT NULL, ADD description LONGTEXT DEFAULT NULL, ADD lyra_seller VARCHAR(255) DEFAULT NULL, ADD schedule JSON NOT NULL COMMENT \'(DC2Type:json_document)\', ADD display_appointment TINYINT(1) NOT NULL, ADD location_address1 VARCHAR(255) NOT NULL, ADD location_address2 VARCHAR(255) DEFAULT NULL, ADD location_city VARCHAR(100) NOT NULL, ADD location_postcode VARCHAR(10) NOT NULL, ADD location_latitude NUMERIC(20, 16) DEFAULT NULL, ADD location_longitude NUMERIC(20, 16) DEFAULT NULL, ADD contact_name VARCHAR(150) NOT NULL, ADD contact_phone VARCHAR(20) NOT NULL, ADD contact_email VARCHAR(150) NOT NULL, ADD advantage_code VARCHAR(50) DEFAULT NULL, ADD advantage_percent SMALLINT DEFAULT NULL, ADD rating_widget_html LONGTEXT DEFAULT NULL, ADD rating_widget_js LONGTEXT DEFAULT NULL');
        $this->addSql('ALTER TABLE crm_footer_social DROP FOREIGN KEY FK_5B18195355FBD7DF');
        $this->addSql('ALTER TABLE crm_footer_social CHANGE upload_uuid upload_uuid BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE crm_footer_social ADD CONSTRAINT FK_5B18195355FBD7DF FOREIGN KEY (upload_uuid) REFERENCES crm_upload (uuid) ON DELETE SET NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_agency_location_area DROP FOREIGN KEY FK_98E80E49C6784CAE');
        $this->addSql('ALTER TABLE crm_agency_location_area DROP FOREIGN KEY FK_98E80E49517BE5E6');
        $this->addSql('DROP TABLE crm_agency_location_area');
        $this->addSql('ALTER TABLE crm_agency DROP free_call_id, DROP certifications, DROP description, DROP lyra_seller, DROP schedule, DROP display_appointment, DROP location_address1, DROP location_address2, DROP location_city, DROP location_postcode, DROP location_latitude, DROP location_longitude, DROP contact_name, DROP contact_phone, DROP contact_email, DROP advantage_code, DROP advantage_percent, DROP rating_widget_html, DROP rating_widget_js');
        $this->addSql('ALTER TABLE crm_footer_social DROP FOREIGN KEY FK_5B18195355FBD7DF');
        $this->addSql('ALTER TABLE crm_footer_social CHANGE upload_uuid upload_uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE crm_footer_social ADD CONSTRAINT FK_5B18195355FBD7DF FOREIGN KEY (upload_uuid) REFERENCES crm_upload (uuid) ON UPDATE NO ACTION ON DELETE NO ACTION');
    }
}

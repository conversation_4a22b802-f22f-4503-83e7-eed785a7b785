<?php declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241024134009 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_content ADD breadcrumb_uuids VARCHAR(1000) DEFAULT \'/\' NOT NULL, ADD breadcrumb_names VARCHAR(1000) DEFAULT \'/\' NOT NULL, ADD breadcrumb_slugs VARCHAR(1000) DEFAULT \'/\' NOT NULL, ADD breadcrumb_depth INT DEFAULT 0 NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE crm_content DROP breadcrumb_uuids, DROP breadcrumb_names, DROP breadcrumb_slugs, DROP breadcrumb_depth');
    }
}

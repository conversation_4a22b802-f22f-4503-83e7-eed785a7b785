<?php declare(strict_types=1);

namespace App\Controller;

use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\Routing\RouterInterface;

#[AsController]
class DefaultController
{
    public function index(
        RouterInterface $router,
    ): RedirectResponse {
        return new RedirectResponse($router->generate('api'));
    }
}

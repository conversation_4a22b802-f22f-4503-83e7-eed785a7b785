<?php declare(strict_types=1);

namespace App\Repository;

use App\Entity\Content\Content;
use Doctrine\ORM\EntityRepository;

/**
 * @template-extends EntityRepository<Content>
 */
class ContentRepository extends EntityRepository
{
    public function getOneBySlug(string $slug): ?Content
    {
        return $this->createQueryBuilder('c')
            ->where('CONCAT(c.breadcrumb.uuids, BIN_TO_UUID(c.uuid)) = :slug')
            ->orWhere('CONCAT(c.breadcrumb.slugs, c.slug) = :slug')
            ->setParameter('slug', $slug)
            ->getQuery()
            ->getOneOrNullResult();
    }
}

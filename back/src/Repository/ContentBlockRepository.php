<?php declare(strict_types=1);

namespace App\Repository;

use App\Doctrine\Enum\TableEnum;
use App\Entity\Content\ContentBlock;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Generator;
use Psr\Log\LoggerInterface;
use Throwable;

/**
 * @template-extends ServiceEntityRepository<ContentBlock>
 */
class ContentBlockRepository extends ServiceEntityRepository
{
    public function __construct(
        private readonly LoggerInterface $logger,
        ManagerRegistry $registry,
    ) {
        parent::__construct($registry, ContentBlock::class);
    }

    /**
     * @return Generator<ContentBlock>
     */
    public function getFavorites(): Generator
    {
        yield from $this->createQueryBuilder('b')
            ->where('b.name IS NOT NULL')
            ->orderBy('b.createdAt', 'DESC')
            ->getQuery()
            ->toIterable();
    }

    public function cleanOrphans(): int
    {
        try {
            $tableContentBlock = TableEnum::CONTENT_BLOCK;
            $tableContentPageBlock = TableEnum::CONTENT_PAGE_BLOCK;

            $query = <<<SQL
            DELETE FROM {$tableContentBlock} b
            WHERE NOT EXISTS (
            	SELECT 1
            	FROM {$tableContentPageBlock} cpb
            	WHERE cpb.block_uuid = b.uuid
            )
            AND b.name IS NULL
            SQL;

            return (int)$this->getEntityManager()->getConnection()->executeStatement($query);
        } catch (Throwable $e) {
            $this->logger->error('Unable to delete orphans contents blocks', ['exception' => $e]);
        }

        return 0;
    }
}

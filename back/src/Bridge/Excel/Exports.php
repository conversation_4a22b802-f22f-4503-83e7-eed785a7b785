<?php declare(strict_types=1);

namespace App\Bridge\Excel;

use App\Bridge\Excel\Exporter\ExcelExporterInterface;
use App\Contract\Uuids;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\DependencyInjection\Attribute\TaggedLocator;
use Symfony\Component\DependencyInjection\ServiceLocator;
use Symfony\Component\Filesystem\Filesystem;

final readonly class Exports
{
    public function __construct(
        #[TaggedLocator(ExcelExporterInterface::class, defaultIndexMethod: 'getType')]
        private ServiceLocator $exporters,
        #[Autowire('%excel_dir%')]
        private string $exportDir,
        private Filesystem $fs,
    ) {
    }

    public function export(string $type, Uuids $uuids): string
    {
        return $this->exporters->get($type)->export($uuids);
    }

    public function getDir(): string
    {
        $dir = rtrim($this->exportDir, '/') . '/';

        $this->fs->mkdir($dir);

        return $dir;
    }
}

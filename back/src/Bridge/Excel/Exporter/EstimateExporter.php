<?php declare(strict_types=1);

namespace App\Bridge\Excel\Exporter;

use App\Bridge\Excel\Enum\ExportTypeEnum;
use App\Bridge\Twig\AgendaExtensionsRuntime;
use App\Business\Estimate\Entity\Estimate;
use App\Contract\Uuids;
use Symfony\Component\Uid\UuidV4;

class EstimateExporter extends AbstractExcelExporter
{
    public static function getType(): string
    {
        return ExportTypeEnum::ESTIMATE;
    }

    public function export(Uuids $uuids): string
    {
        $datas = $this->em->getRepository(Estimate::class)->findBy(['uuid' => $uuids->toBinaries()]);

        $excelData = [];
        foreach ($datas as $estimate) {
            $excelData[] = [
                self::format($estimate->customerFirstname),
                self::format($estimate->customerLastname),
                self::format($estimate->city?->name),
                self::format($estimate->city->zip),
                self::format(null),
                self::format(AgendaExtensionsRuntime::estimateType($estimate->type)),
                self::format(AgendaExtensionsRuntime::estimatePropertyType($estimate->propertyType)),
                self::format($estimate->roomNumber),
                self::format(AgendaExtensionsRuntime::estimateYear($estimate->buildYear)),
                self::format($estimate->gazOlderThan15Years),
                self::format($estimate->electricityOlderThan15Years),
                self::format($estimate->opportunityWanted->isAmiante()),
                self::format($estimate->opportunityWanted->isAudit()),
                self::format($estimate->opportunityWanted->isCarrez()),
                self::format($estimate->opportunityWanted->isDecent()),
                self::format($estimate->opportunityWanted->isDpe()),
                self::format($estimate->opportunityWanted->isElectricite()),
                self::format($estimate->opportunityWanted->isErp()),
                self::format($estimate->opportunityWanted->isEtatLieux()),
                self::format($estimate->opportunityWanted->isGaz()),
                self::format($estimate->opportunityWanted->isMesurage()),
                self::format($estimate->opportunityWanted->isPlomb()),
                self::format($estimate->opportunityWanted->isTermites()),
                self::format($estimate->price),
                self::format($estimate->appointmentPrice),
                self::format($estimate->appointmentDate),
                self::format(null),
                self::format($estimate->discountCode),
            ];

            $this->em->detach($estimate);
        }

        $filepath = (new UuidV4())->toRfc4122();

        $this->writeExcel([
            $this->getHeaders(),
            ...$excelData,
        ], $filepath);

        return $filepath;
    }

    public function getHeaders(): array
    {
        return [
            'Nom',
            'Prénom',
            'Ville',
            'Code postal',
            'Commentaire',
            'Type de devis',
            'Type de bien',
            'Nombre de pièces',
            'Année de construction',
            'Installation gaz',
            'Installation electricité',
            'Amiante',
            'Audit',
            'Loi carrez',
            'Logement décent',
            'DPE',
            'Electricité',
            'ERP',
            'État des lieux',
            'Gaz',
            'Mesurage',
            'Plomb',
            'Termites',
            'Prix',
            'Prix réduction',
            'Date RDV',
            'Date FR',
            'Code avantage',
        ];
    }
}

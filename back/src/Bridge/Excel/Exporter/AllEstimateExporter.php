<?php declare(strict_types=1);

namespace App\Bridge\Excel\Exporter;

use App\Bridge\Excel\Enum\ExportTypeEnum;
use App\Bridge\Twig\AgendaExtensionsRuntime;
use App\Business\Estimate\Entity\Estimate;
use App\Contract\Uuids;
use App\Entity\Agency\AgencyUser;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;
use Symfony\Component\Uid\UuidV4;
use function count;

class AllEstimateExporter extends AbstractExcelExporter
{
    public static function getType(): string
    {
        return ExportTypeEnum::ALL_ESTIMATE;
    }

    public function export(Uuids $uuids): string
    {
        $queryBuilder = $this->em->createQueryBuilder()
            ->select('e')
            ->from(Estimate::class, 'e');

        if (!$this->security->user->isSuperAdmin()) {
            $agencies = $this->em->getRepository(AgencyUser::class)->findBy(['user' => $this->security->user]);
            $agencies = array_map(fn (AgencyUser $agencyUser) => $agencyUser->agency, $agencies);

            if (0 === count($agencies)) {
                throw new UnauthorizedHttpException('');
            }

            $queryBuilder->andWhere('e.agency IN (:agencies)')->setParameter('agencies', $agencies);
        }

        $queryBuilder->orderBy('e.createdAt', 'DESC');

        $filepath = (new UuidV4())->toRfc4122();

        $r = $this->getRessource($filepath);

        $this->writeCsv($this->getHeaders(), $r);

        /** @var Estimate $estimate */
        foreach ($queryBuilder->getQuery()->toIterable() as $estimate) {
            $this->writeCsv([
                self::format($estimate->customerFirstname),
                self::format($estimate->customerLastname),
                self::format($estimate->customerEmail),
                self::format($estimate->customerPhone),
                self::format($estimate->city?->name),
                self::format($estimate->city->zip),
                self::format(null),
                self::format(AgendaExtensionsRuntime::estimateType($estimate->type)),
                self::format(AgendaExtensionsRuntime::estimatePropertyType($estimate->propertyType)),
                self::format($estimate->roomNumber),
                self::format(AgendaExtensionsRuntime::estimateYear($estimate->buildYear)),
                self::format($estimate->gazOlderThan15Years),
                self::format($estimate->electricityOlderThan15Years),
                self::format($estimate->opportunityWanted->isAmiante()),
                self::format($estimate->opportunityWanted->isAudit()),
                self::format($estimate->opportunityWanted->isCarrez()),
                self::format($estimate->opportunityWanted->isDecent()),
                self::format($estimate->opportunityWanted->isDpe()),
                self::format($estimate->opportunityWanted->isElectricite()),
                self::format($estimate->opportunityWanted->isErp()),
                self::format($estimate->opportunityWanted->isEtatLieux()),
                self::format($estimate->opportunityWanted->isGaz()),
                self::format($estimate->opportunityWanted->isMesurage()),
                self::format($estimate->opportunityWanted->isPlomb()),
                self::format($estimate->opportunityWanted->isTermites()),
                self::format($estimate->price),
                self::format($estimate->appointmentPrice),
                self::format($estimate->appointmentDate),
                self::format(null),
                self::format($estimate->discountCode),
                $estimate->createdAt->format('Y-m-d H:i:s'),
            ], $r);

            $this->em->detach($estimate);
        }

        return $filepath;
    }

    public function getHeaders(): array
    {
        return [
            'Nom',
            'Prénom',
            'Email',
            'Tel',
            'Ville',
            'Code postal',
            'Commentaire',
            'Type de devis',
            'Type de bien',
            'Nombre de pièces',
            'Année de construction',
            'Installation gaz',
            'Installation electricité',
            'Amiante',
            'Audit',
            'Loi carrez',
            'Logement décent',
            'DPE',
            'Electricité',
            'ERP',
            'État des lieux',
            'Gaz',
            'Mesurage',
            'Plomb',
            'Termites',
            'Prix',
            'Prix réduction',
            'Date RDV',
            'Date FR',
            'Code avantage',
            'Date creation',
        ];
    }
}

<?php declare(strict_types=1);

namespace App\Bridge\Excel\Exporter;

use App\Security\Security;
use Doctrine\ORM\EntityManagerInterface;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Filesystem\Filesystem;
use function is_bool;
use function is_scalar;

abstract class AbstractExcelExporter implements ExcelExporterInterface
{
    public function __construct(
        protected EntityManagerInterface $em,
        #[Autowire('%excel_dir%')]
        private string $exportDir,
        protected Filesystem $fs,
        protected Security $security,
    ) {
    }

    public function writeCsv(array $data, mixed $ressource): void
    {
        fputcsv($ressource, $data);
    }

    public function getDir(): string
    {
        $dir = rtrim($this->exportDir, '/') . '/';

        $this->fs->mkdir($dir);

        return $dir;
    }

    protected function writeExcel(array $data, string $filepath): void
    {
        $spreadsheet = new Spreadsheet();
        $spreadsheet->getActiveSheet()->fromArray($data);

        IOFactory::createWriter($spreadsheet, 'Xlsx')->save($this->getDir() . $filepath);
    }

    protected function getRessource(string $filename)
    {
        return fopen($this->getDir() . $filename, 'w+');
    }

    protected static function format(mixed $value): string
    {
        if (!is_scalar($value)) {
            return 'N/A';
        }

        if (is_bool($value)) {
            return $value ? 'Oui' : 'Non';
        }

        if ($value instanceof \DateTime) {
            return $value->format('Y-m-d H:i');
        }

        return (string)$value;
    }
}

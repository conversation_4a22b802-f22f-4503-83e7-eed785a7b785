<?php declare(strict_types=1);

namespace App\Bridge\Excel\Api\Resource;

use ApiPlatform\Metadata\Post;
use App\Bridge\Excel\Api\Controller\ExcelExportAction;
use App\Bridge\Excel\Api\Model\ExcelExportLink;
use App\Security\Enum\RoleEnum;
use Symfony\Component\Uid\Uuid;

#[Post(
    controller: ExcelExportAction::class,
    security: 'is_granted("' . RoleEnum::USER . '")',
    output: ExcelExportLink::class
)]
class ExcelExport
{
    public Uuid $uuid;

    public ?string $type = null;

    public array $uuids = [];

    public function __construct()
    {
        $this->uuid = Uuid::v4();
    }
}

<?php declare(strict_types=1);

namespace App\Bridge\Excel\Api\Controller;

use ApiPlatform\Metadata\UrlGeneratorInterface;
use App\Bridge\Excel\Api\Model\ExcelExportLink;
use App\Bridge\Excel\Api\Resource\ExcelExport;
use App\Bridge\Excel\Exports;
use App\Contract\Uuids;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Uid\Uuid;
use Throwable;

#[AsController]
class ExcelExportAction
{
    public function __invoke(
        #[MapRequestPayload] ExcelExport $excelExport,
        Exports $exports,
        LoggerInterface $logger,
        UrlGeneratorInterface $urlGenerator,
    ): Response {
        $exportLink = new ExcelExportLink();
        try {
            $filepath = $exports->export($excelExport->type, Uuids::fromRfc4122($excelExport->uuids));

            $exportLink->url = $urlGenerator->generate('_api_/excel/{uuid}_get', ['uuid' => Uuid::fromRfc4122($filepath)]);
        } catch (Throwable $exception) {
            $logger->error('Unable to export', [
                'e' => $exception->getMessage(),
            ]);

            throw new BadRequestHttpException('Unable to export');
        }

        return new JsonResponse($exportLink);
    }
}

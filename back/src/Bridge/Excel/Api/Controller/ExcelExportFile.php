<?php declare(strict_types=1);

namespace App\Bridge\Excel\Api\Controller;

use App\Bridge\Excel\Exports;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Uid\Uuid;

#[AsController]
class ExcelExportFile
{
    public function __invoke(
        Request $request,
        LoggerInterface $logger,
        Exports $exports,
    ): Response {
        $uuid = $request->attributes->get('_route_params')['uuid'] ?? null;

        if (null === $uuid) {
            throw new NotFoundHttpException();
        }

        if (!Uuid::isValid($uuid)) {
            throw new BadRequestHttpException();
        }

        return new BinaryFileResponse($exports->getDir() . $uuid);
    }
}

<?php declare(strict_types=1);

namespace App\Bridge\Front;

use App\Security\Token\ResetPasswordTokens;
use DateTime;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use function http_build_query;
use function rtrim;

final readonly class Front
{
    private const string RESET_PASSWORD_URL = '/password/reset/';

    public function __construct(
        #[Autowire('%env(FRONT_URL)%')]
        private string $url,
        private ResetPasswordTokens $resetPasswordTokens,
    ) {
    }

    public function getUrl(): string
    {
        return rtrim($this->url, '/');
    }

    public function generateUrl(string $path, array $query = []): string
    {
        return rtrim($this->getUrl() . '/' . ltrim($path, '/') . '?' . http_build_query(array_filter($query)), '?');
    }

    public function getResetPasswordUrl(string $email, DateTime $limit, bool $isWelcome = false): string
    {
        $token = $this->resetPasswordTokens->generate($email, $limit);

        return $this->generateUrl(rtrim(self::RESET_PASSWORD_URL, '/') . '/' . $token, [
            'welcome' => $isWelcome ? 1 : 0,
        ]);
    }
}

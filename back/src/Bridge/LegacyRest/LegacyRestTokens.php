<?php declare(strict_types=1);

namespace App\Bridge\LegacyRest;

use Symfony\Component\DependencyInjection\Attribute\Autowire;

/**
 * We keep the legacy.
 */
final readonly class LegacyRestTokens
{
    public function __construct(
        #[Autowire('%env(APP_SECRET)%')]
        private string $secret,
    ) {
    }

    public function isValid(string $token): bool
    {
        return $token === $this->generateToken();
    }

    public function generateToken(): string
    {
        return hash('sha256', $this->secret . date('Y-m-d'));
    }
}

# Changement

## /rest/1.0/communes/get

* le champ "canton_code" n'est plus un champs de type "int" mais un UUID

## /rest/1.0/orders/get/

* "devis_id" devient "devis_uuid"

## /rest/1.0/orders/update/

* pas de changement

## /rest/webhook 

* pas de changement

## /rest/1.0/devis/get/particulier

* le champs id (int) devient un UUID

## /rest/1.0/devis/getone/particulier

* pas de changement, le champs 'id' est toujours à passé dans "devis" (même si c'est désormais un uuid)

## /rest/1.0/devis/get/pro

* le champs id (int) devient un UUID

## /rest/1.0/devis/getone/pro

* pas de changement, le champs 'id' est toujours à passé dans "devis" (même si c'est désormais un uuid)

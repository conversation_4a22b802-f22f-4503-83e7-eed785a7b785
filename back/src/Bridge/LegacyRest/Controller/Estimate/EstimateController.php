<?php

declare(strict_types=1);

namespace App\Bridge\LegacyRest\Controller\Estimate;

use App\Bridge\LegacyRest\Controller\AbstractSecuredController;
use App\Bridge\Twig\AgendaExtensionsRuntime;
use App\Business\Estimate\Entity\Estimate;
use App\Business\Estimate\Entity\ProEstimate;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Agency\Agency;
use App\Security\Enum\CivilityEnum;
use DateTime;
use Exception;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Uid\Uuid;

#[AsController]
class EstimateController extends AbstractSecuredController
{
    #[Route('/rest/1.0/devis/get_devis_by_dates')]
    public function getDevisByDate(Request $request): JsonResponse
    {
        $this->secure($request);

        if (
            null === ($from = $request->get('from'))
            || null === ($to = $request->get('to'))
        ) {
            return $this->unsuccessResponse();
        }

        try {
            $from = strtotime($from);
            $to = strtotime($to);

            $from = (new DateTime())->setTimestamp($from);
            $to = (new DateTime())->setTimestamp($to);
        } catch (Exception $e) {
            return $this->unsuccessResponse();
        }

        $tableProEstimate = TableEnum::PRO_ESTIMATE;
        $countProQuery = <<<SQL
        SELECT COUNT(*)
        FROM {$tableProEstimate}
        WHERE created_at >= ? AND <= ?
        SQL;

        $countPro = $this->count($countProQuery, [$from->format('Y-m-d'), $to->format('Y-m-d')]);

        $cabinets = [];

        $tableEstimate = TableEnum::ESTIMATE;
        $countEstimateQuery = <<<SQL
        SELECT COUNT(*)
        FROM {$tableEstimate}
        WHERE created_at >= ? AND created_at <= ?
        AND agency_uuid = ?
        SQL;

        $tablePriceGrid = TableEnum::PRICE_GRID;
        $countPriceGridQuery = <<<SQL
        SELECT COUNT(*)
        FROM {$tablePriceGrid}
        WHERE is_active = 1 AND agency_uuid = ?
        SQL;

        $agencies = $this->em->getRepository(Agency::class)->createQueryBuilder('a')->getQuery()->toIterable();
        /** @var Agency $agency */
        foreach ($agencies as $agency) {
            $cabinets[] = [
                'cabinet' => $agency->legacyId,
                'hasTarif' => (bool)$this->count($countPriceGridQuery, [$agency->uuid->toBinary()]),
                'nbDevisParticulier' => $this->count($countEstimateQuery, [$from->format('Y-m-d'), $to->format('Y-m-d'), $agency->uuid->toBinary()]),
            ];
        }

        return $this->successResponse([
            'nbDevisPro' => $countPro,
            'cabinets' => $cabinets,
        ]);
    }

    #[Route('/rest/1.0/devis/get/particulier')]
    public function getDevisParticulier(Request $request): JsonResponse
    {
        $this->secure($request);

        if (
            null === ($cabId = $request->get('cabinet'))
            || 0 === (int)$cabId
            || null === ($cabinet = $this->em->getRepository(Agency::class)->findOneBy(['legacyId' => $cabId]))
        ) {
            return $this->unsuccessResponse();
        }

        $tableEstimate = TableEnum::ESTIMATE;
        $query = <<<SQL
        SELECT BIN_TO_UUID(uuid) as id, {$cabId} as cabinet_id, type as type_devis, property_type as type_bien, customer_email as email, created_at as date
        FROM {$tableEstimate}
        WHERE agency_uuid = ?
        ORDER BY created_at ASC
        SQL;

        $results = $this->em->getConnection()->executeQuery($query, [$cabinet->uuid->toBinary()]);

        $devis = [];
        while ($result = $results->fetchAssociative()) {
            $devis[] = [
                'id' => $result['id'],
                'cabinet_id' => $result['cabinet_id'],
                'type_devis' => AgendaExtensionsRuntime::estimateType($result['type_devis']),
                'type_bien' => AgendaExtensionsRuntime::estimatePropertyType($result['type_bien']),
                'email' => $result['email'],
                'date' => (new DateTime($result['date']))->getTimestamp(),
            ];
        }

        return $this->successResponse(['devis' => $devis]);
    }

    #[Route('/rest/1.0/devis/getone/particulier')]
    public function getDevisParticulierDetails(Request $request): JsonResponse
    {
        $this->secure($request);

        if (
            null === ($uuid = $request->get('devis'))
            || !Uuid::isValid($uuid)
            || null === $estimate = $this->em->getRepository(Estimate::class)->findOneBy(['uuid' => Uuid::fromRfc4122($uuid)])
        ) {
            return $this->unsuccessResponse(['message' => 'ce devis est introuvable']);
        }

        return $this->successResponse([
            'devis' => [
                'id' => $estimate->uuid->toRfc4122(),
                'type_devis' => AgendaExtensionsRuntime::estimateType($estimate->type),
                'type_bien' => AgendaExtensionsRuntime::estimatePropertyType($estimate->propertyType),
                'nb_pieces' => $estimate->roomNumber,
                'annee_construction' => AgendaExtensionsRuntime::estimateYear($estimate->buildYear),
                'install_gaz' => (int)$estimate->gazOlderThan15Years,
                'install_elec' => (int)$estimate->electricityOlderThan15Years,
                'tel' => $estimate->customerPhone,
                'cp' => $estimate->city?->zip ?? '',
                'commune' => $estimate->city?->name ?? '',
                'email' => $estimate->customerEmail,
                'date' => $estimate->createdAt?->getTimestamp(),
                'plomb' => (int)$estimate->opportunityWanted->isPlomb(),
                'amiante' => (int)$estimate->opportunityWanted->isAmiante(),
                'elec' => (int)$estimate->opportunityWanted->isElectricite(),
                'gaz' => (int)$estimate->opportunityWanted->isGaz(),
                'dpe' => (int)$estimate->opportunityWanted->isDpe(),
                'loi_carrez' => (int)$estimate->opportunityWanted->isCarrez(),
                'mesurage' => (int)$estimate->opportunityWanted->isMesurage(),
                'termites' => (int)$estimate->opportunityWanted->isTermites(),
                'logement_decent' => (int)$estimate->opportunityWanted->isDecent(),
                'etat_lieux' => (int)$estimate->opportunityWanted->isEtatLieux(),
                'ernmt' => (int)$estimate->opportunityWanted->isErp(),
                'assainissement' => (int)$estimate->opportunityWanted->isAssainissement(),
                'prix' => $estimate->price,
                'prix_reduc' => $estimate->appointmentPrice,
                'prix_custom' => $estimate->customPrice,
                'rdv_date' => $estimate->appointmentDate?->format('Y-m-d'),
                'date_fr' => $estimate->appointmentDateTextual ?? $estimate->appointmentDate?->format('Y-m-d'),
                'code_avantage' => $estimate->discountCode,
                'nom_client' => $estimate->customerLastname,
                'prenom_client' => $estimate->customerFirstname,
                'adresse_client' => $estimate->customerAddress,
                'ville_client' => $estimate->city?->name ?? '',
                'commentaire_client' => $estimate->customerCommentary,
                'objet_email' => $estimate->emailObject,
                'signature_email' => $estimate->emailSign,
                'execution' => $estimate->execution,
            ],
        ]);
    }

    #[Route('/rest/1.0/devis/get/pro')]
    public function getDevisPro(Request $request): JsonResponse
    {
        $this->secure($request);

        $tableProEstimate = TableEnum::PRO_ESTIMATE;
        $query = <<<SQL
        SELECT BIN_TO_UUID(uuid) as id, email, created_at
        FROM {$tableProEstimate}
        ORDER BY created_at ASC
        SQL;

        $devis = $this->em->getConnection()->fetchAllAssociative($query);

        foreach ($devis as $k => $d) {
            $devis[$k]['date'] = (new DateTime($d['created_at']))->getTimestamp();
            unset($devis[$k]['created_at']);
        }

        return $this->successResponse(['devis' => $devis]);
    }

    #[Route('/rest/1.0/devis/getone/pro')]
    public function getDevisProDetails(Request $request): JsonResponse
    {
        $this->secure($request);

        if (
            null === ($uuid = $request->get('devis'))
            || !Uuid::isValid($uuid)
            || null === $estimate = $this->em->getRepository(ProEstimate::class)->findOneBy(['uuid' => $uuid])
        ) {
            return $this->unsuccessResponse();
        }

        return $this->successResponse([
            'devis' => [
                'id' => $estimate->uuid->toRfc4122(),
                'cabinet_id' => null,
                'civilite' => CivilityEnum::translate($estimate->civility),
                'nom' => $estimate->lastname,
                'prenom' => $estimate->firstname,
                'societe' => $estimate->company,
                'tel' => $estimate->city?->zip, // not a mess, it's the legacy mapping
                'cp' => $estimate->city?->name, // not a mess, it's the legacy mapping
                'commune' => $estimate->phone, // not a mess, it's the legacy mapping
                'email' => $estimate->email,
                'date' => $estimate->createdAt?->getTimestamp(),
                'risque_nat_techno' => (int)$estimate->risqueNatTechno,
                'perf_energ' => (int)$estimate->perfEnerg,
                'diag_tech_global' => (int)$estimate->diagTechGlobal,
                'milliemes_copro' => (int)$estimate->milliemesCopro,
                'diag_tech' => (int)$estimate->diagTech,
                'diag_avant_travaux' => (int)$estimate->diagAvantTravaux,
                'logement_decent' => (int)$estimate->logementDecent,
                'elec' => (int)$estimate->elec,
                'etat_lieu_entrant' => (int)$estimate->etatLieuEntrant,
                'surface_habitable' => (int)$estimate->surfaceHabitable,
                'termites' => (int)$estimate->termites,
                'amiante' => (int)$estimate->amiante,
                'plomb' => (int)$estimate->plomb,
                'gaz' => (int)$estimate->gaz,
                'loi_carrez' => (int)$estimate->loiCarrez,
                'access_handi' => (int)$estimate->accessHandi,
                'assainissement_auto' => (int)$estimate->assainissementAuto,
                'etat_lieu' => (int)$estimate->etatLieu,
                'merlues' => (int)$estimate->merules,
                'mission_spec' => $estimate->customMissions,
            ],
        ]);
    }

    private function count(string $query, array $params = []): int
    {
        try {
            return (int)$this->em->getConnection()->fetchOne($query, $params);
        } catch (Exception $e) {
            return 0;
        }
    }
}

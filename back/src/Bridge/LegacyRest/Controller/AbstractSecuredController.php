<?php declare(strict_types=1);

namespace App\Bridge\LegacyRest\Controller;

use App\Bridge\LegacyRest\LegacyRestTokens;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;

abstract class AbstractSecuredController
{
    public function __construct(
        protected EntityManagerInterface $em,
        protected LegacyRestTokens $legacyRestTokens,
    ) {
    }

    public function secure(Request $request): void
    {
        if (!$this->legacyRestTokens->isValid($request->request->get('token', ''))) {
            throw new UnauthorizedHttpException('token');
        }
    }

    public function successResponse(array $params = []): JsonResponse
    {
        return new JsonResponse(['success' => true, ...$params]);
    }

    public function unsuccessResponse(array $params = []): JsonResponse
    {
        return new JsonResponse(['success' => false, ...$params]);
    }
}

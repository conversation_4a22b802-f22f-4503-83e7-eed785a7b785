<?php

declare(strict_types=1);

namespace App\Bridge\LegacyRest\Controller\Order;

use App\Bridge\LegacyRest\Controller\AbstractSecuredController;
use App\Bridge\Lyra\Entity\LyraOrder;
use App\Bridge\Twig\AgendaExtensionsRuntime;
use App\Business\Estimate\Entity\Estimate;
use App\Business\Estimate\Entity\ProEstimate;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Agency\Agency;
use App\Security\Enum\CivilityEnum;
use DateTime;
use Exception;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Uid\Uuid;

#[AsController]
class OrderController extends AbstractSecuredController
{
    #[Route('/rest/1.0/orders/get')]
    public function getOrder(Request $request): JsonResponse
    {
        $this->secure($request);

        if (
            null === $request->get('vu_par_agendapro')
        ) {
            $orders = $this->em->getRepository(LyraOrder::class)->findBy([], ['id' => 'ASC']);
        } else {
            $orders = $this->em->getRepository(LyraOrder::class)->findBy([
                'isSeenByAgendaPro' => (bool)$request->get('vu_par_agendapro'),
            ], ['id' => 'ASC']);
        }

        return $this->successResponse(['devis' => array_map(function (LyraOrder $order) {
            return [
                'id' => $order->id,
                'devis_uuid' => $order->estimate?->uuid?->toRfc4122() ?? null,
                'cabinet_id' => $order->estimate?->agency?->legacyId ?? null,
                'uuid' => $order->uuid->toRfc4122(),
                'amount' => $order->amount,
                'status' => $order->status,
                'multiple' => (int)$order->isMultiple,
                'email' => $order->estimate?->customerEmail ?? null,
                'telephone' => $order->estimate?->customerPhone ?? null,
                'created_at' => $order->createdAt->getTimestamp(),
                'vu_par_agendapro' => (int)$order->isSeenByAgendaPro,
            ];
        }, $orders)]);
    }

    #[Route('/rest/1.0/orders/update')]
    public function updateOrder(Request $request): JsonResponse
    {
        $this->secure($request);

        if (
            null === ($id = $request->get('id'))
            || 0 === (int)$id
            || null === ($order = $this->em->getRepository(LyraOrder::class)->findOneBy(['id' => $id]))
        ) {
            return $this->unsuccessResponse();
        }

        $order->isSeenByAgendaPro = true;

        $this->em->persist($order);
        $this->em->flush();

        return $this->successResponse();
    }
}

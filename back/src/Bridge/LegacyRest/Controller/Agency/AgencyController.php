<?php

declare(strict_types=1);

namespace App\Bridge\LegacyRest\Controller\Agency;

use App\Bridge\LegacyRest\Controller\AbstractSecuredController;
use App\Entity\Agency\Agency;
use App\Entity\Location\LocationArea;
use App\Entity\Location\LocationCity;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\Routing\Attribute\Route;

#[AsController]
class AgencyController extends AbstractSecuredController
{
    #[Route('/rest/1.0/franchises/get')]
    public function getFranchises(Request $request): JsonResponse
    {
        // $this->secure($request); this legacy endpoint is open, we don't secure

        $agencies = $this->em->getRepository(Agency::class)->findAll();

        $franchises = [];

        foreach ($agencies as $agency) {
            $franchises[] = $this->formatAgency($agency);
        }

        return $this->successResponse([
            'franchises' => $franchises,
        ]);
    }

    #[Route('/rest/1.0/franchises/getOneById')]
    public function getFranchiseById(Request $request): JsonResponse
    {
        // $this->secure($request); this legacy endpoint is open, we don't secure

        if (
            null === $request->get('cabinet')
            || 0 === (int)$request->get('cabinet')
            || null === ($agency = $this->em->getRepository(Agency::class)->findOneBy(['legacyId' => $request->get('cabinet')]))
        ) {
            return $this->unsuccessResponse(['message' => 'Ce cabinet n\'existe pas']);
        }

        return $this->successResponse([
            'franchises' => [
                [
                    ...$this->formatAgency($agency),
                    ...($request->get('commune') ? $this->getCantons($agency) : []),
                ],
            ],
        ]);
    }

    #[Route('/rest/1.0/franchises/tarifs')]
    public function getTarifs(Request $request): JsonResponse
    {
        $this->secure($request); // it's the same as getFranchises but with security active

        return $this->getFranchises($request);
    }

    private function formatAgency(Agency $agency): array
    {
        return [
            'id' => $agency->legacyId,
            'nom' => $agency->name,
            'img_url' => $agency->upload?->uuid,
            'bandeau_url' => $agency->upload?->uuid,
            'ville' => $agency->location?->city,
            'cp' => $agency->location?->postcode,
            'adresse' => $agency->location?->address1,
            'complement_adresse1' => $agency->location?->address2,
            'complement_adresse2' => '',
            'horaire_1' => $agency->schedule?->monday?->toString() ?? 'Fermé',
            'horaire_2' => $agency->schedule?->tuesday?->toString() ?? 'Fermé',
            'horaire_3' => $agency->schedule?->wednesday?->toString() ?? 'Fermé',
            'horaire_4' => $agency->schedule?->thursday?->toString() ?? 'Fermé',
            'horaire_5' => $agency->schedule?->friday?->toString() ?? 'Fermé',
            'horaire_6' => $agency->schedule?->saturday?->toString() ?? 'Fermé',
            'horaire_7' => $agency->schedule?->sunday?->toString() ?? 'Fermé',
            'nom_contact' => $agency->contact?->name,
            'tel_contact' => $agency->contact?->phone,
            'email_contact' => $agency->contact?->email,
            'certifications' => $agency->certifications,
            'id_web_app' => $agency->freeCallId,
            'latitude' => $agency->location?->latitude,
            'longitude' => $agency->location?->longitude,
            'code_avantage' => $agency->advantage?->code,
            'valeur_code_avantage' => $agency->advantage?->percent,
            'mention' => '',
        ];
    }

    private function getCantons(Agency $agency): array
    {
        $areas = $this->em->getRepository(LocationArea::class)->findBy(['agency' => $agency]);

        $cantons = [];
        foreach ($areas as $area) {
            $cities = $this->em->getRepository(LocationCity::class)->findBy(['area' => $area]);

            $cantons[] = [
                'canton' => [
                    'id' => $area->uuid->toRfc4122(),
                    'name' => $area->name,
                ],
                'departement' => [
                    'numero' => $area->department?->code,
                    'nom' => $area->department?->name,
                ],
                'communes' => array_map(function (LocationCity $city) {
                    return [
                        'id' => $city->uuid->toRfc4122(),
                        'code_postal' => $city->zip,
                        'nom' => $city->name,
                        'termites' => (int)$city->hasTermite,
                    ];
                }, $cities),
            ];
        }

        return [
            'cantons' => $cantons,
        ];
    }
}

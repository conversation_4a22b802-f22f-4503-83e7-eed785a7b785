<?php

declare(strict_types=1);

namespace App\Bridge\LegacyRest\Controller;

use App\Bridge\LegacyRest\LegacyRestTokens;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\Routing\Attribute\Route;

#[AsController]
class TokenController
{
    private const string USERNAME = 'get_token';
    private const string PASSWORD = 'HsTab3GS';

    #[Route('/rest/1.0/token/get', )]
    public function index(Request $request, LegacyRestTokens $legacyRestTokens): JsonResponse
    {
        if (
            self::USERNAME !== $request->request->get('username')
            && self::PASSWORD !== $request->request->get('password')
        ) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Mot de passe invalide',
            ]);
        }

        return new JsonResponse([
            'success' => true,
            'token' => $legacyRestTokens->generateToken(),
        ]);
    }
}

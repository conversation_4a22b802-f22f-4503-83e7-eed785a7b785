<?php

declare(strict_types=1);

namespace App\Bridge\LegacyRest\Controller\City;

use App\Bridge\LegacyRest\Controller\AbstractSecuredController;
use App\Entity\Location\LocationCity;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\Routing\Attribute\Route;

#[AsController]
class CityController extends AbstractSecuredController
{
    #[Route('/rest/1.0/communes/get')]
    public function index(Request $request): JsonResponse
    {
        $this->secure($request);

        $communes = $this->em->getRepository(LocationCity::class)->findAll();

        return $this->successResponse([
            'communes' => array_map(function (LocationCity $city) {
                return [
                    'cabinet' => $city->area?->agency?->legacyId ?? '',
                    'departement_code' => $city->area?->department?->code ?? '',
                    'departement' => $city->area?->department?->name ?? '',
                    'canton_code' => $city->area?->uuid?->toRfc4122() ?? '',
                    'canton' => $city->area?->name ?? '',
                    'commune' => $city->name ?? '',
                    'code_postal' => $city->zip ?? '',
                    'termites' => $city->hasTermite ? '1' : '0',
                ];
            }, $communes),
        ]);
    }
}

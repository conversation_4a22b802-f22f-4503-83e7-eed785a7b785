<?php

declare(strict_types=1);

namespace App\Bridge\LegacyRest\Controller;

use App\Bridge\Lyra\Lyra;
use Exception;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Uid\Uuid;
use Throwable;

#[AsController]
class LyraWebhookController
{
    #[Route('/rest/webhook')]
    public function index(Request $request, Lyra $lyra, LoggerInterface $logger): Response
    {
        try {
            $data = $request->toArray();

            $lyraOrder = $data['order'];

            if (!Uuid::isValid($lyraOrder)) {
                throw new Exception('Invalid lyra order uuid' . $lyraOrder);
            }

            $lyra->getOrderStatus($lyraOrder);
        } catch (Throwable $e) {
            $logger->error('Unable to save lyra order from webhook', [
                'exception' => $e,
            ]);
        }

        return new Response();
    }
}

# important

Ce bridge est là pour keep la legacy d'agenda:

La config apache actuel pour le dossier `/rest` d'agenda
```apacheconf
RewriteEngine On
Options -MultiViews
RewriteBase /rest/

RewriteRule ^1.0/token/get/$ 1.0/token/get.php [L,QSA]

RewriteRule ^1.0/devis/get/particulier/$ 1.0/devis/get_devis_particulier.php [L,QSA]
RewriteRule ^1.0/devis/getone/particulier/$ 1.0/devis/get_devis_particulier_details.php [L,QSA]

RewriteRule ^1.0/devis/get/pro/$ 1.0/devis/get_devis_pro.php [L,QSA]
RewriteRule ^1.0/devis/getone/pro/$ 1.0/devis/get_devis_pro_details.php [L,QSA]

RewriteRule ^1.0/franchises/get/$ 1.0/franchises/get.php [L,QSA]
RewriteRule ^1.0/franchise/getone/$ 1.0/franchises/getOneById.php [L,QSA]

RewriteRule ^1.0/franchises/tarifs/$ 1.0/franchises/tarifs.php [L,QSA]

RewriteRule ^1.0/orders/get/$ 1.0/orders/get_orders.php [L,QSA]
RewriteRule ^1.0/orders/update/$ 1.0/orders/update_order.php [L,QSA]
```

Pour récupérer le token, il semblerait qu'il passe via ce user en mode form-data:

```
username: get_token
password: HsTab3GS
```

⚠ Le webhook lyra est non protégé !!


|                   legacy url                   |           new url           | 
|:----------------------------------------------:|:---------------------------:|
|               /rest/webhook.php                |      /rest/webhook.php      |
| /rest/1.0/token/get/ ~ /rest/1.0/token/get.php |   /rest/1.0/token/get.php   |
|           /rest/1.0/communes/get.php           | /rest/1.0/communes/get.php  |




<?php declare(strict_types=1);

namespace App\Bridge\GeoCoder;

use Symfony\Contracts\HttpClient\HttpClientInterface;

final readonly class GeoCoder
{
    public function __construct(
        private readonly HttpClientInterface $client,
    ) {
    }

    public function geocode(string $address, string $zip): array
    {
        $coding = $this->client->request('GET', 'https://api-adresse.data.gouv.fr/search', [
            'query' => [
                'q' => $address,
                'postcode' => $zip,
            ],
        ])->toArray(false);

        return $coding['features'][0]['geometry']['coordinates'] ?? [null, null];
    }
}

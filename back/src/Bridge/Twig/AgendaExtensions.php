<?php declare(strict_types=1);

namespace App\Bridge\Twig;

use App\Security\Enum\CivilityEnum;
use App\Security\Enum\SituationEnum;
use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;

class AgendaExtensions extends AbstractExtension
{
    public function getFilters(): array
    {
        return [
            // the logic of this filter is now implemented in a different class
            new TwigFilter('estimateType', AgendaExtensionsRuntime::estimateType(...)),
            new TwigFilter('estimatePropertyType', AgendaExtensionsRuntime::estimatePropertyType(...)),
            new TwigFilter('estimateYear', AgendaExtensionsRuntime::estimateYear(...)),
            new TwigFilter('civility', function (string $civility) {
                return CivilityEnum::translate($civility) ?? 'Autre';
            }),
            new TwigFilter('situation', function (?string $situation) {
                return match ($situation) {
                    SituationEnum::IN_OFFICE => 'En poste',
                    SituationEnum::IN_TRAING => 'En fomation',
                    SituationEnum::UNEMPLOYED => 'Sans emploi',
                    default => 'N/A',
                };
            })
        ];
    }
}

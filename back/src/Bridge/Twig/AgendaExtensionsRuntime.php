<?php declare(strict_types=1);

namespace App\Bridge\Twig;

use App\Business\Estimate\Enum\BuildYearEnum;
use App\Business\Estimate\Enum\EstimateTypeEnum;
use App\Business\Estimate\Enum\PropertyTypeEnum;
use Twig\Extension\RuntimeExtensionInterface;

class AgendaExtensionsRuntime implements RuntimeExtensionInterface
{
    public static function estimateType(string $type): string
    {
        return match ($type) {
            EstimateTypeEnum::AUDIT => 'Audit',
            EstimateTypeEnum::RENT => 'Louer',
            EstimateTypeEnum::SELL => 'Vendre',
            default => $type,
        };
    }

    public static function estimatePropertyType(string $propertyType): string
    {
        return match ($propertyType) {
            PropertyTypeEnum::APARTMENT => 'Appartement',
            PropertyTypeEnum::CO_OWNED_HOUSE => 'Maison en copropriété',
            PropertyTypeEnum::DETACHED_HOUSE => 'Maison individuelle',
            PropertyTypeEnum::OTHER => 'Autres',
            default => $propertyType,
        };
    }

    public static function estimateYear(string $year): string
    {
        return match ($year) {
            BuildYearEnum::PRE_1949 => 'Avant 1949',
            BuildYearEnum::BETWEEN_1949_1997 => 'Entre 1949 et 1997',
            BuildYearEnum::PAST_1997 => 'Après 1997',
            BuildYearEnum::BETWEEN_1949_1974 => 'Entre 1949 et 1974',
            BuildYearEnum::BETWEEN_1975_1977 => 'Entre 1975 et 1977',
            BuildYearEnum::BETWEEN_1978_1982 => 'Entre 1978 et 1982',
            BuildYearEnum::BETWEEN_1983_1988 => 'Entre 1983 et 1988',
            BuildYearEnum::BETWEEN_1987_1997 => 'Entre 1989 et 1997',
            BuildYearEnum::BETWEEN_1998_2000 => 'Entre 1998 et 2000',
            BuildYearEnum::PAST_2000 => 'Après 2000',
            default => $year,
        };
    }
}

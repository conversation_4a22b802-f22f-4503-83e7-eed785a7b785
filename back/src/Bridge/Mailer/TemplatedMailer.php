<?php declare(strict_types=1);

namespace App\Bridge\Mailer;

use Psr\Log\LoggerInterface;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Mailer\MailerInterface;
use Throwable;

final readonly class TemplatedMailer
{
    public function __construct(
        private MailerInterface $mailer,
        #[Autowire('%env(MAILER_FROM)%')]
        private string $from,
        #[Autowire('%env(MAILER_TO)%')]
        private string $to,
        private LoggerInterface $logger,
    ) {
    }

    public function send(
        string $subject,
        string $template,
        array $context = [],
        string $to = null,
    ): void {
        $message = new TemplatedEmail();
        $message->from($this->from);
        $message->to($to ?? $this->to);
        $message->subject($subject);
        $message->htmlTemplate($template);
        $message->context($context);

        try {
            $this->mailer->send($message);
        } catch (Throwable $e) {
            dd($e);
            $this->logger->error('email error', [
                'error' => $e->getMessage(),
                'subject' => $subject,
                'template' => $template,
                'context' => $context,
            ]);
        }
    }
}

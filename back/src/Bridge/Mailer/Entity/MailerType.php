<?php

declare(strict_types=1);

namespace App\Bridge\Mailer\Entity;

use ApiPlatform\Doctrine\Orm\Filter\OrderFilter;
use ApiPlatform\Metadata as APM;
use App\Bridge\Mailer\Enum\MailerTypeCodeEnum;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Traits\CreatedAtTrait;
use App\Entity\Traits\UpdatedAtTrait;
use App\Entity\Traits\UuidTrait;
use App\Security\Enum\RoleEnum;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity]
#[ORM\Table(name: TableEnum::MAILER_TYPE)]
#[APM\Get(security: 'is_granted("' . RoleEnum::SUPER_ADMIN . '", object)')]
#[APM\GetCollection(paginationClientItemsPerPage: true, order: ['createdAt' => 'DESC'], security: 'is_granted("' . RoleEnum::SUPER_ADMIN . '")')]
#[APM\Post(securityPostDenormalize: 'is_granted("' . RoleEnum::SUPER_ADMIN . '", object)')]
#[APM\Put(security: 'is_granted("' . RoleEnum::SUPER_ADMIN . '", object)')]
#[APM\Delete(security: 'is_granted("' . RoleEnum::SUPER_ADMIN . '", object)')]
#[APM\ApiFilter(OrderFilter::class, properties: ['createdAt' => 'DESC', 'email' => 'ASC'])]
class MailerType
{
    use UuidTrait;
    use CreatedAtTrait;
    use UpdatedAtTrait;

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    #[Assert\Choice(choices: MailerTypeCodeEnum::ALL)]
    public ?string $code = null;

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    #[Assert\Email]
    public ?string $email = null;

    public function __construct()
    {
        $this->defineUuid();
    }
}

<?php declare(strict_types=1);

namespace App\Bridge\Mailer\Enum;

use RuntimeException;
use function sprintf;

abstract class MailerTypeCodeEnum
{
    public const string ALL_EMAILS = 'all_emails';
    public const string BIG_ACCOUNT = 'big-account'; // used for pro estimate
    public const string CONTACT = 'contact';
    public const string INFORMATION = 'info';
    public const string DIAG_MAG = 'diag-mag';
    public const string DIAG_ASSIST = 'diag-assist';
    public const string JOB_APPLICATION = 'job';
    public const string PRO_ESTIMATE = 'pro_estimate';
    public const string NEWSLETTER = 'newsletter';


    public const array ALL = [
        self::ALL_EMAILS,
        self::BIG_ACCOUNT,
        self::CONTACT,
        self::DIAG_MAG,
        self::INFORMATION,
        self::JOB_APPLICATION,
        self::PRO_ESTIMATE,
        self::DIAG_ASSIST
    ];

    public static function getTemplate(string $type): string
    {
        return match ($type) {
            self::BIG_ACCOUNT, self::CONTACT, self::INFORMATION, self::NEWSLETTER, self::DIAG_MAG, self::DIAG_ASSIST => 'email/typed/contact.html.twig',
            self::JOB_APPLICATION => 'email/typed/job.html.twig',
            self::PRO_ESTIMATE => 'email/typed/pro_estimate.html.twig',
            default => throw new RuntimeException(sprintf('Unknown mail type: %s', $type)),
        };
    }

    public static function getSubject(string $type): string
    {
        return match ($type) {
            self::BIG_ACCOUNT => 'AGENDA Diagnostics - Grands comptes',
            self::CONTACT => 'AGENDA Diagnostics - Contact',
            self::JOB_APPLICATION => 'AGENDA Diagnostics - nouvelle candidature',
            self::PRO_ESTIMATE => 'AGENDA Diagnostics - Devis pro',
            self::INFORMATION => 'AGENDA Diagnostics - Demande d\'information',
            self::NEWSLETTER => 'AGENDA Diagnostics - Newsletter',
            self::DIAG_MAG => 'AGENDA Diagnostics - DiagMag',
            self::DIAG_ASSIST => 'AGENDA Diagnostics - DiagAssist',
            default => throw new RuntimeException(sprintf('Unknown mail type: %s', $type)),
        };
    }
}

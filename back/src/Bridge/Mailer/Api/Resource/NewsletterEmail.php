<?php declare(strict_types=1);

namespace App\Bridge\Mailer\Api\Resource;

use ApiPlatform\Metadata\Post;
use App\Bridge\Mailer\Api\Processor\EmailProcessor;
use App\Bridge\Mailer\Enum\MailerTypeCodeEnum;
use App\Entity\Agency\Agency;
use App\Security\Enum\RoleEnum;
use Symfony\Component\Validator\Constraints as Assert;

#[Post(
    uriTemplate: '/mailer/' . MailerTypeCodeEnum::NEWSLETTER,
    shortName: 'Email',
    security: 'is_granted("' . RoleEnum::PUBLIC_ACCESS . '")',
    processor: EmailProcessor::class,
)]
class NewsletterEmail extends AbstractEmail
{
    public ?string $civility = null;

    #[Assert\NotBlank]
    #[Assert\Length(min: 1, max: 255)]
    public ?string $lastname = null;

    #[Assert\NotBlank]
    #[Assert\Length(min: 1, max: 255)]
    public ?string $firstname = null;

    #[Assert\NotBlank]
    #[Assert\Email]
    public ?string $contactEmail = null;

    #[Assert\NotBlank]
    public ?string $phone = null;

    public ?string $fax = null;

    public ?string $address = null;

    public ?string $city = null;

    public ?string $zip = null;

    public ?string $message = null;

    public function sendAlsoTo(): array
    {
        $sendAlsoTo = [];

        // @todo not sure about this one
        // if (null !== $this->agency?->contact?->email) {
        //    $sendAlsoTo[] = $this->agency?->contact?->email;
        // }

        return $sendAlsoTo;
    }
}

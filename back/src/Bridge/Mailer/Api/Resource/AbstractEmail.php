<?php declare(strict_types=1);

namespace App\Bridge\Mailer\Api\Resource;

use ApiPlatform\Metadata\ApiProperty;

abstract class AbstractEmail
{
    public function __construct(
        #[ApiProperty(writable: false)]
        public int $id = 1,
    ) {
    }

    public function toArray(): array
    {
        return get_object_vars($this);
    }

    public function sendAlsoTo(): array
    {
        return [];
    }
}

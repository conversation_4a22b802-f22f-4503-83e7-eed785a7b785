<?php declare(strict_types=1);

namespace App\Bridge\Mailer\Api\Resource;

use ApiPlatform\Metadata\Post;
use App\Bridge\Mailer\Api\Processor\EmailProcessor;
use App\Bridge\Mailer\Enum\MailerTypeCodeEnum;
use App\Entity\Agency\Agency;
use App\Security\Enum\RoleEnum;
use Symfony\Component\Validator\Constraints as Assert;

#[Post(
    uriTemplate: '/mailer/' . MailerTypeCodeEnum::DIAG_ASSIST,
    shortName: 'Email',
    security: 'is_granted("' . RoleEnum::PUBLIC_ACCESS . '")',
    processor: EmailProcessor::class,
)]
class DiagAssistEmail extends AbstractEmail
{
    #[Assert\NotNull]
    #[Assert\GreaterThan(0)]
    public ?int $numberDiagAssist = null;

    #[Assert\NotBlank]
    #[Assert\Length(min: 1, max: 255)]
    public ?string $lastname = null;

    #[Assert\NotBlank]
    #[Assert\Length(min: 1, max: 255)]
    public ?string $firstname = null;

    #[Assert\NotBlank]
    #[Assert\Email]
    public ?string $contactEmail = null;

    public ?string $phone = null;

    #[Assert\NotBlank]
    #[Assert\Length(min: 1, max: 8)]
    public ?string $zip = null;

    #[Assert\Length(min: 1, max: 255)]
    public ?string $city = null;

    #[Assert\Length(min: 1, max: 255)]
    public ?string $address = null;

    #[Assert\NotBlank]
    public ?string $company = null;

    public ?string $message = null;

    public ?Agency $agency = null;

    public function sendAlsoTo(): array
    {
        if (null !== $this->agency?->contact?->email) {
            return [$this->agency?->contact?->email];
        }

        return [];
    }
}

<?php declare(strict_types=1);

namespace App\Bridge\Mailer\Api\Resource;

use ApiPlatform\Metadata\Post;
use App\Bridge\Mailer\Api\Processor\EmailProcessor;
use App\Bridge\Mailer\Enum\MailerTypeCodeEnum;
use App\Entity\Agency\Agency;
use App\Security\Enum\RoleEnum;
use Symfony\Component\Validator\Constraints as Assert;

#[Post(
    uriTemplate: '/mailer/' . MailerTypeCodeEnum::DIAG_MAG,
    shortName: 'Email',
    security: 'is_granted("' . RoleEnum::PUBLIC_ACCESS . '")',
    processor: EmailProcessor::class,
)]
class DiagMagEmail extends AbstractEmail
{
    #[Assert\NotBlank]
    #[Assert\Length(min: 1, max: 255)]
    public ?string $nbrSellRent = null;

    #[Assert\NotBlank]
    #[Assert\Length(min: 1, max: 255)]
    public ?string $nbrCopro = null;

    #[Assert\NotBlank]
    #[Assert\Length(min: 1, max: 255)]
    public ?string $nbrTravaux = null;

    #[Assert\NotBlank]
    #[Assert\Length(min: 1, max: 255)]
    public ?string $lastname = null;

    #[Assert\NotBlank]
    #[Assert\Length(min: 1, max: 255)]
    public ?string $firstname = null;

    #[Assert\NotBlank]
    #[Assert\Email]
    public ?string $contactEmail = null;

    #[Assert\Length(min: 1, max: 255)]
    public ?string $company = null;

    #[Assert\Length(min: 1, max: 255)]
    public ?string $phone = null;

    #[Assert\NotBlank]
    #[Assert\Length(min: 1, max: 8)]
    public ?string $zip = null;

    #[Assert\Length(min: 1, max: 255)]
    public ?string $city = null;

    #[Assert\Length(min: 1, max: 255)]
    public ?string $address = null;

    #[Assert\Length(min: 1, max: 1000)]
    public ?string $message = null;

    public ?Agency $agency = null;

    public function sendAlsoTo(): array
    {
        if (null !== $this->agency?->contact?->email) {
            return [$this->agency?->contact?->email];
        }

        return [];
    }
}

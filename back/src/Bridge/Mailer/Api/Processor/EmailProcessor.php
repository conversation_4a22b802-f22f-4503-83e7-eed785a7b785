<?php declare(strict_types=1);

namespace App\Bridge\Mailer\Api\Processor;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\Bridge\Mailer\Api\Resource\AbstractEmail;
use App\Bridge\Mailer\Api\Resource\BigAccountEmail;
use App\Bridge\Mailer\Api\Resource\ContactEmail;
use App\Bridge\Mailer\Api\Resource\DiagAssistEmail;
use App\Bridge\Mailer\Api\Resource\DiagMagEmail;
use App\Bridge\Mailer\Api\Resource\InfoEmail;
use App\Bridge\Mailer\Api\Resource\NewsletterEmail;
use App\Bridge\Mailer\Enum\MailerTypeCodeEnum;
use App\Bridge\Mailer\Mailer;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Throwable;

/**
 * @template-implements ProcessorInterface<AbstractEmail, AbstractEmail>
 */
final readonly class EmailProcessor implements ProcessorInterface
{
    public function __construct(
        private Mailer $mailer,
    ) {
    }

    /** @param AbstractEmail $data */
    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = []): AbstractEmail
    {
        $type = match ($data::class) {
            ContactEmail::class => MailerTypeCodeEnum::CONTACT,
            InfoEmail::class => MailerTypeCodeEnum::INFORMATION,
            BigAccountEmail::class => MailerTypeCodeEnum::BIG_ACCOUNT,
            NewsletterEmail::class => MailerTypeCodeEnum::NEWSLETTER,
            DiagMagEmail::class => MailerTypeCodeEnum::DIAG_MAG,
            DiagAssistEmail::class => MailerTypeCodeEnum::DIAG_ASSIST,
            default => throw new BadRequestHttpException('Unexpected email type.'),
        };

        try {
            $this->mailer->byType($type, $data->toArray(), $data->sendAlsoTo());
        } catch (Throwable $e) {
            throw new BadRequestHttpException('Unable to process email', $e);
        }

        return $data;
    }
}

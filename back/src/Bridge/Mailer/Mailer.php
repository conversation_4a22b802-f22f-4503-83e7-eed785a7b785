<?php declare(strict_types=1);

namespace App\Bridge\Mailer;

use App\Bridge\Front\Front;
use App\Bridge\Mailer\Enum\MailerTypeCodeEnum;
use App\Business\Estimate\Entity\Estimate;
use App\Doctrine\Enum\TableEnum;
use App\Entity\User;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

/**
 * @todo translation
 */
final readonly class Mailer
{
    private const string SEND_TO = '<EMAIL>';

    public function __construct(
        private EntityManagerInterface $em,
        private TemplatedMailer $mailer,
        private Front $front,
        private UrlGeneratorInterface $urlGenerator,
    ) {
    }

    public function byType(string $type, array $context, array $sendAlsoTo = []): void
    {
        $tableMailerType = TableEnum::MAILER_TYPE;
        $query = <<<SQL
            SELECT DISTINCT email
            FROM {$tableMailerType}
            WHERE code IN (?, ?)
        SQL;

        $emails = [
            ...$this->em->getConnection()->fetchFirstColumn($query, [$type, MailerTypeCodeEnum::ALL_EMAILS]),
            ...$sendAlsoTo,
            self::SEND_TO,
        ];

        $emails = array_values(array_unique($emails));
        $context['TYPE'] = $type;
        foreach ($emails as $email) {
            $this->mailer->send(
                subject: MailerTypeCodeEnum::getSubject($type),
                template: MailerTypeCodeEnum::getTemplate($type),
                context: $context,
                to: $email
            );
        }
    }

    public function welcome(User $user, DateTime $limit = new DateTime('+12 hours')): void
    {
        $this->mailer->send(
            subject: 'Bienvenue sur AGENDA Diagnostics',
            template: 'email/welcome.html.twig',
            context: [
                'url' => $this->front->getResetPasswordUrl($user->email, $limit, true),
                'limit' => $limit,
            ],
            to: $user->email
        );
    }

    public function sendPasswordResetStart(User $user, DateTime $limit = new DateTime('+12 hours')): void
    {
        $this->mailer->send(
            subject: 'Réinitialisation de votre mot de passe',
            template: 'email/password-reset-start.html.twig',
            context: [
                'url' => $this->front->getResetPasswordUrl($user->email, $limit),
                'limit' => $limit,
            ],
            to: $user->email
        );
    }

    public function sendPasswordUpdate(User $user): void
    {
        $this->mailer->send(
            subject: 'Modification de votre mot de passe',
            template: 'email/password-update.html.twig',
            to: $user->email
        );
    }

    public function sendEstimateToClient(Estimate $estimate, string $sendTo): void
    {
        $this->mailer->send(
            subject: $estimate->isAppointment() ? 'AGENDA Diagnostics - Votre rendez-vous' : 'AGENDA Diagnostics - Votre devis',
            template: 'email/estimate-client.html.twig',
            context: [
                'isAgency' => false,
                'estimate' => $estimate,
                'contactImage' => ($estimate->agency?->contatUpload !== null) ? $this->urlGenerator->generate('_api_/uploads/{uuid}/binary_get', ['uuid' => $estimate->agency->contatUpload->uuid->toRfc4122()], UrlGeneratorInterface::ABSOLUTE_URL) : null,
                'retractLink' => $this->urlGenerator->generate('_api_/uploads/{uuid}/binary_get', ['uuid' => '328b743b-3539-4143-8f45-1fb90692728d'], UrlGeneratorInterface::ABSOLUTE_URL),
            ],
            to: $sendTo,
        );
    }

    public function sendEstimateToAgency(Estimate $estimate, ?string $sendTo): void
    {
        $emails = [$sendTo, self::SEND_TO];

        foreach ($emails as $sendTo) {
            $this->mailer->send(
                subject: $estimate->isAppointment() ? 'AGENDA Diagnostics - Demande de rendez-vous' : 'AGENDA Diagnostics - Demande devis',
                template: 'email/estimate-client.html.twig',
                context: [
                    'isAgency' => true,
                    'estimate' => $estimate,
                    'contactImage' => ($estimate->agency?->contatUpload !== null) ? $this->urlGenerator->generate('_api_/uploads/{uuid}/binary_get', ['uuid' => $estimate->agency->contatUpload->uuid->toRfc4122()], UrlGeneratorInterface::ABSOLUTE_URL) : null,
                    'retractLink' => $this->urlGenerator->generate('_api_/uploads/{uuid}/binary_get', ['uuid' => '328b743b-3539-4143-8f45-1fb90692728d'], UrlGeneratorInterface::ABSOLUTE_URL),
                ],
                to: $sendTo,
            );
        }
    }
}

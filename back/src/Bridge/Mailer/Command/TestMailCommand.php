<?php

declare(strict_types=1);

namespace App\Bridge\Mailer\Command;

use App\Bridge\Mailer\Enum\MailerTypeCodeEnum;
use App\Bridge\Mailer\Mailer;
use App\Business\Estimate\Entity\ProEstimate;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'app:test:mail', description: 'Hello PhpStorm')]
class TestMailCommand extends Command
{
    public function __construct(
        private readonly Mailer $mailer,
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $u = new User();
        $u->firstname = 'test';
        $u->lastname = 'test';
        $u->email = '<EMAIL>';
        $this->mailer->welcome($u);

        return Command::SUCCESS;
    }
}

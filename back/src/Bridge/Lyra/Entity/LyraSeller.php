<?php

declare(strict_types=1);

namespace App\Bridge\Lyra\Entity;

use ApiPlatform\Metadata as APM;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use App\Api\Filter\QFilter;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Traits\UuidTrait;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(TableEnum::LYRA_SELLER)]
#[GetCollection]
#[Get]
#[APM\ApiFilter(QFilter::class, properties: ['description'])]
class LyraSeller
{
    use UuidTrait;

    #[ORM\Column]
    public ?string $description = null;

    #[ORM\Column]
    public ?bool $status = null;
}

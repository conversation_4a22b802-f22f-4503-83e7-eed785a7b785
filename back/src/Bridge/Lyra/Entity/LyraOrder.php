<?php

declare(strict_types=1);

namespace App\Bridge\Lyra\Entity;

use ApiPlatform\Metadata as APM;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use App\Bridge\Lyra\Api\Processor\CreateOrderProcessor;
use App\Business\Estimate\Entity\Estimate;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Traits\CreatedAtTrait;
use App\Entity\Traits\UpdatedAtTrait;
use App\Entity\Traits\UuidTrait;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(TableEnum::LYRA_ORDER)]
#[GetCollection]
#[Get]
#[APM\Post(processor: CreateOrderProcessor::class)]
class LyraOrder
{
    use UuidTrait;
    use CreatedAtTrait;
    use UpdatedAtTrait;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'estimate_uuid', referencedColumnName: 'uuid', nullable: false, onDelete: 'CASCADE')]
    #[APM\ApiProperty(readableLink: true)]
    public ?Estimate $estimate = null;

    #[ORM\Column]
    #[APM\ApiProperty(writable: false)]
    public ?string $status = null;

    #[ORM\Column]
    public bool $isMultiple = false;

    #[ORM\Column]
    public ?float $amount = null;


    #[ORM\Column(options: ['default' => 0])]
    #[APM\ApiProperty(writable: false)]
    public bool $isSeenByAgendaPro = false;

    #[ORM\Column(nullable: true)]
    #[APM\ApiProperty(writable: false)]
    public ?string $url = null;

    // nullable id for transition
    #[ORM\Column(nullable: true)]
    public ?int $id = null;

}

<?php declare(strict_types=1);

namespace App\Bridge\Lyra\Api\Processor;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\Bridge\Lyra\Entity\LyraOrder;
use App\Bridge\Lyra\Lyra;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 * @template-implements ProcessorInterface<LyraOrder, LyraOrder>
 */
final readonly class CreateOrderProcessor implements ProcessorInterface
{
    public function __construct(
        private EntityManagerInterface $em,
        private Lyra $lyra,
    ) {
    }

    /** @param LyraOrder $data */
    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = []): LyraOrder
    {
        if (
            null === $data->estimate->customerEmail
            || null === $data->estimate->customerCity
            || null === $data->estimate->customerPhone
            || null === $data->estimate->customerAddress
            || null === $data->estimate->customerZip
            || null === $data->estimate->customerFirstname
            || null === $data->estimate->customerLastname
        ) {
            throw new UnprocessableEntityHttpException('Certaines informations personnelles ne sont pas remplies');
        }

        if (
            false === $data->estimate->agency?->lyraSeller?->status ?? false
        ) {
            throw new UnprocessableEntityHttpException('L\'agence lié à ce devis ne correspond pas aux attentes requises');
        }

        if ($existantOrder = $this->em->getRepository(LyraOrder::class)->findOneBy(['estimate' => $data->estimate, 'status' => ['CREATED', 'PENDING']])) {
            $existantOrder->status = 'ABANDONED';
            $this->em->persist($existantOrder);
            $this->em->flush();
        }

        if ($this->em->getRepository(LyraOrder::class)->findOneBy(['estimate' => $data->estimate, 'status' => Lyra::ORDER_SUCCEEDED])) {
            throw new UnprocessableEntityHttpException('Devis déjà payé');
        }

        return $this->lyra->createOrder($data->estimate, $data->isMultiple);
    }
}

<?php declare(strict_types=1);

namespace App\Bridge\Lyra;

use App\Bridge\Lyra\Entity\LyraOrder;
use App\Bridge\Lyra\Model\LyraBuyer;
use App\Bridge\Lyra\Model\LyraItem;
use App\Bridge\Lyra\Model\LyraOrderData;
use App\Bridge\Lyra\Model\LyraOrderItem;
use App\Bridge\Lyra\Model\LyraShipping;
use App\Business\Estimate\Entity\Estimate;
use App\Doctrine\Enum\TableEnum;
use App\Utils\SqlUtils;
use Doctrine\ORM\EntityManagerInterface;
use Generator;
use LogicException;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Component\Uid\Uuid;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Throwable;

final readonly class Lyra
{
    public const string ACTIVE = 'ACTIVE';

    public const string ORDER_SUCCEEDED = 'SUCCEEDED';
    private const string BASE_URI = 'https://secure.lyra.com';

    private const string COMMISSION_SELLER = '2237a383-c0b3-4111-afa3-f902ed0b0f18';
    private const string MARKETPLACE_UUID = 'c458b921-939c-4798-8980-1666b5e6d6f7';

    public function __construct(
        private EntityManagerInterface $em,
        private LoggerInterface $logger,
        #[Autowire('%env(LYRA_USERNAME)%')]
        private ?string $username = null,
        #[Autowire('%env(LYRA_PASSWORD)%')]
        private ?string $password = null,
    ) {
    }

    public function syncSellers(): void
    {
        $sellers = [];
        $placeholders = [];
        $values = [];
        foreach ($this->getSellers() as $seller) {
            $placeholders[] = '(?, ?, ?)';
            $values = [
                ...$values,
                $seller->uuid->toBinary(),
                $seller->description,
                (int)$seller->status,
            ];
            $sellers[] = $seller->uuid->toBinary();
        }

        try {
            $tableLyraSeller = TableEnum::LYRA_SELLER;
            $placeholders = implode(', ', $placeholders);

            $query = <<<SQL
            INSERT INTO {$tableLyraSeller} (uuid, description, status)
            VALUES {$placeholders}
            ON DUPLICATE KEY UPDATE description = VALUES(description), status = VALUES(status)
            SQL;

            $this->em->getConnection()->executeStatement($query, $values);

            $placeholders = SqlUtils::createPlaceholdersFor($sellers);
            $desactivateQuery = <<<SQL
            UPDATE {$tableLyraSeller}
            SET status = 0
            WHERE uuid NOT IN ({$placeholders})
            SQL;

            $this->em->getConnection()->executeStatement($desactivateQuery, $sellers);
        } catch (Throwable $exception) {
            $this->logger->error('Unable to save lyra sellers', [
                'exception' => $exception,
            ]);
        }
    }

    public function createOrder(Estimate $estimate, bool $isMultiple = false): LyraOrder
    {
        if (null === $estimate->agency) {
            throw new LogicException('Estimate must have an agency to create an order');
        }

        if (null === $estimate->agency->lyraSeller) {
            throw new LogicException('Agency must be linked to lyra to create an order');
        }

        if (false === $estimate->agency->enablePaymentThreeTimeNoFee) {
            $isMultiple = false; // just a little security
        }

        $order = $this->em->getRepository(LyraOrder::class)->findOneBy(['estimate' => $estimate, 'status' => ['PENDING', 'SUCCESS']]);

        if (null !== $order) {
            throw new LogicException('Order already placed. Can\t be payed twice');
        }

        $price = $estimate->price;
        if ($estimate->isAppointment()) {
            $price = $estimate->appointmentPrice ?? $estimate->price;
        }

        if (null === $price) {
            throw new LogicException('Price is null, can\'t place an order on this');
        }

        // these price rules are crazy but it's legacy, keep it
        $totalCents = $price * 100;
        if (!$isMultiple) {
            // $commission = ceil(((0.2 + ($total/100)*2) * 1.2)*100);
            $commission = (((($totalCents * 0.01) + 21) * 1.2) / 100);
        } else {
            // $commission = ceil(((0.6 + ($total/100)*6) * 1.6)*100);
            $commission = (((($totalCents * 0.01) + 61) * 1.2) / 100);
        }
        $commission = round($commission, 2) * 100;

        $totalIntervention = $totalCents - $commission;

        // ---- create json
        $order = new LyraOrderData(
            marketplace: self::MARKETPLACE_UUID,
            reference: 'AGENDADIAG' . $estimate->uuid->toBase58(),
            description: 'Commande diagnostic',
            urlReturn: 'https://www.agendadiagnostics.fr?id_devis_particulier=' . $estimate->uuid->toRfc4122() . '&success=0',
            buyer: new LyraBuyer(
                reference: md5($estimate->customerEmail),
                phoneNumber: $estimate->customerPhone,
                email: $estimate->customerEmail,
                firstName: $estimate->customerFirstname,
                lastName: $estimate->customerLastname,
                title: $estimate->customerCivility ?? 'M',
            ),
            shipping: new LyraShipping(),
            paymentConfig: ($isMultiple) ? 'MULTI:first=' . ceil($totalCents / 3) . ';count=3;period=30' : null,
            items: [
                new LyraOrderItem(
                    seller: $estimate->agency->lyraSeller->uuid->toRfc4122(),
                    reference: 'AGENDADIAG' . $estimate->uuid->toBase58(),
                    description: 'Diagnostics',
                    amount: (int)$totalIntervention,
                    isCommission: false,
                ),
                new LyraOrderItem(
                    seller: self::COMMISSION_SELLER,
                    reference: 'AGENDADIAG' . $estimate->uuid->toBase58(),
                    description: 'Commission',
                    amount: (int)$commission,
                    isCommission: true,
                ),
            ]
        );

        $httpClient = $this->getHttpClient();

        $response = $httpClient->request('POST', '/marketplace/orders?expand=items', [
            'auth_basic' => [$this->username, $this->password],
            'json' => $order->toArray(),
        ])->toArray();

        if (null === $orderLink = ($response['links']['execute']['href'] ?? null)) {
            $this->logger->error('Unable to create order', [
                'order' => $order,
                'response' => $response,
            ]);

            throw new LogicException('Unable to create order');
        }

        $tableOrder = TableEnum::LYRA_ORDER;
        $query = <<<SQL
        SELECT id from {$tableOrder} 
        ORDER BY id DESC
        LIMIT 1
        SQL;

        $id = (int)$this->em->getConnection()->fetchOne($query);

        $order = new LyraOrder();
        $order->uuid = Uuid::fromRfc4122($response['uuid']);
        $order->estimate = $estimate;
        $order->status = $response['status'];
        $order->amount = $price;
        $order->isMultiple = $isMultiple;
        $order->id = $id+1;

        $response = $httpClient->request('GET', $orderLink, [
            'auth_basic' => [$this->username, $this->password],
        ])->toArray();

        if (null !== $response['payment_url'] ?? null) {
            $order->url = $response['payment_url'];
        } else {
            throw new LogicException('Unable to create order');
        }

        $this->em->persist($order);
        $this->em->flush();

        return $order;
    }

    public function getOrderStatus(string|Uuid $orderUuid): void
    {
        if (!$orderUuid instanceof Uuid) {
            $orderUuid = Uuid::fromString($orderUuid);
        }

        $httpClient = $this->getHttpClient();

        $response = $httpClient->request('GET', '/marketplace/orders/' . $orderUuid->toRfc4122(), [
            'auth_basic' => [$this->username, $this->password],
        ])->toArray();

        $tableLyraOrder = TableEnum::LYRA_ORDER;
        $query = <<<SQL
            UPDATE {$tableLyraOrder}
            SET status = ?
            WHERE uuid = ?
        SQL;

        $this->em->getConnection()->executeStatement($query, [$response['status'], Uuid::fromRfc4122($response['uuid'])->toBinary()]);

        if (self::ORDER_SUCCEEDED === $response['status']) {
            $order = $this->em->getRepository(LyraOrder::class)->find($orderUuid);
            $estimate = $order->estimate;

            $estimate->isPaid = true;
            $this->em->persist($estimate);
            $this->em->flush();
        }
    }

    private function getSellers(): Generator
    {
        $httpClient = $this->getHttpClient();
        $url = '/marketplace/marketplaces/c458b921-939c-4798-8980-1666b5e6d6f7/sellers';

        do {
            $response = $httpClient->request('GET', $url, [
                'auth_basic' => [$this->username, $this->password],
            ])->toArray();

            $url = $response['next'] ?? null;

            foreach (($response['results'] ?? []) as $seller) {
                if (false === $seller['is_marketplace_seller']) {
                    yield new LyraItem(
                        uuid: Uuid::fromRfc4122($seller['uuid']),
                        description: $seller['description'],
                        status: self::ACTIVE === $seller['status'],
                    );
                }
            }
        } while (null !== $url);
    }

    private function getHttpClient(): HttpClientInterface
    {
        return HttpClient::createForBaseUri(self::BASE_URI);
    }
}

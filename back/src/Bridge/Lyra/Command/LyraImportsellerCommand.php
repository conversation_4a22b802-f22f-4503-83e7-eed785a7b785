<?php

declare(strict_types=1);

namespace App\Bridge\Lyra\Command;

use App\Bridge\Lyra\Lyra;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'app:bridge:lyra:import-seller')]
class LyraImportsellerCommand extends Command
{
    public function __construct(
        private readonly Lyra $lyra,
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->lyra->syncSellers();

        return Command::SUCCESS;
    }
}

<?php declare(strict_types=1);

namespace App\Bridge\Lyra\Model;

class LyraOrderData
{
    private const string CURRENCY = 'EUR';

    public function __construct(
        public string $marketplace,
        public string $reference,
        public string $description,
        public string $urlReturn,
        public LyraBuyer $buyer,
        public LyraShipping $shipping,
        public ?string $paymentConfig = null,
        /** @var LyraOrderItem[] */
        public array $items = [],
    ) {
    }

    public function addItem(LyraOrderItem $item): void
    {
        $this->items[] = $item;
    }

    public function toArray(): array
    {
        $array = [
            'marketplace' => $this->marketplace,
            'reference' => $this->reference,
            'description' => $this->description,
            'currency' => self::CURRENCY,
            'url_return' => $this->urlReturn,
            'buyer' => $this->buyer->toArray(),
            'shipping' => $this->shipping->toArray(),
            'items' => array_map(fn($item) => $item->toArray(), $this->items),
        ];

        if (null !== $this->paymentConfig) {
            $array['payment_config'] = $this->paymentConfig;
        }

        return $array;
    }
}

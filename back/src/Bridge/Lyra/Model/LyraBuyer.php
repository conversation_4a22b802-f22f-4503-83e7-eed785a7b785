<?php declare(strict_types=1);

namespace App\Bridge\Lyra\Model;

class LyraBuyer
{
    private const string TYPE = 'PRIVATE';

    public function __construct(
        public string $reference,
        public string $phoneNumber,
        public string $email,
        public string $firstName,
        public string $lastName,
        public string $title,
    ) {
    }

    public function toArray(): array
    {
        return [
            'reference' => $this->reference,
            'type' => self::TYPE,
            'phone_number' => $this->phoneNumber,
            'email' => $this->email,
            'first_name' => $this->firstName,
            'last_name' => $this->lastName,
            'title' => '',
        ];
    }
}

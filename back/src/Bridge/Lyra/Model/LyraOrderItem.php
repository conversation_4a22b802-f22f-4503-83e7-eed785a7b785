<?php declare(strict_types=1);

namespace App\Bridge\Lyra\Model;

class LyraOrderItem
{
    public function __construct(
        public string $seller,
        public string $reference,
        public string $description,
        public int $amount,
        public bool $isCommission,
    ) {
    }

    public function toArray(): array
    {
        return [
            'seller' => $this->seller,
            'reference' => $this->reference,
            'description' => $this->description,
            'amount' => $this->amount,
            'is_commission' => $this->isCommission,
        ];
    }
}

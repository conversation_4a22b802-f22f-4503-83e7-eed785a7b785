<?php declare(strict_types=1);

namespace App\Validator\Constraints;

use App\ApiResource\FlatContent;
use App\Doctrine\Enum\TableEnum;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Throwable;

final class UniqueSlugValidator extends ConstraintValidator
{
    public function __construct(
        private readonly EntityManagerInterface $em,
    ) {
    }

    /** @phpstan-param UniqueSlug $constraint */
    public function validate(mixed $value, Constraint $constraint): void
    {
        if (null === $value) {
            return;
        }

        if (!$value instanceof FlatContent) {
            return;
        }

        // content creation
        if (null === $value->uuid) {
            if (!$this->isValidForCreation($value)) {
                $this->context->buildViolation('Ce slug n\'est pas disponible')->atPath('slug')->addViolation();
            }

            return;
        }

        // content update
        if (!$this->isValidForUpdate($value)) {
            $this->context->buildViolation('Ce slug n\'est pas disponible')->atPath('slug')->addViolation();
        }
    }

    private function isValidForCreation(FlatContent $value): bool
    {
        try {
            $tableContent = TableEnum::CONTENT;

            if (null === $value->parent?->uuid) {
                $query = <<<SQL
                SELECT COUNT(*)
                FROM {$tableContent}
                WHERE parent_uuid IS NULL
                AND slug = ?
                SQL;

                return 0 === $this->em->getConnection()->fetchOne($query, [$value->slug]);
            }
            $query = <<<SQL
            SELECT COUNT(*)
            FROM {$tableContent}
            WHERE parent_uuid = ?
            AND slug = ?
            SQL;

            return 0 === $this->em->getConnection()->fetchOne($query, [$value->parent->uuid->toBinary(), $value->slug]);
        } catch (Throwable $e) {
        }

        return false;
    }

    private function isValidForUpdate(FlatContent $value): bool
    {
        try {
            $tableContent = TableEnum::CONTENT;

            if (null === $value->parent?->uuid) {
                $query = <<<SQL
                SELECT COUNT(*)
                FROM {$tableContent}
                WHERE parent_uuid IS NULL
                AND slug = ?
                AND uuid != ?
                SQL;

                return 0 === $this->em->getConnection()->fetchOne($query, [$value->slug, $value->uuid->toBinary()]);
            }
            $query = <<<SQL
            SELECT COUNT(*)
            FROM {$tableContent}
            WHERE parent_uuid = ?
            AND slug = ?
            AND uuid != ?
            SQL;

            return 0 === $this->em->getConnection()->fetchOne($query, [$value->parent->uuid->toBinary(), $value->slug, $value->uuid->toBinary()]);
        } catch (Throwable $e) {
        }

        return false;
    }
}

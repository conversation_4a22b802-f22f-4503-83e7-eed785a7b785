<?php declare(strict_types=1);

namespace App\Validator\Constraints;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use function is_string;
use function preg_match;

final class SlugValidator extends ConstraintValidator
{
    /** @phpstan-param Slug $constraint */
    public function validate(mixed $value, Constraint $constraint): void
    {
        if (is_string($value) && !preg_match('/^[a-zA-Z\d_-]+$/', $value)) {
            $this->context
                ->buildViolation('Le code ne doit contenir que des lettres minuscules ou majuscules, des chiffres et des tirets milieu/bas.')
                ->atPath('slug')
                ->addViolation();
        }
    }
}

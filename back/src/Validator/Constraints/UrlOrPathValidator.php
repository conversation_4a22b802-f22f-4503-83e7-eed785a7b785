<?php declare(strict_types=1);

namespace App\Validator\Constraints;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintValidator;

final class UrlOrPathValidator extends ConstraintValidator
{
    /** @phpstan-param UrlOrPath $constraint */
    public function validate(mixed $value, Constraint $constraint): void
    {
        if (null === $value) {
            return;
        }

        $this->context->getValidator()->inContext($this->context)->validate($value, new Assert\AtLeastOneOf([
            new Assert\Url(),
            new Assert\Regex('/\/(?:[A-Za-z0-9\-._~!$&\'()*+,;=:@]|%[0-9A-Fa-f]{2})*/'), // regex to capture only "path" of url
        ], message: 'Cette valeur n\'est pas une URL valide.', includeInternalMessages: false));
    }
}

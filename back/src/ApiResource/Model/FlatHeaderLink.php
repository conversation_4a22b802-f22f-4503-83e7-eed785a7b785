<?php declare(strict_types=1);

namespace App\ApiResource\Model;

use App\ApiResource\FlatContent;
use App\Entity\Upload;
use App\Validator\Constraints\UrlOrPath;
use Symfony\Component\Uid\Uuid;
use Symfony\Component\Validator\Constraints as Assert;

class FlatHeaderLink
{
    public ?Uuid $uuid = null;

    #[Assert\Length(max: 255)]
    #[UrlOrPath]
    public ?string $url = null;

    #[Assert\Length(max: 255)]
    public ?string $label = null;

    #[Assert\Length(max: 255)]
    public ?string $anchor = null;

    public ?FlatContent $content = null;

    public ?Upload $upload = null;
}

<?php declare(strict_types=1);

namespace App\ApiResource\Model;

use ApiPlatform\Metadata as APM;
use ApiPlatform\State\ObjectProvider;
use App\ApiResource\FlatBlock;
use Symfony\Component\Uid\Uuid;
use Symfony\Component\Validator\Constraints as Assert;

#[APM\ApiResource(shortName: 'Page', operations: [], provider: ObjectProvider::class)]
final class FlatPage
{
    #[APM\ApiProperty(identifier: true)]
    public ?Uuid $uuid = null;

    /** @var FlatBlock[] */
    #[Assert\Count(min: 1)]
    #[Assert\Valid]
    #[APM\ApiProperty(readableLink: true, writableLink: true)]
    public array $blocks = [];

    public function __construct()
    {
        $this->uuid ??= Uuid::v4();
    }
}

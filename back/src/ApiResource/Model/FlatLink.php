<?php declare(strict_types=1);

namespace App\ApiResource\Model;

use App\ApiResource\FlatContent;
use App\Validator\Constraints\UrlOrPath;
use Symfony\Component\Uid\Uuid;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

class FlatLink
{
    public ?Uuid $uuid = null;

    #[Assert\NotBlank(allowNull: true)]
    #[Assert\Length(max: 255)]
    #[UrlOrPath]
    public ?string $url = null;

    #[Assert\NotBlank(allowNull: true)]
    #[Assert\Length(max: 255)]
    public ?string $label = null;

    public ?FlatContent $content = null;

    #[Assert\NotBlank(allowNull: true)]
    #[Assert\Length(max: 255)]
    public ?string $anchor = null;

    #[Assert\Callback]
    public function assertIsValid(ExecutionContextInterface $context): void
    {
        if (null === $this->content && null === $this->label) {
            $context->buildViolation('Il faut obligatoirement un label ou un contenu lié')->atPath('label')->addViolation();

            return;
        }

        if (null !== $this->content && (null !== $this->label || null !== $this->url)) {
            $context->buildViolation('Le lien ne peut pas contenir un label et un contenu en même temps')->atPath('label')->addViolation();
        }
    }
}

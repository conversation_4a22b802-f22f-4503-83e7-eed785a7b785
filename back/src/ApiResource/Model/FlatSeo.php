<?php declare(strict_types=1);

namespace App\ApiResource\Model;

use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\State\ObjectProvider;
use Symfony\Component\Uid\Uuid;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(shortName: 'Seo', operations: [], provider: ObjectProvider::class)]
class FlatSeo
{
    #[ApiProperty(writable: false, identifier: true)]
    public ?Uuid $uuid = null;

    #[Assert\Length(max: 255)]
    public ?string $title = null;

    #[Assert\Length(max: 1000)]
    public ?string $description = null;

    public function __construct()
    {
        $this->uuid ??= Uuid::v4();
    }
}

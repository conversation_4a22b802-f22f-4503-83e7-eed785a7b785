<?php declare(strict_types=1);

namespace App\ApiResource\Model;

use App\Entity\Upload;
use App\Validator\Constraints\UrlOrPath;
use Symfony\Component\Validator\Constraints as Assert;

class FlatSocialLink
{
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    #[UrlOrPath]
    public ?string $url = null;

    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    public ?string $label = null;

    public ?Upload $upload = null;

    #[Assert\NotBlank(allowNull: true)]
    #[Assert\Length(max: 255)]
    public ?string $anchor = null;
}

<?php declare(strict_types=1);

namespace App\ApiResource;

use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\Post;
use App\Api\Model\OrderPosition;
use App\Api\Processor\OrderProcessor;
use App\Security\Enum\RoleEnum;
use Symfony\Component\Validator\Constraints as Assert;

#[Post(security: 'is_granted("' . RoleEnum::SUPER_ADMIN . '")', processor: OrderProcessor::class)]
class ContentOrder
{
    #[ApiProperty(identifier: true)]
    public ?int $id = null;

    /** @var OrderPosition[] */
    #[Assert\Count(min: 1)]
    #[Assert\Valid]
    public array $orders = [];
}

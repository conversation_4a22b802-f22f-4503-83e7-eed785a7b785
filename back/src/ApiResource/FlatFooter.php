<?php declare(strict_types=1);

namespace App\ApiResource;

use ApiPlatform\Metadata as APM;
use App\Api\Processor\FooterProcessor;
use App\Api\Provider\FooterProvider;
use App\ApiResource\Model\FlatLogo;
use App\ApiResource\Model\FlatNav;
use App\ApiResource\Model\FlatSocial;
use App\ApiResource\Model\Link;
use App\Security\Enum\RoleEnum;
use Symfony\Component\Validator\Constraints as Assert;

#[APM\ApiResource(uriTemplate: 'footer', shortName: 'Footer')]
#[APM\Get(provider: FooterProvider::class)]
#[APM\Put(security: 'is_granted("' . RoleEnum::SUPER_ADMIN . '", object)', provider: FooterProvider::class, processor: FooterProcessor::class)]
class FlatFooter
{
    #[Assert\NotNull]
    #[Assert\Valid]
    public ?FlatLogo $logo = null;

    /** @var FlatNav[] */
    #[Assert\Valid]
    public array $navs = [];

    /** @var Link[] */
    #[Assert\Valid]
    public array $links = [];

    /** @var FlatSocial[] */
    #[Assert\Valid]
    public array $socials = [];
}

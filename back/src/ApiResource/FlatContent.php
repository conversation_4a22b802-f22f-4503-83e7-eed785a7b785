<?php declare(strict_types=1);

namespace App\ApiResource;

use ApiPlatform\Metadata as APM;
use App\Api\Filter\ContentFilter;
use App\Api\Processor\ContentProcessor;
use App\Api\Provider\ContentCollectionProvider;
use App\Api\Provider\ContentProvider;
use App\ApiResource\Model\FlatPage;
use App\ApiResource\Model\FlatSeo;
use App\Business\Breadcrumb\Model\Breadcrumb;
use App\Entity\Traits\PositionTrait;
use App\Entity\Traits\PublishedAtTrait;
use App\Security\Enum\RoleEnum;
use App\Validator\Constraints\UniqueSlug;
use Symfony\Component\Uid\Uuid;
use Symfony\Component\Validator\Constraints as Assert;

#[APM\ApiResource(uriTemplate: 'contents', shortName: 'Content', provider: ContentProvider::class, processor: ContentProcessor::class)]
#[APM\GetCollection(provider: ContentCollectionProvider::class)]
#[APM\Get(uriTemplate: 'contents/{uuid}', priority: 1)]
#[APM\Get(uriTemplate: 'content/slug', parameters: new APM\Parameters([new APM\QueryParameter('slug', required: true)]))]
#[APM\Post(security: 'is_granted("' . RoleEnum::SUPER_ADMIN . '", object)')]
#[APM\Put(uriTemplate: 'contents/{uuid}', security: 'is_granted("' . RoleEnum::SUPER_ADMIN . '", object)')]
#[APM\Delete(uriTemplate: 'contents/{uuid}', security: 'is_granted("' . RoleEnum::SUPER_ADMIN . '", object)')]
#[UniqueSlug]
#[APM\ApiFilter(ContentFilter::class)]
final class FlatContent
{
    public const string READ = 'content:read';

    use PublishedAtTrait;
    use PositionTrait;

    #[APM\ApiProperty(identifier: true, fetchable: true)]
    public ?Uuid $uuid = null;

    #[APM\ApiProperty(writable: false)]
    public ?Breadcrumb $breadcrumb = null;

    public ?FlatContent $parent = null;

    public bool $status = true;

    #[Assert\NotBlank]
    public ?string $name = null;

    #[Assert\Valid]
    #[APM\ApiProperty(readableLink: true, writableLink: true)]
    public ?FlatSeo $seo = null;

    // computed if null
    // #[Assert\NotBlank(allowNull: true)]
    public ?string $slug = null;

    #[APM\ApiProperty(writable: false)]
    public ?string $url = null; // <------------ url sur le site à titre d'info (tous ses slugs parents + le sien)

    #[Assert\Valid]
    #[APM\ApiProperty(readableLink: true, writableLink: true)]
    public ?FlatPage $page = null;
}

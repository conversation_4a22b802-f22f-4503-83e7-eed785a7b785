<?php declare(strict_types=1);

namespace App\ApiResource;

use ApiPlatform\Metadata as APM;
use ApiPlatform\State\ObjectProvider;
use App\Api\Provider\FavoriteBlockCollectionProvider;
use App\Doctrine\Enum\BlockTypeEnum;
use App\Security\Enum\RoleEnum;
use Symfony\Component\Uid\Uuid;
use Symfony\Component\Validator\Constraints as Assert;

#[APM\ApiResource(shortName: 'Block', operations: [], provider: ObjectProvider::class)]
#[APM\GetCollection(
    uriTemplate: 'contents/blocks/favorite',
    shortName: 'ContentBlockFavorite',
    security: 'is_granted("' . RoleEnum::SUPER_ADMIN . '")', // only people who can create contents can see favorite blocks
    provider: FavoriteBlockCollectionProvider::class,
)]
class FlatBlock
{
    #[APM\ApiProperty(identifier: true)]
    public ?Uuid $uuid = null;

    #[Assert\NotBlank(allowNull: true)]
    public ?string $name = null;

    #[APM\ApiProperty(openapiContext: ['enum' => BlockTypeEnum::ALL])]
    #[Assert\NotNull]
    #[Assert\Choice(choices: BlockTypeEnum::ALL)]
    public ?string $type = null;

    // @todo how can i validate this ?
    public array $parameters = [];

    public function __construct()
    {
        $this->uuid ??= Uuid::v4();
    }
}

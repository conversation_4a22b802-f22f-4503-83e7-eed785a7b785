<?php declare(strict_types=1);

namespace App\ApiResource;

use ApiPlatform\Metadata as APM;
use App\Api\Processor\HeaderProcessor;
use App\Api\Provider\HeaderProvider;
use App\ApiResource\Model\FlatLogo;
use App\ApiResource\Model\FlatNav;
use App\ApiResource\Model\HeaderLink;
use App\Security\Enum\RoleEnum;
use Symfony\Component\Validator\Constraints as Assert;

#[APM\ApiResource(uriTemplate: 'header', shortName: 'Header')]
#[APM\Get(provider: HeaderProvider::class)]
#[APM\Put(security: 'is_granted("' . RoleEnum::SUPER_ADMIN . '", object)', provider: HeaderProvider::class, processor: HeaderProcessor::class)]
class FlatHeader
{
    #[Assert\NotNull]
    #[Assert\Valid]
    public ?FlatLogo $logo = null;

    /** @var FlatNav[] */
    #[Assert\Valid]
    public array $navs = [];

    /** @var HeaderLink[] */
    #[Assert\Valid]
    public array $links = [];
}

<?php declare(strict_types=1);

namespace App\Contract;

use Symfony\Component\Uid\Uuid;
use function count;

final class Uuids
{
    /**
     * @var Uuid[]
     */
    public array $uuids = [];

    public function add(Uuid $uuid): void
    {
        $this->uuids[] = $uuid;
    }

    public static function fromRfc4122(array $uuids): self
    {
        $u = new self();

        foreach ($uuids as $uuid) {
            $u->add(Uuid::fromRfc4122($uuid));
        }

        return $u;
    }

    public static function fromBinaries(array $uuids): self
    {
        $u = new self();

        foreach ($uuids as $uuid) {
            $u->add(Uuid::fromBinary($uuid));
        }

        return $u;
    }

    /** @return string[] uuid as binary format */
    public function toBinaries(): array
    {
        return array_map(fn (Uuid $uuid) => $uuid->toBinary(), $this->uuids);
    }

    public function isEmpty(): bool
    {
        return 0 === $this->count();
    }

    public function count(): int
    {
        return count($this->uuids);
    }
}

<?php declare(strict_types=1);

namespace App\Utils;

use Symfony\Component\Uid\Uuid;
use function count;
use function ctype_digit;
use function end;
use function explode;
use function is_int;
use function is_string;
use function str_replace;
use function str_starts_with;

abstract class IriUtils
{
    public static function toId(string|int|null $iri): ?int
    {
        if (is_int($iri)) {
            return $iri;
        }

        if (is_string($iri)) {
            if (ctype_digit($iri)) {
                return (int)$iri;
            }
            if (str_starts_with($iri, '/api/') && 4 === count($parts = explode('/', $iri))) {
                return self::toId(end($parts));
            }
        }

        return null;
    }

    public static function toCode(string|int|null $iri): ?string
    {
        if (is_string($iri)) {
            if (str_starts_with($iri, '/api/') && 4 === count($parts = explode('/', $iri))) {
                return end($parts);
            }

            return $iri;
        }

        return null;
    }

    public static function toUuid(mixed $iri): ?Uuid
    {
        if ($iri instanceof Uuid) {
            return $iri;
        }
        if (is_string($iri)) {
            if (Uuid::isValid($iri)) {
                return Uuid::fromRfc4122($iri);
            }
            if (str_starts_with($iri, '/api/')) {
                $iri = str_replace('/binary', '', $iri);
                if (4 === count($parts = explode('/', $iri))) {
                    return self::toUuid(end($parts));
                }
            }
        }

        return null;
    }
}

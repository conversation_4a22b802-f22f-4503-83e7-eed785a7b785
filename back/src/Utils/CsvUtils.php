<?php declare(strict_types=1);

namespace App\Utils;

use Generator;
use Throwable;
use function array_combine;
use function array_filter;
use function array_key_first;
use function array_map;
use function arsort;
use function count;
use function fclose;
use function feof;
use function fgetcsv;
use function fgets;
use function file_exists;
use function fopen;
use function is_resource;
use function is_string;
use function substr_count;
use const PHP_EOL;

abstract class CsvUtils
{
    public const string DEFAULT_SEPARATOR = ',';
    public const string DEFAULT_ENCLOSURE = '"';
    public const string DEFAULT_ESCAPE = '\\';
    public const string DEFAULT_EOL = PHP_EOL;
    public const string SEPARATOR_COMMA = 'comma';
    public const string SEPARATOR_SEMICOLON = 'semicolon';
    public const string SEPARATOR_PIPE = 'pipe';
    public const string SEPARATOR_TAB = 'tab';
    public const array SEPARATORS = [
        self::SEPARATOR_COMMA => ',',
        self::SEPARATOR_SEMICOLON => ';',
        self::SEPARATOR_PIPE => '|',
        self::SEPARATOR_TAB => "\t",
    ];

    public static function noBom(?string $string): string
    {
        return null === $string ? '' : trim($string, " \xef\xbb\xbf");
    }

    /**
     * @phpstan-return Generator<array>
     */
    public static function mapAssociative(mixed $path, ?string $separator = null): Generator
    {
        $separator ??= self::detectSeparator($path);

        try {
            if (null !== $r = self::toResource($path)) {
                $i = 0;

                $keys = [];
                while (false !== $row = fgetcsv($r, separator: $separator)) {
                    if (self::rowIsEmpty($row)) {
                        continue;
                    }

                    if (0 === $i) {
                        $keys = array_map(self::noBom(...), $row);
                    } else {
                        yield array_combine(
                            $keys,
                            array_map(self::noBom(...), $row),
                        );
                    }
                    $i++;
                }
                fclose($r);
            }
        } catch (Throwable) {
            // @todo log!
        }
    }

    public static function detectSeparator(mixed $path, string $default = self::DEFAULT_SEPARATOR): string
    {
        if (null !== $r = self::toResource($path)) {
            $occurrences = [];
            while (!feof($r)) {
                if (false !== $line = fgets($r)) {
                    foreach (self::SEPARATORS as $separator) {
                        $occurrences[$separator] = substr_count($line, $separator);
                    }
                }
            }
            fclose($r);

            if (0 < count($occurrences)) {
                arsort($occurrences);

                return array_key_first($occurrences);
            }
        }

        return $default;
    }

    /**
     * @phpstan-return resource|null
     */
    private static function toResource(mixed $path): mixed
    {
        if (is_resource($path)) {
            return $path;
        }
        if (is_string($path) && file_exists($path)) {
            return fopen($path, 'r') ?: null;
        }

        return null;
    }

    private static function rowIsEmpty(array $row): bool
    {
        return (1 === count($row) && '' === $row[0])
            || 0 === count(array_filter($row));
    }
}

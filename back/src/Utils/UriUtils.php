<?php declare(strict_types=1);

namespace App\Utils;

use Symfony\Component\Uid\Uuid;

abstract class UriUtils
{
    public static function getUuid(?string $uri): ?Uuid
    {
        if (null === $uri) {
            return null;
        }

        if (str_starts_with($uri, '/api/')) {
            $uri = explode('/', $uri);
            $uri = end($uri);
        }

        if (Uuid::isValid($uri)) {
            return Uuid::fromRfc4122($uri);
        }

        return null;
    }
}

<?php declare(strict_types=1);

namespace App\Utils;

use Generator;
use function preg_match;

abstract class SecurityUtils
{
    private const array SPECIAL_CHARS = [
        '²', '&', '~', '"', '#', '\'', '{',
        '(', '[', '-', '|', '`', '_', '\\',
        '^', '^', '@', ')', ']', '=', '}',
        '¨', '^', '$', '£', '¤', '%', '*',
        '?', ',', ';', '.', '/', ':', '!',
        '§', '+',
    ];

    public static function getPasswordErrors(string $password): Generator
    {
        if (8 > mb_strlen($password)) {
            yield 'user.password.length';
        }
        if (!preg_match('/[A-Z]/', $password)) {
            yield 'user.password.uppercase';
        }
        if (!preg_match('/[a-z]/', $password)) {
            yield 'user.password.lowercase';
        }
        if (!preg_match('/\d/', $password)) {
            yield 'user.password.number';
        }

        $hasSpecialChar = false;
        foreach (self::SPECIAL_CHARS as $specialChar) {
            if (str_contains($password, $specialChar)) {
                $hasSpecialChar = true;
                break;
            }
        }
        if (!$hasSpecialChar) {
            yield 'user.password.special';
        }
    }
}

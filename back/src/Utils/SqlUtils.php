<?php declare(strict_types=1);

namespace App\Utils;

use function array_fill;
use function count;
use function implode;

abstract class SqlUtils
{
    public static function createPlaceholdersFor(array $values, bool $withParentheses = false): string
    {
        return self::createPlaceholders(count($values), $withParentheses);
    }

    public static function createPlaceholders(int $count, bool $withParentheses = false): string
    {
        $placeholders = implode(', ', array_fill(0, $count, '?'));

        if ($withParentheses) {
            $placeholders = "({$placeholders})";
        }

        return $placeholders;
    }

    public static function fixJsonNull(?string $value): ?string
    {
        if (null === $value || 'null' === $value) {
            return null;
        }

        return $value;
    }
}

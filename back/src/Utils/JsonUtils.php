<?php declare(strict_types=1);

namespace App\Utils;

use Exception;
use Throwable;
use function is_array;
use function is_string;
use function json_decode;
use function json_encode;
use function json_last_error_msg;
use const JSON_THROW_ON_ERROR;

abstract class JsonUtils
{
    public const string DOCUMENT_TYPE_KEY = '#type';
    public const string ODM_SERIALIZER = 'dunglas_doctrine_json_odm.serializer';

    public static function encode(mixed $array, int $flags = JSON_THROW_ON_ERROR): ?string
    {
        if (null === $array) {
            return null;
        }

        try {
            if (false === $encoded = json_encode($array, $flags)) {
                throw new Exception(json_last_error_msg());
            }

            return $encoded;
        } catch (Throwable) {
        }

        return null;
    }

    public static function decode(string|array|null $string): ?array
    {
        if (is_array($string)) {
            return $string;
        }
        if (is_string($string)) {
            try {
                return (array)json_decode($string, true, 512, JSO<PERSON>_THROW_ON_ERROR);
            } catch (Throwable) {
            }
        }

        return null;
    }
}

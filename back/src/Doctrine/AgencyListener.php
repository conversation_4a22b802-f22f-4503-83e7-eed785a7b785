<?php declare(strict_types=1);

namespace App\Doctrine;

use App\Bridge\GeoCoder\GeoCoder;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Agency\Agency;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Event\PrePersistEventArgs;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Doctrine\ORM\Events;
use Throwable;

#[AsDoctrineListener(event: Events::prePersist)]
#[AsDoctrineListener(event: Events::preUpdate)]
final readonly class AgencyListener
{
    public function __construct(
        private GeoCoder $geocoder,
        private EntityManagerInterface $em,
    ) {
    }

    public function prePersist(PrePersistEventArgs $args): void
    {
        /** @var Agency $agency */
        $agency = $args->getObject();

        if (!$args->getObject() instanceof Agency) {
            return;
        }

        $tableAgency = TableEnum::AGENCY;

        if (null === $agency->legacyId) {
            $query = <<<SQL
            SELECT legacy_id
            FROM {$tableAgency}
            ORDER BY legacy_id DESC
            SQL;

            try {
                $id = $this->em->getConnection()->fetchOne($query);
            } catch (Throwable $exception) {
                $id = $this->em->getRepository(Agency::class)->count([]);
            }

            $id++;

            $agency->legacyId = $id;
        }

        $this->handleAgencyChange($args);
    }

    public function preUpdate(PreUpdateEventArgs $args): void
    {
        $this->handleAgencyChange($args);
    }

    private function handleAgencyChange(PrePersistEventArgs|PreUpdateEventArgs $args): void
    {
        if (!$args->getObject() instanceof Agency) {
            return;
        }

        /** @var Agency $agency */
        $agency = $args->getObject();

        if (
            !$args instanceof PrePersistEventArgs
            && ! $args->hasChangedField('location')
        ) {
            return;
        }

        $fullAdress = $agency->location->address1 . ' ' . $agency->location->city;

        [$long, $lat] = $this->geocoder->geocode($fullAdress, $agency->location->postcode);

        if (null === $lat || null === $long) {
            return;
        }

        $agency->location->latitude = $lat;
        $agency->location->longitude = $long;
    }
}

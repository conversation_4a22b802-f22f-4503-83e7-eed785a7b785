<?php

declare(strict_types=1);

namespace App\Doctrine\Enum;

abstract class BlockTypeEnum
{
    public const string DEFAULT = 'default';
    public const string TEXT = 'BlockText';
    public const string IMAGE_BANNER = 'BlocImageBanner';
    public const string BANNER = 'BlocBanner';
    public const string TEXT_MEDIA = 'BlocTextMedia';
    public const string BLOCK_DOUBLE_TEXT = 'BlockDoubleText';
    public const string BLOCK_FILES = 'BlockFiles';
    public const string BLOCK_CARDS = 'BlockCards';
    public const string BLOCK_CARD_SUMMARY = 'BlockCardSummary';
    public const string BLOCK_FORM_NEWSLETTER = 'BlockFormNewsLetter';
    public const string BLOCK_SURFACE_COMPARISION = 'BlockSurfaceComparision';
    public const string BLOCK_DOUBLE_BLOCK = 'BlockDoubleBlock';
    public const string BLOCK_TESTIMONIALS = 'BlockTestimonials';
    public const string BLOCK_QUOTE = 'BlockQuote';
    public const string BLOCK_QUOTE_SINGLE = 'BlockQuoteSingle';
    public const string BLOCK_FIND_MAP = 'BlockFindMap';
    public const string BLOCK_CATEGORIES = 'BlockCategories';
    public const string BLOCK_MAP = 'BlockMap';
    public const string BLOCK_NEWS_PREVIEW = 'BlockNewsPreview';
    public const string BLOCK_NEWS = 'BlockNews';
    public const string BLOCK_FIND_PARTNERS = 'BlockFindPartners';
    public const string BLOCK_MOST_VIEWED = 'BlockMostViewed';
    public const string BLOCK_CAROUSEL = 'BlockCarousel';
    public const string BLOCK_JOB = 'BlockJob';
    public const string BLOCK_DIAGNOSTIC = 'BlockDiagnostic';
    public const string BLOCK_REVIEW = 'BlockReview';
    public const string BLOCK_PRO_QUOTE = 'BlockProQuote';
    public const string BLOCK_FORM_JOB = 'BlockFormJob';
    public const string BLOCK_FORM_INFOS = 'BlockFormInfos';
    public const string BLOCK_FORM_GRAND_COMPTE = 'BlockFormGrandCompte';
    public const string BLOCK_NEWSLETTER = 'BlockNewsletter';
    public const string BLOCK_FORM_DIAG_MAG = 'BlockFormDiagMag';
    public const string BLOCK_FORM_DIAG_ASSIST = 'BlockFormDiagAssist';

    public const array ALL = [
        self::DEFAULT,
        self::TEXT,
        self::IMAGE_BANNER,
        self::BANNER,
        self::TEXT_MEDIA,
        self::BLOCK_DOUBLE_TEXT,
        self::BLOCK_FILES,
        self::BLOCK_CARDS,
        self::BLOCK_CARD_SUMMARY,
        self::BLOCK_FORM_NEWSLETTER,
        self::BLOCK_SURFACE_COMPARISION,
        self::BLOCK_DOUBLE_BLOCK,
        self::BLOCK_TESTIMONIALS,
        self::BLOCK_QUOTE,
        self::BLOCK_QUOTE_SINGLE,
        self::BLOCK_FIND_MAP,
        self::BLOCK_CATEGORIES,
        self::BLOCK_MAP,
        self::BLOCK_NEWS_PREVIEW,
        self::BLOCK_NEWS,
        self::BLOCK_FIND_PARTNERS,
        self::BLOCK_MOST_VIEWED,
        self::BLOCK_CAROUSEL,
        self::BLOCK_JOB,
        self::BLOCK_DIAGNOSTIC,
        self::BLOCK_REVIEW,
        self::BLOCK_PRO_QUOTE,
        self::BLOCK_FORM_JOB,
        self::BLOCK_FORM_INFOS,
        self::BLOCK_FORM_GRAND_COMPTE,
        self::BLOCK_NEWSLETTER,
        self::BLOCK_FORM_DIAG_MAG,
        self::BLOCK_FORM_DIAG_ASSIST,
    ];
}

<?php declare(strict_types=1);

namespace App\Doctrine\Enum;

abstract class TableEnum
{
    public const string AGENCY = 'crm_agency';

    public const string AGENCY_LOCATION_AREA = 'crm_agency_location_area';
    public const string AGENCY_POST = 'crm_agency_post';
    public const string AGENCY_USER = 'crm_agency_user';
    public const string CONTENT = 'crm_content';
    public const string CONTENT_BLOCK = 'crm_content_block';
    public const string CONTENT_PAGE = 'crm_content_page';
    public const string CONTENT_PAGE_BLOCK = 'crm_content_page_block';
    public const string FOOTER = 'crm_footer';
    public const string FOOTER_NAV = 'crm_footer_nav';
    public const string FOOTER_LINK = 'crm_footer_link';
    public const string FOOTER_SOCIAL = 'crm_footer_social';
    public const string HEADER = 'crm_header';
    public const string HEADER_LINK = 'crm_header_link';
    public const string HEADER_NAV = 'crm_header_nav';
    public const string JOB_OFFER = 'crm_job_offer';
    public const string JOB_APPLICATION = 'crm_job_application';
    public const string LOCATION_AREA = 'crm_location_area';
    public const string LOCATION_CITY = 'crm_location_city';
    public const string LOCATION_DEPARTMENT = 'crm_location_department';
    public const string MAILER_TYPE = 'crm_mailer_type';
    public const string LYRA_ORDER = 'crm_lyra_order';
    public const string LYRA_SELLER = 'crm_lyra_seller';
    public const string PARTNER = 'crm_partner';
    public const string POST = 'crm_post';
    public const string SETTING = 'crm_setting';
    public const string UPLOAD = 'crm_upload';
    public const string USER = 'crm_user';

    // TABLE FOR ESTIMATION MODULE
    public const string ESTIMATE = 'est_estimate';
    public const string PRICE_GRID = 'est_price_grid';
    public const string PRO_ESTIMATE = 'est_pro_estimate';
}

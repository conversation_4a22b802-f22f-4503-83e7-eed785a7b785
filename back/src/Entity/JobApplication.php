<?php

declare(strict_types=1);

namespace App\Entity;

use ApiPlatform\Doctrine\Orm\Filter\ExistsFilter;
use ApiPlatform\Doctrine\Orm\Filter\OrderFilter;
use ApiPlatform\Metadata as APM;
use App\Api\Filter\QFilter;
use App\Api\Processor\JobApplicationProcessor;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Agency\Agency;
use App\Entity\Traits\CreatedAtTrait;
use App\Entity\Traits\UpdatedAtTrait;
use App\Entity\Traits\UuidTrait;
use App\Security\Enum\CivilityEnum;
use App\Security\Enum\DrivingLicenceEnum;
use App\Security\Enum\SituationEnum;
use App\Security\Voter\JobApplicationVoter;
use DateTime;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Table(name: TableEnum::JOB_APPLICATION)]
#[ORM\Entity]
#[APM\GetCollection(security: 'is_granted("' . JobApplicationVoter::GET_COLLECTION . '")')]
#[APM\Post(securityPostDenormalize: 'is_granted("' . JobApplicationVoter::POST . '", object)', processor: JobApplicationProcessor::class)]
#[APM\Get(security: 'is_granted("' . JobApplicationVoter::GET . '", object)')]
#[APM\Put(security: 'is_granted("' . JobApplicationVoter::PUT . '", object)')]
#[APM\Delete(security: 'is_granted("' . JobApplicationVoter::DELETE . '", object)')]
#[APM\ApiFilter(ExistsFilter::class, properties: ['jobOffer'])]
#[APM\ApiFilter(OrderFilter::class, properties: ['createdAt' => 'DESC', 'lastname' => 'ASC', 'firstname' => 'ASC', 'email' => 'ASC', 'phone' => 'ASC', 'address' => 'ASC', 'zipCode' => 'ASC', 'city' => 'ASC'])]
#[APM\ApiFilter(QFilter::class, properties: ['lastname', 'firstname', 'email', 'phone', 'address', 'zipCode', 'city'])]
class JobApplication
{
    use UuidTrait;
    use CreatedAtTrait;
    use UpdatedAtTrait;

    #[ORM\Column(nullable: true)]
    #[Assert\Length(max: 255)]
    public ?string $wantedJob = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    public ?DateTime $birthdate = null;

    #[ORM\Column(nullable: true)]
    #[Assert\Length(max: 255)]
    #[Assert\Choice(choices: CivilityEnum::ALL, multiple: false)]
    public ?string $civility = null;

    #[ORM\Column]
    #[Assert\Length(max: 255)]
    #[Assert\NotNull]
    #[Assert\NotBlank]
    public ?string $firstname = null;

    #[ORM\Column]
    #[Assert\Length(max: 255)]
    #[Assert\NotBlank]
    public ?string $lastname = null;

    #[ORM\Column]
    #[Assert\Length(max: 255)]
    #[Assert\NotBlank]
    public ?string $address = null;

    #[ORM\Column]
    #[Assert\Length(max: 255)]
    #[Assert\NotBlank]
    public ?string $zipCode = null;

    #[ORM\Column]
    #[Assert\Length(max: 255)]
    #[Assert\NotBlank]
    public ?string $city = null;

    #[ORM\Column]
    #[Assert\Length(max: 255)]
    #[Assert\NotBlank]
    public ?string $phone = null;

    #[ORM\Column]
    #[Assert\Length(max: 255)]
    #[Assert\NotBlank]
    #[Assert\Email]
    public ?string $email = null;

    #[ORM\Column(type: Types::TEXT)]
    #[Assert\NotBlank]
    public ?string $training = null;

    #[ORM\Column(type: Types::JSON)]
    #[Assert\Choice(choices: DrivingLicenceEnum::ALL, multiple: true)]
    #[Assert\NotNull]
    public array $drivingLicense = [];

    #[ORM\Column(type: Types::TEXT)]
    #[Assert\NotBlank]
    public ?string $professionalExperience = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    public ?string $computerLevel = null;

    #[ORM\Column(nullable: true)]
    #[Assert\Choice(choices: SituationEnum::ALL)]
    public ?string $situation = null;

    #[ORM\Column(nullable: true)]
    #[Assert\Length(max: 255)]
    public ?string $workplace = null;

    #[ORM\Column(nullable: true)]
    #[Assert\Length(max: 255)]
    public ?string $availability = null;

    #[ORM\Column(type: Types::FLOAT, length: 255, nullable: true)]
    public ?float $salary = null;

    #[ORM\ManyToOne(targetEntity: Upload::class)]
    #[ORM\JoinColumn(name: 'cv_uuid', referencedColumnName: 'uuid', nullable: false, onDelete: 'CASCADE')]
    #[Assert\NotNull]
    public ?Upload $cv = null;

    #[ORM\ManyToOne(targetEntity: Upload::class)]
    #[ORM\JoinColumn(name: 'cover_letter_uuid', referencedColumnName: 'uuid', nullable: false, onDelete: 'CASCADE')]
    #[Assert\NotNull]
    public ?Upload $coverLetter = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'job_offer_uuid', referencedColumnName: 'uuid', nullable: true, onDelete: 'CASCADE')]
    #[APM\ApiProperty(readableLink: true, writableLink: false)]
    public ?JobOffer $jobOffer = null;

    public ?Agency $agency = null;

    public function __construct()
    {
        $this->defineUuid();
    }

    public function toArray(): array
    {
        return get_object_vars($this);
    }
}

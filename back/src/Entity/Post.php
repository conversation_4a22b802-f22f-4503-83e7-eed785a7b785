<?php

declare(strict_types=1);

namespace App\Entity;

use ApiPlatform\Doctrine\Orm\Filter\OrderFilter;
use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Metadata as APM;
use App\Api\Filter\QFilter;
use App\Api\Provider\PostProvider;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Traits\CreatedAtTrait;
use App\Entity\Traits\SeoTrait;
use App\Entity\Traits\UpdatedAtTrait;
use App\Entity\Traits\UuidTrait;
use App\Security\Enum\RoleEnum;
use App\Validator\Constraints\UrlOrPath;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity]
#[ORM\Table(name: TableEnum::POST)]
#[APM\GetCollection(paginationClientEnabled: true, paginationClientItemsPerPage: true, order: ['createdAt' => 'DESC'], security: 'is_granted("' . RoleEnum::PUBLIC_ACCESS . '")')]
#[APM\Post(securityPostDenormalize: 'is_granted("' . RoleEnum::SUPER_ADMIN . '", object)')]
#[APM\Get(security: 'is_granted("' . RoleEnum::PUBLIC_ACCESS . '", object)')]
#[APM\Get(uriTemplate: '/post', uriVariables: [], security: 'is_granted("' . RoleEnum::PUBLIC_ACCESS . '", object)', provider: PostProvider::class)]
#[APM\Put(security: 'is_granted("' . RoleEnum::SUPER_ADMIN . '", object)')]
#[APM\Delete(security: 'is_granted("' . RoleEnum::SUPER_ADMIN . '", object)')]
#[APM\ApiFilter(SearchFilter::class, properties: ['category' => 'exact'])]
#[APM\ApiFilter(OrderFilter::class, properties: ['createdAt' => 'DESC', 'title' => 'ASC', 'url' => 'ASC'])]
#[APM\ApiFilter(QFilter::class, properties: ['title', 'url'])]
class Post
{
    use UuidTrait;
    use CreatedAtTrait;
    use UpdatedAtTrait;
    use SeoTrait;

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    public ?string $title = null;

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    public ?string $longTitle = null;

    #[ORM\Column(type: Types::TEXT)]
    #[Assert\NotBlank]
    public ?string $description = null;

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    public ?string $category = null;

    #[ORM\Column(type: Types::TEXT)]
    #[Assert\NotBlank]
    public ?string $content = null;

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    #[UrlOrPath]
    public ?string $url = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'upload_uuid', referencedColumnName: 'uuid', nullable: true, onDelete: 'SET NULL')]
    #[Assert\NotNull]
    public ?Upload $upload = null;

    #[ORM\Column]
    #[Assert\NotNull]
    public bool $isPublished = false;

    public function __construct()
    {
        $this->defineUuid();
    }
}

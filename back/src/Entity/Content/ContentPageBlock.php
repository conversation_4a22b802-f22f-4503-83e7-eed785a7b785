<?php declare(strict_types=1);

namespace App\Entity\Content;

use App\Doctrine\Enum\TableEnum;
use App\Entity\Traits\CreatedAtTrait;
use App\Entity\Traits\PositionTrait;
use App\Entity\Traits\UpdatedAtTrait;
use App\Entity\Traits\UuidTrait;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity]
#[ORM\Table(name: TableEnum::CONTENT_PAGE_BLOCK)]
class ContentPageBlock
{
    use UuidTrait;
    use CreatedAtTrait;
    use UpdatedAtTrait;
    use PositionTrait;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'page_uuid', referencedColumnName: 'uuid', nullable: false, onDelete: 'CASCADE')]
    #[Assert\NotNull]
    public ?ContentPage $page = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'block_uuid', referencedColumnName: 'uuid', nullable: false, onDelete: 'CASCADE')]
    public ?ContentBlock $block = null;

    public function __construct()
    {
        $this->defineUuid();
    }
}

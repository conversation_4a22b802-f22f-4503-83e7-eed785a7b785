<?php declare(strict_types=1);

namespace App\Entity\Content;

use App\Business\Breadcrumb\Entity\Breadcrumb;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Traits\CreatedAtTrait;
use App\Entity\Traits\PositionTrait;
use App\Entity\Traits\PublishedAtTrait;
use App\Entity\Traits\SeoTrait;
use App\Entity\Traits\UpdatedAtTrait;
use App\Entity\Traits\UuidTrait;
use App\Repository\ContentRepository;
use App\Validator\Constraints\Slug;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: ContentRepository::class)]
#[ORM\Table(name: TableEnum::CONTENT)]
class Content
{
    use UuidTrait;
    use CreatedAtTrait;
    use UpdatedAtTrait;
    use PublishedAtTrait;
    use SeoTrait;
    use PositionTrait;

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    public ?string $name = null;

    #[ORM\Column(length: 255)]
    #[Assert\Length(max: 255)]
    #[Slug]
    public ?string $slug = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'parent_uuid', referencedColumnName: 'uuid', onDelete: 'CASCADE')]
    public ?Content $parent = null;

    #[ORM\Column(options: ['default' => true])]
    public bool $status = true;

    /**
     * A content without a content page is seen as a category.
     */
    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'content_uuid', referencedColumnName: 'uuid', onDelete: 'SET NULL')]
    public ?ContentPage $contentPage = null;

    #[ORM\Embedded]
    public ?Breadcrumb $breadcrumb = null;

    public function __construct()
    {
        $this->defineUuid();
        $this->breadcrumb = new Breadcrumb();
    }
}

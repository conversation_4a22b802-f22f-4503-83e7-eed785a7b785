<?php declare(strict_types=1);

namespace App\Entity\Content;

use ApiPlatform\Metadata as APM;
use App\ApiResource\FlatBlock;
use App\Doctrine\Enum\BlockTypeEnum;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Traits\CreatedAtTrait;
use App\Entity\Traits\PublishedAtTrait;
use App\Entity\Traits\UpdatedAtTrait;
use App\Entity\Traits\UuidTrait;
use App\Repository\ContentBlockRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: ContentBlockRepository::class)]
#[ORM\Table(name: TableEnum::CONTENT_BLOCK)]
class ContentBlock
{
    use UuidTrait;
    use CreatedAtTrait;
    use UpdatedAtTrait;
    use PublishedAtTrait;

    #[ORM\Column(nullable: true)]
    #[Assert\NotBlank(allowNull: true)]
    public ?string $name = null;

    #[ORM\Column]
    #[APM\ApiProperty(openapiContext: ['enum' => BlockTypeEnum::ALL])]
    #[Assert\NotNull]
    #[Assert\Choice(choices: BlockTypeEnum::ALL)]
    public ?string $type = null;

    #[ORM\Column(type: Types::JSON)]
    public array $parameters = [];

    public function __construct()
    {
        $this->defineUuid();
    }

    public function toFlat(): ?FlatBlock
    {
        $flat = new FlatBlock();

        $flat->uuid = $this->uuid;
        $flat->name = $this->name;
        $flat->type = $this->type;
        $flat->parameters = $this->parameters;

        return $flat;
    }
}

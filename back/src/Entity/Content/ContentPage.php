<?php declare(strict_types=1);

namespace App\Entity\Content;

use App\Doctrine\Enum\TableEnum;
use App\Entity\Traits\CreatedAtTrait;
use App\Entity\Traits\PublishedAtTrait;
use App\Entity\Traits\UpdatedAtTrait;
use App\Entity\Traits\UuidTrait;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: TableEnum::CONTENT_PAGE)]
class ContentPage
{
    use UuidTrait;
    use CreatedAtTrait;
    use UpdatedAtTrait;
    use PublishedAtTrait;

    public function __construct()
    {
        $this->defineUuid();
    }
}

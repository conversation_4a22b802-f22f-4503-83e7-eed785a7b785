<?php

declare(strict_types=1);

namespace App\Entity;

use ApiPlatform\Doctrine\Orm\Filter\OrderFilter;
use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Metadata as APM;
use App\Api\Filter\QFilter;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Traits\CreatedAtTrait;
use App\Entity\Traits\UpdatedAtTrait;
use App\Entity\Traits\UuidTrait;
use App\Security\Api\Processor\UserPostProcessor;
use App\Security\Api\Provider\UserProvider;
use App\Security\Enum\RoleEnum;
use App\Security\Voter\UserVoter;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\Ignore;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity]
#[ORM\Table(name: TableEnum::USER)]
#[ORM\UniqueConstraint(fields: ['email'])]
#[APM\GetCollection(security: 'is_granted("' . UserVoter::GET_COLLECTION . '")')]
#[APM\Get(security: 'is_granted("' . UserVoter::GET . '", object)', provider: UserProvider::class)]
#[APM\Get(uriTemplate: '/user', security: 'is_granted("' . UserVoter::GET . '", object)', provider: UserProvider::class)] // @todo we can't glitch a "0" users with UUID
#[APM\Post(denormalizationContext: ['groups' => [self::WRITE_CREATE_CONTEXT, self::WRITE_CONTEXT]], securityPostDenormalize: 'is_granted("' . UserVoter::POST . '", object)', processor: UserPostProcessor::class)]
#[APM\Put(denormalizationContext: ['groups' => [self::WRITE_CONTEXT]], security: 'is_granted("' . UserVoter::PUT . '", object)')]
#[APM\Delete(security: 'is_granted("' . UserVoter::DELETE . '", object)')]
#[UniqueEntity(fields: ['email'])]
#[APM\ApiFilter(QFilter::class, properties: ['email', 'lastname', 'firstname'])]
#[APM\ApiFilter(OrderFilter::class, properties: ['lastname' => 'ASC', 'firstname' => 'ASC', 'email' => 'ASC'])]
#[APM\ApiFilter(SearchFilter::class, properties: ['role' => 'exact'])]
class User implements UserInterface, PasswordAuthenticatedUserInterface
{
    private const string WRITE_CREATE_CONTEXT = 'user:write:create';
    private const string WRITE_CONTEXT = 'user:create';

    use UuidTrait;
    use CreatedAtTrait;
    use UpdatedAtTrait;

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\Email]
    #[Groups([self::WRITE_CONTEXT])]
    public ?string $email = null;

    #[ORM\Column]
    #[Ignore]
    public ?string $password = null;

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Groups([self::WRITE_CONTEXT])]
    public ?string $lastname = null;

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Groups([self::WRITE_CONTEXT])]
    public ?string $firstname = null;

    #[ORM\Column]
    #[Assert\NotNull]
    #[Assert\Choice(choices: RoleEnum::ALL)]
    #[Groups([self::WRITE_CONTEXT])]
    public ?string $role = RoleEnum::USER;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'upload_uuid', referencedColumnName: 'uuid', nullable: true, onDelete: 'SET NULL')]
    public ?Upload $upload = null;

    public function __construct()
    {
        $this->defineUuid();
    }

    #[Ignore]
    public function getUserIdentifier(): string
    {
        return (string)$this->email;
    }

    #[Ignore]
    public function getRoles(): array
    {
        return [$this->role];
    }

    #[Ignore]
    public function getPassword(): ?string
    {
        return $this->password;
    }

    #[Ignore]
    public function isSuperAdmin(): bool
    {
        return RoleEnum::SUPER_ADMIN === $this->role;
    }

    public function eraseCredentials(): void
    {
    }
}

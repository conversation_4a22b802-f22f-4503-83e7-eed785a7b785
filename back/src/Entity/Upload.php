<?php declare(strict_types=1);

namespace App\Entity;

use ApiPlatform\Doctrine\Orm\Filter\BooleanFilter;
use ApiPlatform\Metadata as APM;
use ApiPlatform\OpenApi\Model\Operation;
use ApiPlatform\OpenApi\Model\RequestBody;
use App\Api\Action\UploadGetBinaryAction;
use App\Api\Action\UploadPostAction;
use App\Api\Filter\QFilter;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Traits\CreatedAtTrait;
use App\Entity\Traits\UpdatedAtTrait;
use App\Entity\Traits\UuidTrait;
use App\Security\Voter\UploadVoter;
use ArrayObject;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity]
#[ORM\Table(name: TableEnum::UPLOAD)]
#[APM\GetCollection(paginationClientEnabled: true, security: 'is_granted("' . UploadVoter::GET_COLLECTION . '")')]
#[APM\ApiFilter(QFilter::class, properties: ['name'])]
#[APM\Get(security: 'is_granted("' . UploadVoter::GET . '", object)')]
#[APM\Get(
    uriTemplate: '/uploads/{uuid}/binary',
    controller: UploadGetBinaryAction::class,
    security: 'is_granted("' . UploadVoter::GET . '", object)',
    read: false,
)]
#[APM\Post(
    inputFormats: ['multipart' => ['multipart/form-data']],
    controller: UploadPostAction::class,
    openapi: new Operation(
        requestBody: new RequestBody(
            description: 'Upload a file',
            content: new ArrayObject([
                'multipart/form-data' => [
                    'schema' => [
                        'type' => 'object',
                        'properties' => [
                            'file' => [
                                'type' => 'string',
                                'format' => 'binary',
                            ],
                            'alt' => [
                                'type' => 'string',
                            ],
                            'isPublic' => [
                                'type' => 'boolean',
                            ],
                        ],
                    ],
                ],
            ]),
            required: true,
        ),
    ),
    securityPostDenormalize: 'is_granted("' . UploadVoter::POST . '", object)',
    deserialize: false,
)]
#[APM\Put(denormalizationContext: ['groups' => [self::UPDATE_CONTEXT]])]
#[APM\Delete(security: 'is_granted("' . UploadVoter::DELETE . '", object)')]
#[APM\ApiFilter(BooleanFilter::class, properties: ['isPublic'])]
class Upload
{
    private const string UPDATE_CONTEXT = 'upload:update';

    use UuidTrait;
    use CreatedAtTrait;
    use UpdatedAtTrait;

    #[ORM\Column]
    #[Assert\NotNull]
    #[Assert\Length(max: 255)]
    public ?string $name = null;

    #[ORM\Column(nullable: true)]
    #[Assert\Length(max: 255)]
    #[Groups([self::UPDATE_CONTEXT])]
    public ?string $alt = null;

    #[ORM\Column]
    #[Assert\NotNull]
    #[Assert\Length(max: 255)]
    public ?string $filename = null;

    #[ORM\Column]
    #[Assert\NotNull]
    #[Assert\Length(max: 255)]
    public ?string $type = null;

    #[ORM\Column]
    #[Assert\NotNull]
    public ?int $size = null;

    #[ORM\Column(options: ['default' => 0])]
    public bool $isPublic = false;

    #[ORM\Column(nullable: true)]
    public ?int $width = null;

    #[ORM\Column(nullable: true)]
    public ?int $height = null;

    public function __construct()
    {
        $this->defineUuid();
    }
}

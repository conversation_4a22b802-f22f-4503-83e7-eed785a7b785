<?php declare(strict_types=1);

namespace App\Entity\Traits;

use ApiPlatform\Metadata as APM;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

trait UuidTrait
{
    #[APM\ApiProperty(writable: false, identifier: true)]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'NONE')]
    public ?Uuid $uuid = null;

    public function defineUuid(): void
    {
        $this->uuid ??= Uuid::v4();
    }
}

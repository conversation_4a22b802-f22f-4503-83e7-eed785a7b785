<?php declare(strict_types=1);

namespace App\Entity\Traits;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

trait SeoTrait
{
    #[ORM\Column(nullable: true)]
    #[Assert\NotBlank(allowNull: true)]
    #[Assert\Length(max: 255)]
    public ?string $seoTitle = null;

    #[ORM\Column(length: 1000, nullable: true)]
    #[Assert\NotBlank(allowNull: true)]
    #[Assert\Length(max: 1000)]
    public ?string $seoDescription = null;
}

<?php declare(strict_types=1);

namespace App\Entity\Traits;

use App\Entity\Content\Content;
use App\Validator\Constraints\UrlOrPath;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Contain ether an url+label or a content
 * if both are filled, content always take priority on url/label duo.
 */
trait LinkTrait
{
    #[ORM\Column(nullable: true)]
    #[Assert\Length(max: 255)]
    #[UrlOrPath]
    public ?string $url = null;

    #[ORM\Column(nullable: true)]
    #[Assert\NotBlank(allowNull: true)]
    #[Assert\Length(max: 255)]
    public ?string $label = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'content_uuid', referencedColumnName: 'uuid', nullable: true, onDelete: 'CASCADE')]
    public ?Content $content = null;

    #[ORM\Column(nullable: true)]
    #[Assert\NotBlank(allowNull: true)]
    #[Assert\Length(max: 255)]
    public ?string $anchor = null;
}

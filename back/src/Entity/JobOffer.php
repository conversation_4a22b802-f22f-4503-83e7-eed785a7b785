<?php

declare(strict_types=1);

namespace App\Entity;

use ApiPlatform\Doctrine\Orm\Filter\BooleanFilter;
use ApiPlatform\Doctrine\Orm\Filter\OrderFilter;
use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata as APM;
use App\Api\Filter\QFilter;
use App\Api\Filter\UuidFilter;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Agency\Agency;
use App\Entity\Traits\CreatedAtTrait;
use App\Entity\Traits\UpdatedAtTrait;
use App\Entity\Traits\UuidTrait;
use App\Security\Voter\JobOfferVoter;
use App\Validator\Constraints\Slug;
use DateTime;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Table(name: TableEnum::JOB_OFFER)]
#[ORM\Entity]
#[UniqueEntity('reference')]
#[APM\GetCollection(security: 'is_granted("' . JobOfferVoter::GET_COLLECTION . '")')]
#[APM\Post(securityPostDenormalize: 'is_granted("' . JobOfferVoter::POST . '", object)')]
#[APM\Get(security: 'is_granted("' . JobOfferVoter::GET . '", object)')]
#[APM\Put(security: 'is_granted("' . JobOfferVoter::PUT . '", object)')]
#[APM\Delete(security: 'is_granted("' . JobOfferVoter::DELETE . '", object)')]
#[APM\ApiFilter(OrderFilter::class, properties: ['createdAt' => 'DESC', 'publicationDate' => 'DESC', 'title' => 'ASC', 'reference' => 'ASC', 'jobName' => 'ASC', 'location' => 'ASC'])]
#[APM\ApiFilter(QFilter::class, properties: ['title', 'reference', 'jobName', 'location'])]
#[APM\ApiFilter(SearchFilter::class, properties: ['reference' => 'exact'])]
#[APM\ApiFilter(BooleanFilter::class, properties: ['status'])]
#[APM\ApiFilter(UuidFilter::class, properties: ['agency'])]
class JobOffer
{
    use UuidTrait;
    use CreatedAtTrait;
    use UpdatedAtTrait;

    #[ORM\Column]
    #[Assert\NotBlank]
    public ?string $location = null;

    #[ORM\Column]
    #[Assert\NotBlank]
    public ?string $title = null;

    #[ORM\Column(type: Types::TEXT)]
    #[Assert\NotBlank]
    public ?string $description = null;

    #[ORM\Column(unique: true)]
    #[Slug]
    #[Assert\NotBlank]
    public ?string $reference = null;

    #[ORM\Column]
    #[Assert\NotBlank]
    public ?string $jobName = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'agency_uuid', referencedColumnName: 'uuid', nullable: true, onDelete: 'CASCADE')]
    #[ApiProperty(readableLink: true)]
    public ?Agency $agency = null;

    #[ORM\Column(nullable: true)]
    public ?DateTime $publicationDate = null;

    #[ORM\Column(nullable: true)]
    public ?bool $status = null;

    public function __construct()
    {
        $this->defineUuid();
    }
}

<?php

declare(strict_types=1);

namespace App\Entity\Agency;

use ApiPlatform\Doctrine\Orm\Filter\OrderFilter;
use ApiPlatform\Metadata as APM;
use App\Api\Filter\QFilter;
use App\Api\Input\AgencyUpdateAreaInput;
use App\Api\Processor\AgencyAreaUpdateProcessor;
use App\Api\Provider\AgencyMapCollectionProvider;
use App\Api\Provider\AgencyProvider;
use App\Api\Provider\MyAgencyCollectionProvider;
use App\Bridge\Lyra\Entity\LyraSeller;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Agency\Embedded\Advantage;
use App\Entity\Agency\Embedded\Contact;
use App\Entity\Agency\Embedded\Location;
use App\Entity\Agency\Embedded\Meta;
use App\Entity\Agency\Embedded\RatingWidget;
use App\Entity\Agency\Model\Schedule;
use App\Entity\Traits\CreatedAtTrait;
use App\Entity\Traits\UpdatedAtTrait;
use App\Entity\Upload;
use App\Security\Enum\RoleEnum;
use App\Security\Voter\AgencyVoter;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Dunglas\DoctrineJsonOdm\Type\JsonDocumentType;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Serializer\Attribute\SerializedPath;
use Symfony\Component\Uid\Uuid;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity]
#[ORM\Table(name: TableEnum::AGENCY)]
#[APM\GetCollection(paginationClientEnabled: true, paginationClientItemsPerPage: true, security: 'is_granted("' . AgencyVoter::GET_COLLECTION . '")')]
#[APM\GetCollection(uriTemplate: '/agencies-map', paginationClientEnabled: true, paginationClientItemsPerPage: true, security: 'is_granted("' . AgencyVoter::GET_COLLECTION . '")', provider: AgencyMapCollectionProvider::class)]
#[APM\GetCollection(uriTemplate: '/my-agencies', security: 'is_granted("' . AgencyVoter::GET_COLLECTION . '")', provider: MyAgencyCollectionProvider::class)]
#[APM\Post(securityPostDenormalize: 'is_granted("' . AgencyVoter::POST . '", object)')]
#[APM\Post(uriTemplate: '/agencies/update-areas', security: 'is_granted("' . RoleEnum::SUPER_ADMIN . '", object)', input: AgencyUpdateAreaInput::class, processor: AgencyAreaUpdateProcessor::class)]
#[APM\Get(security: 'is_granted("' . AgencyVoter::GET . '", object)', provider: AgencyProvider::class)]
#[APM\Put(security: 'is_granted("' . AgencyVoter::PUT . '", object)', provider: AgencyProvider::class, extraProperties: ['standard_put' => false])]
#[APM\Delete(security: 'is_granted("' . AgencyVoter::DELETE . '", object)', provider: AgencyProvider::class)]
#[APM\ApiFilter(OrderFilter::class, properties: ['name' => 'ASC', 'location.city' => 'ASC', 'location.postcode' => 'ASC', 'contact.name' => 'ASC'])]
#[APM\ApiFilter(QFilter::class, properties: [
    'name',
    'location.city',
    'location.postcode',
    'contact.email',
    'contact.phone',
    'contact.name',
])]
#[UniqueEntity(fields: ['legacyId'])]
class Agency
{
    use CreatedAtTrait;
    use UpdatedAtTrait;

    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'NONE')]
    #[APM\ApiProperty(identifier: false)]
    public ?Uuid $uuid = null;

    #[ORM\Column(unique: true)]
    #[APM\ApiProperty(writable: false, identifier: true)]
    public ?int $legacyId = null;

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    public ?string $name = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'upload_uuid', referencedColumnName: 'uuid', nullable: true, onDelete: 'SET NULL')]
    public ?Upload $upload = null;

    #[ORM\Column(type: 'string', length: 20)]
    #[Assert\Length(min: 1, max: 20)]
    public ?string $freeCallId = null;

    #[ORM\Embedded]
    #[Assert\NotNull]
    #[Assert\Valid]
    public ?Location $location = null;

    #[ORM\Embedded]
    #[Assert\NotNull]
    #[Assert\Valid]
    public ?Contact $contact = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'contact_upload_uuid', referencedColumnName: 'uuid', nullable: true, onDelete: 'SET NULL')]
    #[SerializedPath('[contact][upload]')]
    public ?Upload $contatUpload = null; // @todo in api, this input is in "contact.upload", don't know how to properly document it

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    #[APM\ApiProperty(description: 'This might be better as an array/select')]
    public ?string $certifications = null;

    #[ORM\Embedded]
    #[Assert\NotNull]
    #[Assert\Valid]
    public ?Advantage $advantage = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    // @todo validation on this html
    public ?string $description = null; // was previously "Texte de référencement"

    #[ORM\Embedded]
    #[Assert\NotNull]
    #[Assert\Valid]
    public ?RatingWidget $ratingWidget = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'lyra_seller_uuid', referencedColumnName: 'uuid', nullable: true, onDelete: 'SET NULL')]
    public ?LyraSeller $lyraSeller = null;

    #[ORM\Column(name: 'lyra_seller', nullable: true)] // @todo delete when migration is done in front
    public ?string $lyraSellerOld = null;

    #[ORM\Column(type: JsonDocumentType::NAME)]
    #[Assert\Valid]
    public Schedule $schedule;

    #[ORM\Column(options: ['default' => '0'])]
    public bool $displayAppointment = false;

    #[ORM\Embedded]
    #[Assert\NotNull]
    #[Assert\Valid]
    public ?Meta $meta = null;

    #[ORM\Column(options: ['default' => '0'])]
    public bool $enablePaymentThreeTimeNoFee = false;

    #[ORM\Column(options: ['default' => '0'])]
    public bool $displayCallIfAvailableNow = false;

    #[APM\ApiProperty(writable: false)]
    public ?int $priceGridType = null;

    #[ORM\Column(nullable: true)]
    #[Assert\Url]
    public ?string $linkToOpen = null;

    #[ORM\Column(nullable: true)]
    #[Assert\Url]
    public ?string $facebookLink = null;

    #[ORM\Column(nullable: true)]
    #[Assert\Url]
    public ?string $linkedinLink = null;

    public function __construct()
    {
        $this->defineUuid();
        $this->location = new Location();
        $this->contact = new Contact();
        $this->advantage = new Advantage();
        $this->ratingWidget = new RatingWidget();
        $this->schedule = new Schedule();
        $this->meta = new Meta();
    }

    public function defineUuid(): void
    {
        $this->uuid ??= Uuid::v4();
    }

    public function getIsAvailable(): bool
    {
        if (false === $this->displayCallIfAvailableNow) {
            return false;
        }

        return $this->schedule->isAvailableNow();
    }
}

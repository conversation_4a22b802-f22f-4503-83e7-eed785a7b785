<?php declare(strict_types=1);

namespace App\Entity\Agency\Model;

use Symfony\Component\Validator\Constraints as Assert;

class ScheduleDay
{
    public const string TYPE = 'agency_schedule_day';

    #[Assert\Valid]
    public ?SchedulePeriod $morning = null;

    #[Assert\Valid]
    public ?SchedulePeriod $afternoon = null;

    public function toString(): string
    {
        $morning = $afternoon = null;

        if (
            $this->morning?->openAt
            && $this->morning?->closeAt
        ) {
            $morning = $this->morning->openAt->format('H:i') . ' à ' . $this->morning->closeAt->format('H:i');
        }

        if (
            $this->afternoon?->openAt
            && $this->afternoon?->closeAt
        ) {
            $afternoon = $this->afternoon->openAt->format('H:i') . ' à ' . $this->afternoon->closeAt->format('H:i');
        }

        if (null === $morning && null === $afternoon) {
            return 'Fermé';
        }

        if (null === $morning) {
            return $afternoon;
        }

        if (null === $afternoon) {
            return $morning;
        }

        return $morning . ' - ' . $afternoon;
    }
}

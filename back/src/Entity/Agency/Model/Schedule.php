<?php declare(strict_types=1);

namespace App\Entity\Agency\Model;

use DateInterval;
use DateTimeImmutable;
use DateTimeZone;
use Symfony\Component\Serializer\Attribute\Ignore;
use Symfony\Component\Validator\Constraints as Assert;

class Schedule
{
    public const string TYPE = 'agency_schedule';

    #[Assert\Valid]
    public ?ScheduleDay $monday = null;

    #[Assert\Valid]
    public ?ScheduleDay $tuesday = null;

    #[Assert\Valid]
    public ?ScheduleDay $wednesday = null;

    #[Assert\Valid]
    public ?ScheduleDay $thursday = null;

    #[Assert\Valid]
    public ?ScheduleDay $friday = null;

    #[Assert\Valid]
    public ?ScheduleDay $saturday = null;

    #[Assert\Valid]
    public ?ScheduleDay $sunday = null;

    #[Ignore]
    public function isAvailableNow(): bool
    {
        $fr = new DateTimeZone('Europe/Paris');
        $now = new DateTimeImmutable(timezone: $fr);
        $day = mb_strtolower($now->format('l'));

        $now = $now->add(new DateInterval('PT1H'));

        /** @var ScheduleDay $scheduleDay */
        $scheduleDay = $this->{$day};

        if (null !== $scheduleDay->morning?->openAt && null !== $scheduleDay->morning?->closeAt) {
            if ($now > $scheduleDay->morning?->openAt && $now < $scheduleDay->morning?->closeAt) {
                return true;
            }
        }

        if (null !== $scheduleDay->afternoon?->openAt && null !== $scheduleDay->afternoon?->closeAt) {
            if ($now > $scheduleDay->afternoon?->openAt && $now < $scheduleDay->afternoon?->closeAt) {
                return true;
            }
        }

        return false;
    }

    public function getDay()
    {
        $fr = new DateTimeZone('Europe/Paris');
        $now = new DateTimeImmutable(timezone: $fr);
        $day = mb_strtolower($now->format('l'));

        /** @var ScheduleDay $scheduleDay */
        return $this->{$day};
    }
}

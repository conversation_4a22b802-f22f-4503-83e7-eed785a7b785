<?php declare(strict_types=1);

namespace App\Entity\Agency\Model;

use DateTime;
use Symfony\Component\Serializer\Attribute\Context;
use Symfony\Component\Serializer\Normalizer\DateTimeNormalizer;
use Symfony\Component\Validator\Constraints as Assert;

class SchedulePeriod
{
    public const string TYPE = 'agency_schedule_period';

    #[Assert\LessThan(propertyPath: 'closeAt')]
    #[Context([DateTimeNormalizer::FORMAT_KEY => 'H:i'])]
    public ?DateTime $openAt = null;

    #[Assert\GreaterThan(propertyPath: 'openAt')]
    #[Context([DateTimeNormalizer::FORMAT_KEY => 'H:i'])]
    public ?DateTime $closeAt = null;
}

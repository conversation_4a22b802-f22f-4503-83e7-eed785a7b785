<?php declare(strict_types=1);

namespace App\Entity\Agency\Embedded;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Embeddable]
class Meta
{
    #[ORM\Column(length: 150, nullable: true)]
    #[Assert\Length(min: 1, max: 150)]
    public ?string $title = null;

    #[ORM\Column(nullable: true)]
    #[Assert\Length(min: 1, max: 255)]
    public ?string $description = null;
}

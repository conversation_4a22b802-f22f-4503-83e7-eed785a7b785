<?php declare(strict_types=1);

namespace App\Entity\Agency\Embedded;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Embeddable]
class Advantage
{
    #[ORM\Column(length: 50, nullable: true)]
    #[Assert\NotBlank(allowNull: true)]
    #[Assert\Length(max: 50)]
    public ?string $code = null;

    #[ORM\Column(type: Types::SMALLINT, nullable: true)]
    #[Assert\Range(min: 0, max: 100)]
    public ?int $percent = null;
}

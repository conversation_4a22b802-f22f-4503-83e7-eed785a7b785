<?php declare(strict_types=1);

namespace App\Entity\Agency\Embedded;

use ApiPlatform\Metadata\ApiProperty;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use function is_string;

#[ORM\Embeddable]
class Location
{
    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    public ?string $address1 = null;

    #[ORM\Column(nullable: true)]
    #[Assert\NotBlank(allowNull: true)]
    #[Assert\Length(max: 255)]
    public ?string $address2 = null;

    #[ORM\Column(length: 100)]
    #[Assert\NotBlank]
    #[Assert\Length(max: 100)]
    public ?string $city = null;

    #[ORM\Column(length: 10)]
    #[Assert\NotBlank]
    #[Assert\Length(max: 10)]
    public ?string $postcode = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 20, scale: 16, nullable: true)]
    #[ApiProperty(writable: false)]
    public ?float $latitude = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 20, scale: 16, nullable: true)]
    #[ApiProperty(writable: false)]
    public ?float $longitude = null;

    public function setLatitude(float|string|null $latitude): void
    {
        if (is_string($latitude)) {
            $latitude = (float)str_replace(',', '.', $latitude);
        }

        $this->latitude = $latitude;
    }

    public function setLongitude(float|string|null $longitude): void
    {
        if (is_string($longitude)) {
            $longitude = (float)str_replace(',', '.', $longitude);
        }

        $this->longitude = $longitude;
    }
}

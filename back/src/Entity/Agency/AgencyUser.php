<?php

declare(strict_types=1);

namespace App\Entity\Agency;

use ApiPlatform\Metadata as APM;
use App\Api\Filter\UuidFilter;
use App\Api\Model\LinkUserAgency;
use App\Api\Processor\LinkUserAgencyProcessor;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Traits\CreatedAtTrait;
use App\Entity\Traits\UuidTrait;
use App\Entity\User;
use App\Security\Enum\RoleEnum;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity]
#[ORM\Table(name: TableEnum::AGENCY_USER)]
#[APM\GetCollection(paginationClientItemsPerPage: true, security: 'is_granted("' . RoleEnum::SUPER_ADMIN . '")')]
#[APM\Put(uriTemplate: '/agency-users', security: 'is_granted("' . RoleEnum::SUPER_ADMIN . '")', input: LinkUserAgency::class, output: LinkUserAgency::class, processor: LinkUserAgencyProcessor::class)]
#[APM\Delete(security: 'is_granted("' . RoleEnum::SUPER_ADMIN . '")')]
#[APM\ApiFilter(UuidFilter::class, properties: ['user'])]
class AgencyUser
{
    use UuidTrait;
    use CreatedAtTrait;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'user_uuid', referencedColumnName: 'uuid', nullable: false, onDelete: 'CASCADE')]
    #[Assert\NotNull]
    public ?User $user = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'agency_uuid', referencedColumnName: 'uuid', nullable: false, onDelete: 'CASCADE')]
    #[Assert\NotNull]
    #[APM\ApiProperty(readableLink: true, writableLink: false)]
    public ?Agency $agency = null;

    public function __construct()
    {
        $this->defineUuid();
    }
}

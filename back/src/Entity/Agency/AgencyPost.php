<?php

declare(strict_types=1);

namespace App\Entity\Agency;

use ApiPlatform\Doctrine\Orm\Filter\ExistsFilter;
use ApiPlatform\Doctrine\Orm\Filter\OrderFilter;
use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Metadata as APM;
use App\Api\Filter\QFilter;
use App\Api\Filter\UuidFilter;
use App\Api\Provider\AgencyPostProvider;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Traits\CreatedAtTrait;
use App\Entity\Traits\UpdatedAtTrait;
use App\Entity\Traits\UuidTrait;
use App\Entity\Upload;
use App\Security\Voter\PostVoter;
use App\Validator\Constraints\Slug;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity]
#[ORM\Table(name: TableEnum::AGENCY_POST)]
#[APM\GetCollection(paginationClientItemsPerPage: true, security: 'is_granted("' . PostVoter::GET_COLLECTION . '")')]
#[APM\Post(securityPostDenormalize: 'is_granted("' . PostVoter::POST . '", object)')]
#[APM\Get(security: 'is_granted("' . PostVoter::GET . '", object)')]
#[APM\Get(uriTemplate: '/agency-post', uriVariables: [], provider: AgencyPostProvider::class)]
#[APM\Put(security: 'is_granted("' . PostVoter::DELETE . '", object)')]
#[APM\Delete(security: 'is_granted("' . PostVoter::DELETE . '", object)')]
#[APM\ApiFilter(UuidFilter::class, properties: ['agency.uuid'])]
#[APM\ApiFilter(SearchFilter::class, properties: ['title' => 'partial', 'url' => 'exact', 'agency.legacyId' => 'exact'])]
#[APM\ApiFilter(OrderFilter::class, properties: ['createdAt' => 'DESC', 'title' => 'ASC', 'agency.name' => 'ASC'])]
#[APM\ApiFilter(ExistsFilter::class, properties: ['agency'])]
#[APM\ApiFilter(QFilter::class, properties: ['title', 'url'])]
class AgencyPost
{
    use UuidTrait;
    use CreatedAtTrait;
    use UpdatedAtTrait;

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    public ?string $title = null;

    #[ORM\Column(type: Types::TEXT)]
    #[Assert\NotBlank]
    public ?string $content = null;

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    #[Slug]
    public ?string $url = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'agency_uuid', referencedColumnName: 'uuid', nullable: false, onDelete: 'CASCADE')]
    #[APM\ApiProperty(readableLink: true)]
    public ?Agency $agency = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'upload_uuid', referencedColumnName: 'uuid', nullable: true, onDelete: 'SET NULL')]
    public ?Upload $upload = null;

    public function __construct()
    {
        $this->defineUuid();
    }
}

<?php

declare(strict_types=1);

namespace App\Entity;

use ApiPlatform\Metadata as APM;
use App\Api\Processor\SettingProcessor;
use App\Api\Provider\SettingProvider;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Traits\CreatedAtTrait;
use App\Entity\Traits\UpdatedAtTrait;
use App\Entity\Traits\UuidTrait;
use App\Repository\SettingRepository;
use App\Security\Enum\RoleEnum;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: SettingRepository::class)]
#[ORM\Table(name: TableEnum::SETTING)]
#[APM\Get(uriTemplate: '/setting', provider: SettingProvider::class)]
#[APM\Put(uriTemplate: '/setting', security: 'is_granted("' . RoleEnum::SUPER_ADMIN . '", object)', provider: SettingProvider::class, processor: SettingProcessor::class)]
class Setting
{
    use UuidTrait;
    use CreatedAtTrait;
    use UpdatedAtTrait;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    public ?string $tagManager = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    public ?string $searchLink = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    public ?string $quoteLink = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    public ?string $html = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    public ?string $js = null;

    public function __construct()
    {
        $this->defineUuid();
    }
}

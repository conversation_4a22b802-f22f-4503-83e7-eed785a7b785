<?php declare(strict_types=1);

namespace App\Entity\Header;

use App\Doctrine\Enum\TableEnum;
use App\Entity\Traits\CreatedAtTrait;
use App\Entity\Traits\LogoTrait;
use App\Entity\Traits\UpdatedAtTrait;
use App\Entity\Traits\UuidTrait;
use App\Repository\HeaderRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: HeaderRepository::class)]
#[ORM\Table(name: TableEnum::HEADER)]
class Header
{
    use UuidTrait;
    use CreatedAtTrait;
    use UpdatedAtTrait;
    use LogoTrait;

    public function __construct()
    {
        $this->defineUuid();
    }
}

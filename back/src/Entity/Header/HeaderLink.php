<?php declare(strict_types=1);

namespace App\Entity\Header;

use App\Doctrine\Enum\TableEnum;
use App\Entity\Traits\CreatedAtTrait;
use App\Entity\Traits\LinkTrait;
use App\Entity\Traits\PositionTrait;
use App\Entity\Traits\UpdatedAtTrait;
use App\Entity\Traits\UuidTrait;
use App\Entity\Upload;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity]
#[ORM\Table(name: TableEnum::HEADER_LINK)]
class HeaderLink
{
    use UuidTrait;
    use CreatedAtTrait;
    use UpdatedAtTrait;
    use PositionTrait;
    use LinkTrait;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'header_uuid', referencedColumnName: 'uuid', nullable: false, onDelete: 'CASCADE')]
    #[Assert\NotNull]
    public ?Header $header = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'upload_uuid', referencedColumnName: 'uuid', onDelete: 'SET NULL')]
    public ?Upload $upload = null;

    public function __construct()
    {
        $this->defineUuid();
    }
}

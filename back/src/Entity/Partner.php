<?php

declare(strict_types=1);

namespace App\Entity;

use ApiPlatform\Doctrine\Orm\Filter\OrderFilter;
use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Metadata as APM;
use App\Api\Filter\QFilter;
use App\Api\Filter\UuidFilter;
use App\Business\Enum\PartnerActivityEnum;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Location\LocationDepartment;
use App\Entity\Traits\CreatedAtTrait;
use App\Entity\Traits\UpdatedAtTrait;
use App\Entity\Traits\UuidTrait;
use App\Security\Enum\RoleEnum;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity]
#[ORM\Table(name: TableEnum::PARTNER)]
#[APM\GetCollection(paginationClientItemsPerPage: true, security: 'is_granted("' . RoleEnum::PUBLIC_ACCESS . '")')]
#[APM\Post(securityPostDenormalize: 'is_granted("' . RoleEnum::SUPER_ADMIN . '", object)')]
#[APM\Get(security: 'is_granted("' . RoleEnum::PUBLIC_ACCESS . '", object)')]
#[APM\Put(security: 'is_granted("' . RoleEnum::SUPER_ADMIN . '", object)')]
#[APM\Delete(security: 'is_granted("' . RoleEnum::SUPER_ADMIN . '", object)')]
#[APM\ApiFilter(OrderFilter::class, properties: ['createdAt' => 'DESC', 'name' => 'ASC', 'activity' => 'ASC', 'department.name' => 'ASC', 'link' => 'ASC'])]
#[APM\ApiFilter(SearchFilter::class, properties: ['activity' => 'exact'])]
#[APM\ApiFilter(UuidFilter::class, properties: ['department'])]
#[APM\ApiFilter(QFilter::class, properties: ['name', 'activity', 'link'])]
class Partner
{
    use UuidTrait;
    use CreatedAtTrait;
    use UpdatedAtTrait;

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    public ?string $name = null;

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    #[Assert\Url]
    public ?string $link = null;

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\Choice(choices: PartnerActivityEnum::ALL)]
    public ?string $activity = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'upload_uuid', referencedColumnName: 'uuid', nullable: true, onDelete: 'SET NULL')]
    #[Assert\NotNull]
    public ?Upload $upload = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'department_uuid', referencedColumnName: 'uuid', nullable: true, onDelete: 'SET NULL')]
    #[APM\ApiProperty(readableLink: true, writableLink: false)]
    public ?LocationDepartment $department = null;

    public function __construct()
    {
        $this->defineUuid();
    }
}

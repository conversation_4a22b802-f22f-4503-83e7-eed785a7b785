<?php declare(strict_types=1);

namespace App\Entity\Footer;

use App\Doctrine\Enum\TableEnum;
use App\Entity\Traits\CreatedAtTrait;
use App\Entity\Traits\LinkTrait;
use App\Entity\Traits\PositionTrait;
use App\Entity\Traits\UpdatedAtTrait;
use App\Entity\Traits\UuidTrait;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity]
#[ORM\Table(name: TableEnum::FOOTER_LINK)]
class FooterLink
{
    use UuidTrait;
    use CreatedAtTrait;
    use UpdatedAtTrait;
    use LinkTrait;
    use PositionTrait;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'footer_uuid', referencedColumnName: 'uuid', nullable: false, onDelete: 'CASCADE')]
    #[Assert\NotNull]
    public ?Footer $footer = null;

    public function __construct()
    {
        $this->defineUuid();
    }
}

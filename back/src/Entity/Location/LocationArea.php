<?php

declare(strict_types=1);

namespace App\Entity\Location;

use ApiPlatform\Doctrine\Orm\Filter\OrderFilter;
use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Metadata as APM;
use App\Api\Filter\QFilter;
use App\Api\Filter\UuidFilter;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Agency\Agency;
use App\Entity\Traits\CreatedAtTrait;
use App\Entity\Traits\UpdatedAtTrait;
use App\Entity\Traits\UuidTrait;
use App\Security\Voter\AreaVoter;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity]
#[ORM\Table(name: TableEnum::LOCATION_AREA)]
#[APM\GetCollection(paginationClientItemsPerPage: true, paginationClientEnabled: true, security: 'is_granted("' . AreaVoter::GET_COLLECTION . '")')]
#[APM\Post(securityPostDenormalize: 'is_granted("' . AreaVoter::POST . '", object)')]
#[APM\Get(security: 'is_granted("' . AreaVoter::GET . '", object)')]
#[APM\Put(security: 'is_granted("' . AreaVoter::DELETE . '", object)')]
#[APM\Delete(security: 'is_granted("' . AreaVoter::DELETE . '", object)')]
#[APM\ApiFilter(UuidFilter::class, properties: ['agency', 'department'])]
#[APM\ApiFilter(SearchFilter::class, properties: ['name' => 'partial'])]
#[APM\ApiFilter(OrderFilter::class, properties: ['name' => 'ASC', 'department.name' => 'ASC', 'agency.name' => 'ASC'])]
#[APM\ApiFilter(QFilter::class, properties: ['name'])]
class LocationArea
{
    use UuidTrait;
    use CreatedAtTrait;
    use UpdatedAtTrait;

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    public ?string $name = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'department_uuid', referencedColumnName: 'uuid', nullable: false, onDelete: 'CASCADE')]
    #[Assert\NotNull]
    #[APM\ApiProperty(readableLink: true, writableLink: false)]
    public ?LocationDepartment $department = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'agency_uuid', referencedColumnName: 'uuid', onDelete: 'SET NULL')]
    #[APM\ApiProperty(readableLink: true, writableLink: false)]
    public ?Agency $agency = null;

    public function __construct()
    {
        $this->defineUuid();
    }
}

<?php declare(strict_types=1);

namespace App\Entity\Location;

use ApiPlatform\Doctrine\Orm\Filter\OrderFilter;
use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Metadata as APM;
use App\Api\Filter\QFilter;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Traits\CreatedAtTrait;
use App\Entity\Traits\UpdatedAtTrait;
use App\Entity\Traits\UuidTrait;
use App\Security\Voter\CityVoter;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity]
#[ORM\Table(name: TableEnum::LOCATION_CITY)]
#[APM\GetCollection(security: 'is_granted("' . CityVoter::GET_COLLECTION . '")')]
#[APM\Post(securityPostDenormalize: 'is_granted("' . CityVoter::POST . '", object)')]
#[APM\Get(security: 'is_granted("' . CityVoter::GET . '", object)')]
#[APM\Put(security: 'is_granted("' . CityVoter::DELETE . '", object)')]
#[APM\Delete(security: 'is_granted("' . CityVoter::DELETE . '", object)')]
#[APM\ApiFilter(SearchFilter::class, properties: ['name' => 'partial', 'zip' => 'partial', 'area' => 'exact'])]
#[APM\ApiFilter(OrderFilter::class, properties: ['name' => 'ASC', 'zip' => 'ASC', 'area.name' => 'ASC'])]
#[APM\ApiFilter(QFilter::class, properties: ['name', 'zip'])]
class LocationCity
{
    use UuidTrait;
    use CreatedAtTrait;
    use UpdatedAtTrait;

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    public ?string $name = null;

    #[ORM\Column(length: 10)]
    #[Assert\NotBlank]
    #[Assert\Length(max: 10)]
    public ?string $zip = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'area_uuid', referencedColumnName: 'uuid', nullable: true, onDelete: 'SET NULL')]
    #[APM\ApiProperty(readableLink: true, writableLink: false)]
    public ?LocationArea $area = null;

    #[ORM\Column(options: ['default' => false])]
    public bool $hasTermite = false;

    public function __construct()
    {
        $this->defineUuid();
    }
}

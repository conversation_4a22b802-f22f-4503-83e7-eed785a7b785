<?php

declare(strict_types=1);

namespace App\Entity\Location;

use ApiPlatform\Doctrine\Orm\Filter\OrderFilter;
use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Metadata as APM;
use App\Api\Filter\QFilter;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Traits\CreatedAtTrait;
use App\Entity\Traits\UpdatedAtTrait;
use App\Entity\Traits\UuidTrait;
use App\Security\Voter\DepartmentVoter;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity]
#[ORM\Table(name: TableEnum::LOCATION_DEPARTMENT)]
#[APM\GetCollection(paginationClientEnabled: true, security: 'is_granted("' . DepartmentVoter::GET_COLLECTION . '")')]
#[APM\Post(securityPostDenormalize: 'is_granted("' . DepartmentVoter::POST . '", object)')]
#[APM\Get(security: 'is_granted("' . DepartmentVoter::GET . '", object)')]
#[APM\Put(security: 'is_granted("' . DepartmentVoter::DELETE . '", object)')]
#[APM\Delete(security: 'is_granted("' . DepartmentVoter::DELETE . '", object)')]
#[APM\ApiFilter(SearchFilter::class, properties: ['name' => 'partial', 'code' => 'partial'])]
#[APM\ApiFilter(OrderFilter::class, properties: ['name' => 'ASC', 'code' => 'ASC'])]
#[APM\ApiFilter(QFilter::class, properties: ['name', 'code'])]
class LocationDepartment
{
    use UuidTrait;
    use CreatedAtTrait;
    use UpdatedAtTrait;

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    public ?string $name = null;

    #[ORM\Column(unique: true)]
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    public ?string $code = null;

    public function __construct()
    {
        $this->defineUuid();
    }
}

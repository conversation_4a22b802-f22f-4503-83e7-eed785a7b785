<?php declare(strict_types=1);

namespace App\Security\Exception;

use Exception;
use Psr\Log\LogLevel;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\WithHttpStatus;
use Symfony\Component\HttpKernel\Attribute\WithLogLevel;

#[WithHttpStatus(Response::HTTP_UNPROCESSABLE_ENTITY)]
#[WithLogLevel(LogLevel::WARNING)]
class InvalidTokenException extends Exception
{
    public function __construct(string $message = 'Invalid token.')
    {
        parent::__construct($message);
    }
}

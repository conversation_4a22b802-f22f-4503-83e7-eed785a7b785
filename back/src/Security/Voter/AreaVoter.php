<?php declare(strict_types=1);

namespace App\Security\Voter;

use App\Entity\Location\LocationArea;
use App\Entity\User;

class AreaVoter extends AbstractCrudVoter
{
    public const string GET_COLLECTION = 'ROLE_AREA_READ';
    public const string POST = 'ROLE_AREA_CREATE';
    public const string GET = 'ROLE_AREA_READ';
    public const string PUT = 'ROLE_AREA_UPDATE';
    public const string DELETE = 'ROLE_AREA_DELETE';

    public function __construct()
    {
        parent::__construct(
            self::GET_COLLECTION,
            self::POST,
            self::GET,
            self::PUT,
            self::DELETE,
            LocationArea::class,
        );
    }

    protected function voteOnUser(string $attribute, mixed $subject, ?User $user): bool
    {
        return match ($attribute) {
            self::GET_COLLECTION,
            self::GET => true,
            default => true === $user?->isSuperAdmin(),
        };
    }
}

<?php declare(strict_types=1);

namespace App\Security\Voter;

use App\Entity\Location\LocationDepartment;
use App\Entity\User;

class DepartmentVoter extends AbstractCrudVoter
{
    public const string GET_COLLECTION = 'ROLE_DEPARTMENT_READ';
    public const string POST = 'ROLE_DEPARTMENT_CREATE';
    public const string GET = 'ROLE_DEPARTMENT_READ';
    public const string PUT = 'ROLE_DEPARTMENT_UPDATE';
    public const string DELETE = 'ROLE_DEPARTMENT_DELETE';

    public function __construct()
    {
        parent::__construct(
            self::GET_COLLECTION,
            self::POST,
            self::GET,
            self::PUT,
            self::DELETE,
            LocationDepartment::class,
        );
    }

    protected function voteOnUser(string $attribute, mixed $subject, ?User $user): bool
    {
        return match ($attribute) {
            self::GET_COLLECTION,
            self::GET => true,
            default => true === $user?->isSuperAdmin(),
        };
    }
}

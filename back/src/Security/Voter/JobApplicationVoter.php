<?php declare(strict_types=1);

namespace App\Security\Voter;

use App\Entity\JobApplication;
use App\Entity\User;
use App\Security\AgencyUserChecker;

class JobApplicationVoter extends AbstractCrudVoter
{
    public const string GET_COLLECTION = 'ROLE_JOB_APPLICATION_READ';
    public const string POST = 'ROLE_JOB_APPLICATION_CREATE';
    public const string GET = 'ROLE_JOB_APPLICATION_READ';
    public const string PUT = 'ROLE_JOB_APPLICATION_UPDATE';
    public const string DELETE = 'ROLE_JOB_APPLICATION_DELETE';

    public function __construct(
        private readonly AgencyUserChecker $agencyUserChecker,
    ) {
        parent::__construct(
            self::GET_COLLECTION,
            self::POST,
            self::GET,
            self::PUT,
            self::DELETE,
            JobApplication::class,
        );
    }

    /** @param JobApplication $subject */
    protected function voteOnUser(string $attribute, mixed $subject, ?User $user): bool
    {
        return match ($attribute) {
            self::POST => true, // for people who want to apply
            self::GET,
            self::GET_COLLECTION,
            self::DELETE => $this->agencyUserChecker->userIsLinkedToAgency($user, $subject->jobOffer->agency),
            default => false,
        };
    }
}

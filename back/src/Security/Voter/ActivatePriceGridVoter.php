<?php declare(strict_types=1);

namespace App\Security\Voter;

use App\Business\Estimate\Api\Resource\ActivatePriceGrid;
use App\Entity\User;
use App\Security\AgencyUserChecker;

class ActivatePriceGridVoter extends AbstractCrudVoter
{
    public const string GET_COLLECTION = 'ROLE_ACTIVATE_PRICE_GRID_READ';
    public const string POST = 'ROLE_ACTIVATE_PRICE_GRID_CREATE';
    public const string GET = 'ROLE_ACTIVATE_PRICE_GRID_READ';
    public const string PUT = 'ROLE_ACTIVATE_PRICE_GRID_UPDATE';
    public const string DELETE = 'ROLE_ACTIVATE_PRICE_GRID_DELETE';

    public function __construct(
        private readonly AgencyUserChecker $agencyUserChecker,
    ) {
        parent::__construct(
            self::GET_COLLECTION,
            self::POST,
            self::GET,
            self::PUT,
            self::DELETE,
            ActivatePriceGrid::class,
        );
    }

    /** @param ActivatePriceGrid $subject */
    protected function voteOnUser(string $attribute, mixed $subject, ?User $user): bool
    {
        return $this->agencyUserChecker->userIsGrantedOnAgency($user, $subject->agency);
    }
}

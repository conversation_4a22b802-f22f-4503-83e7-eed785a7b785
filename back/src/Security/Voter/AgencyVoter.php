<?php declare(strict_types=1);

namespace App\Security\Voter;

use App\Entity\Agency\Agency;
use App\Entity\User;
use App\Security\AgencyUserChecker;

class AgencyVoter extends AbstractCrudVoter
{
    public const string GET_COLLECTION = 'ROLE_AGENCY_READ';
    public const string POST = 'ROLE_AGENCY_CREATE';
    public const string GET = 'ROLE_AGENCY_READ';
    public const string PUT = 'ROLE_AGENCY_UPDATE';
    public const string DELETE = 'ROLE_AGENCY_DELETE';

    public function __construct(
        private readonly AgencyUserChecker $agencyUserChecker,
    ) {
        parent::__construct(
            self::GET_COLLECTION,
            self::POST,
            self::GET,
            self::PUT,
            self::DELETE,
            Agency::class,
        );
    }

    protected function voteOnUser(string $attribute, mixed $subject, ?User $user): bool
    {
        return match ($attribute) {
            self::GET_COLLECTION,
            self::GET => true,
            self::PUT => $this->agencyUserChecker->userIsGrantedOnAgency($user, $subject),
            default => true === $user?->isSuperAdmin(),
        };
    }
}

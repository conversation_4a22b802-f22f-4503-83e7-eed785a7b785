<?php declare(strict_types=1);

namespace App\Security\Voter;

use App\Entity\User;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use function in_array;
use function is_a;

abstract class AbstractCrudVoter extends Voter
{
    public function __construct(
        private readonly string $getCollection,
        private readonly string $post,
        private readonly string $get,
        private readonly string $put,
        private readonly string $delete,
        private readonly string $class,
    ) {
    }

    abstract protected function voteOnUser(
        string $attribute,
        mixed $subject,
        ?User $user,
    ): bool;

    protected function supports(string $attribute, mixed $subject): bool
    {
        return
            null === $subject && $this->getCollection === $attribute
            || is_a($subject, $this->class) && in_array($attribute, [
                $this->post,
                $this->get,
                $this->put,
                $this->delete,
            ], true);
    }

    protected function voteOnAttribute(
        string $attribute,
        mixed $subject,
        TokenInterface $token,
    ): bool {
        $user = $token->getUser();
        if (null !== $user && !$user instanceof User) {
            return false;
        }

        return $this->voteOnUser(
            $attribute,
            $subject,
            $user,
        );
    }
}

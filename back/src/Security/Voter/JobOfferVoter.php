<?php declare(strict_types=1);

namespace App\Security\Voter;

use App\Entity\JobOffer;
use App\Entity\User;
use App\Security\AgencyUserChecker;

class JobOfferVoter extends AbstractCrudVoter
{
    public const string GET_COLLECTION = 'ROLE_JOB_OFFER_READ';
    public const string POST = 'ROLE_JOB_OFFER_CREATE';
    public const string GET = 'ROLE_JOB_OFFER_READ';
    public const string PUT = 'ROLE_JOB_OFFER_UPDATE';
    public const string DELETE = 'ROLE_JOB_OFFER_DELETE';

    public function __construct(
        private readonly AgencyUserChecker $agencyUserChecker,
    ) {
        parent::__construct(
            self::GET_COLLECTION,
            self::POST,
            self::GET,
            self::PUT,
            self::DELETE,
            JobOffer::class,
        );
    }

    /**
     * @param JobOffer $subject
     */
    protected function voteOnUser(string $attribute, mixed $subject, ?User $user): bool
    {
        return match ($attribute) {
            self::GET_COLLECTION,
            self::GET => true,
            default => $this->agencyUserChecker->userIsLinkedToAgency($user, $subject->agency),
        };
    }
}

<?php declare(strict_types=1);

namespace App\Security\Voter;

use App\Entity\Upload;
use App\Entity\User;

class UploadVoter extends AbstractCrudVoter
{
    public const string GET_COLLECTION = 'ROLE_UPLOAD_READ';
    public const string POST = 'ROLE_UPLOAD_CREATE';
    public const string GET = 'ROLE_UPLOAD_READ';
    public const string PUT = 'ROLE_UPLOAD_UPDATE';
    public const string DELETE = 'ROLE_UPLOAD_DELETE';

    public function __construct()
    {
        parent::__construct(
            self::GET_COLLECTION,
            self::POST,
            self::GET,
            self::PUT,
            self::DELETE,
            Upload::class,
        );
    }

    protected function voteOnUser(string $attribute, mixed $subject, ?User $user): bool
    {
        return match ($attribute) {
            self::GET_COLLECTION,
            self::GET,
            self::POST => true,
            default => true === $user?->isSuperAdmin(),
        };
    }
}

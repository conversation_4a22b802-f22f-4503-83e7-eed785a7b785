<?php declare(strict_types=1);

namespace App\Security\Voter;

use App\Entity\Agency\AgencyPost;
use App\Entity\User;
use App\Security\AgencyUserChecker;

class PostVoter extends AbstractCrudVoter
{
    public const string GET_COLLECTION = 'ROLE_POST_READ';
    public const string POST = 'ROLE_POST_CREATE';
    public const string GET = 'ROLE_POST_READ';
    public const string PUT = 'ROLE_POST_UPDATE';
    public const string DELETE = 'ROLE_POST_DELETE';

    public function __construct(
        private readonly AgencyUserChecker $agencyUserChecker,
    ) {
        parent::__construct(
            self::GET_COLLECTION,
            self::POST,
            self::GET,
            self::PUT,
            self::DELETE,
            AgencyPost::class,
        );
    }

    /** @param AgencyPost $subject */
    protected function voteOnUser(string $attribute, mixed $subject, ?User $user): bool
    {
        return match ($attribute) {
            self::GET_COLLECTION,
            self::GET => true,
            default => $this->agencyUserChecker->userIsGrantedOnAgency($user, $subject->agency),
        };
    }
}

<?php declare(strict_types=1);

namespace App\Security\Voter;

use App\Entity\Location\LocationCity;
use App\Entity\User;

class CityVoter extends AbstractCrudVoter
{
    public const string GET_COLLECTION = 'ROLE_CITY_READ';
    public const string POST = 'ROLE_CITY_CREATE';
    public const string GET = 'ROLE_CITY_READ';
    public const string PUT = 'ROLE_CITY_UPDATE';
    public const string DELETE = 'ROLE_CITY_DELETE';

    public function __construct()
    {
        parent::__construct(
            self::GET_COLLECTION,
            self::POST,
            self::GET,
            self::PUT,
            self::DELETE,
            LocationCity::class,
        );
    }

    protected function voteOnUser(string $attribute, mixed $subject, ?User $user): bool
    {
        return match ($attribute) {
            self::GET_COLLECTION,
            self::GET => true,
            default => true === $user?->isSuperAdmin(),
        };
    }
}

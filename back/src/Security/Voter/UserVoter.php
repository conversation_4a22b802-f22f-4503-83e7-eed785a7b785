<?php declare(strict_types=1);

namespace App\Security\Voter;

use App\Entity\User;

class UserVoter extends AbstractCrudVoter
{
    public const string GET_COLLECTION = 'ROLE_USER_READ';
    public const string POST = 'ROLE_USER_CREATE';
    public const string GET = 'ROLE_USER_READ';
    public const string PUT = 'ROLE_USER_UPDATE';
    public const string DELETE = 'ROLE_USER_DELETE';

    public function __construct()
    {
        parent::__construct(
            self::GET_COLLECTION,
            self::POST,
            self::GET,
            self::PUT,
            self::DELETE,
            User::class,
        );
    }

    protected function voteOnUser(string $attribute, mixed $subject, ?User $user): bool
    {
        return match ($attribute) {
            self::GET_COLLECTION,
            self::GET => true,
            self::PUT => (true === $user?->isSuperAdmin() || $subject === $user),
            default => true === $user?->isSuperAdmin(),
        };
    }
}

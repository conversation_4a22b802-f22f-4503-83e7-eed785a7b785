<?php declare(strict_types=1);

namespace App\Security\Voter;

use App\Business\Estimate\Entity\Estimate;
use App\Entity\User;
use App\Security\AgencyUserChecker;

class EstimateVoter extends AbstractCrudVoter
{
    public const string GET_COLLECTION = 'ROLE_ESTIMATE_READ_COLLECTION';
    public const string POST = 'ROLE_ESTIMATE_CREATE';
    public const string GET = 'ROLE_ESTIMATE_READ';
    public const string PUT = 'ROLE_ESTIMATE_UPDATE';
    public const string DELETE = 'ROLE_ESTIMATE_DELETE';

    public function __construct(
        private readonly AgencyUserChecker $agencyUserChecker,
    ) {
        parent::__construct(
            self::GET_COLLECTION,
            self::POST,
            self::GET,
            self::PUT,
            self::DELETE,
            Estimate::class,
        );
    }

    /** @param Estimate $subject */
    protected function voteOnUser(string $attribute, mixed $subject, ?User $user): bool
    {
        return true;
        return match ($attribute) {
            self::GET_COLLECTION => $this->agencyUserChecker->userIsGrantedOnAgency($user, $subject?->agency),
            self::GET,
            self::POST,
            self::PUT => true, // needed for random can do an estimation
            default => true === $user?->isSuperAdmin(),
        };
    }
}

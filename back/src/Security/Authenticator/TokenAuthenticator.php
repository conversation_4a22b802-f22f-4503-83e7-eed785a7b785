<?php declare(strict_types=1);

namespace App\Security\Authenticator;

use App\Entity\User;
use App\Security\Token\ApiTokens;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Http\Authenticator\AbstractAuthenticator;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Passport;
use Symfony\Component\Security\Http\Authenticator\Passport\SelfValidatingPassport;
use Symfony\Component\Security\Http\Authenticator\Token\PostAuthenticationToken;

final class TokenAuthenticator extends AbstractAuthenticator
{
    public const string TOKEN_QUERY_PARAMETER = 'token';
    public const string TOKEN_HEADER = 'X-Sinfin-Token';
    public const string TOKEN_RENEW_HEADER = 'X-Sinfin-Token-Renew';

    public function __construct(
        private readonly ApiTokens $tokens,
        private readonly EntityManagerInterface $em,
    ) {
    }

    public function supports(Request $request): ?bool
    {
        return null !== $this->getToken($request);
    }

    public function authenticate(Request $request): Passport
    {
        $token = $this->getToken($request);

        return new SelfValidatingPassport(new UserBadge($token, function (string $token): ?User {
            if (null !== $id = $this->tokens->decode($token)) {
                return $this->em->getRepository(User::class)->find($id);
            }

            return null;
        }));
    }

    public function createToken(Passport $passport, string $firewallName): TokenInterface
    {
        return new PostAuthenticationToken(
            $passport->getUser(),
            $firewallName,
            $passport->getUser()->getRoles(),
        );
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token, string $firewallName): ?Response
    {
        return null;
    }

    public function onAuthenticationFailure(Request $request, AuthenticationException $exception): ?Response
    {
        return new JsonResponse(['error' => 'Authentication failure.'], Response::HTTP_UNAUTHORIZED);
    }

    private function getToken(Request $request): ?string
    {
        if ($request->isMethod(Request::METHOD_GET)) {
            if (null !== $token = $request->query->get(self::TOKEN_QUERY_PARAMETER)) {
                return $token;
            }
        }

        return $request->headers->get(self::TOKEN_HEADER);
    }
}

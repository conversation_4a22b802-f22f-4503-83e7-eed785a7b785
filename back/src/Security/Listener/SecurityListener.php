<?php declare(strict_types=1);

namespace App\Security\Listener;

use App\Entity\User;
use App\Security\Security;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

final readonly class SecurityListener
{
    public function __construct(
        private TokenStorageInterface $storage,
        private Security $security,
    ) {
    }

    #[AsEventListener(priority: 7)]
    public function onRequest(RequestEvent $event): void
    {
        if ($event->isMainRequest()) {
            if (null !== $token = $this->storage->getToken()) {
                $user = $token->getUser();
                if ($user instanceof User) {
                    $this->security->user = $user;
                }
            }
        }
    }
}

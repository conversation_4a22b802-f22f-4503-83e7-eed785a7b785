<?php declare(strict_types=1);

namespace App\Security\Listener;

use App\Bridge\Mailer\Mailer;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\ORM\Event\PostPersistEventArgs;
use Doctrine\ORM\Events;
use Symfony\Component\HttpFoundation\RequestStack;

#[AsDoctrineListener(event: Events::postPersist)]
final readonly class UserListener
{
    public function __construct(
        private RequestStack $requestStack,
        private Mailer $mailer,
    ) {
    }

    public function postPersist(PostPersistEventArgs $args): void
    {
        if (!$args->getObject() instanceof User) {
            return;
        }

        if (null === $this->requestStack->getMainRequest()) {
            return;
        }

        $this->mailer->welcome($args->getObject());
    }
}

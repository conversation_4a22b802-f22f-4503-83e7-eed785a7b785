<?php declare(strict_types=1);

namespace App\Security;

use App\Doctrine\Enum\TableEnum;
use App\Entity\Agency\Agency;
use App\Entity\User;
use App\Security\Enum\RoleEnum;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\SecurityBundle\Security as SecurityBundle;
use Throwable;

/**
 * Global class to ease handling of user and agency management.
 */
final readonly class AgencyUserChecker
{
    public function __construct(
        private EntityManagerInterface $em,
        private LoggerInterface $logger,
        private SecurityBundle $security,
    ) {
    }

    /**
     * when RoleEnum::SUPER_ADMIN - user is always allowed to do the action.
     */
    public function userIsLinkedToAgency(?User $user, ?Agency $agency): bool
    {
        if (null === $user) {
            return false;
        }

        if (true === $this->security->isGranted(RoleEnum::SUPER_ADMIN, $user)) {
            return true;
        }

        if (null === $agency) {
            return true;
        }

        try {
            $tableAgencyUser = TableEnum::AGENCY_USER;

            $query = <<<SQL
            SELECT COUNT(*)
            FROM {$tableAgencyUser}
            WHERE user_uuid = ? AND agency_uuid = ?
            SQL;

            return 1 === $this->em->getConnection()->fetchOne($query, [$user->uuid->toBinary(), $agency->uuid->toBinary()]);
        } catch (Throwable $exception) {
            $this->logger->error('cannot check if user is linked to agency', [
                'exception' => $exception,
                'user' => $user,
                'agency' => $agency,
            ]);

            return false;
        }
    }

    /**
     * when RoleEnum::SUPER_ADMIN - user is always allowed to do the action.
     */
    public function userIsGrantedOnAgency(?User $user, ?Agency $agency): bool
    {
        if (false === $this->userIsLinkedToAgency($user, $agency)) {
            return false;
        }

        return $this->security->isGranted(RoleEnum::ADMIN, $user);
    }
}

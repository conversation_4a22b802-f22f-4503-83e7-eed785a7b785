<?php declare(strict_types=1);

namespace App\Security\Api\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Entity\User;
use App\Security\Security;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Uid\Uuid;

final readonly class UserProvider implements ProviderInterface
{
    public function __construct(
        private Security $security,
        private EntityManagerInterface $em,
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): ?User
    {
        $uuid = $uriVariables['uuid'] ?? 0;
        if ($uuid instanceof Uuid) {
            return $this->em->getRepository(User::class)->find($uuid);
        }
        if (0 === $uuid) {
            return $this->security->user;
        }

        return null;
    }
}

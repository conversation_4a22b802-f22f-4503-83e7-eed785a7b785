<?php declare(strict_types=1);

namespace App\Security\Api\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Security\Api\Resource\PasswordResetToken;
use App\Security\Token\ResetPasswordTokens;

final readonly class PasswordResetTokenProvider implements ProviderInterface
{
    public function __construct(
        private ResetPasswordTokens $resetPasswordTokens,
    ) {
    }

    public function provide(
        Operation $operation,
        array $uriVariables = [],
        array $context = [],
    ): ?PasswordResetToken {
        $token = $uriVariables['token'];

        if (null === $email = $this->resetPasswordTokens->getEmail($token)) {
            return null;
        }

        $resetToken = new PasswordResetToken();
        $resetToken->token = $token;
        $resetToken->email = $email;

        return $resetToken;
    }
}

<?php declare(strict_types=1);

namespace App\Security\Api\Resource;

use ApiPlatform\Metadata as APM;
use ApiPlatform\OpenApi\Model\Operation;
use App\Api\Enum\ApiTagEnum;
use App\Security\Api\Processor\PasswordUpdateProcessor;
use App\Utils\SecurityUtils;
use Symfony\Component\Security\Core\Validator\Constraints\UserPassword;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

#[APM\Post(openapi: new Operation(tags: [ApiTagEnum::SECURITY]), processor: PasswordUpdateProcessor::class)]
class PasswordUpdate
{
    #[APM\ApiProperty(writable: false, identifier: true)]
    public ?int $id = null;

    #[Assert\NotNull]
    #[UserPassword]
    public ?string $oldPassword = null;

    #[Assert\NotNull]
    public ?string $newPassword = null;

    #[Assert\Callback]
    public function assertIsValid(ExecutionContextInterface $context): void
    {
        if (null !== $this->newPassword) {
            foreach (SecurityUtils::getPasswordErrors($this->newPassword) as $message) {
                $context
                    ->buildViolation($message)
                    ->atPath('newPassword')
                    ->addViolation();
            }
        }
    }
}

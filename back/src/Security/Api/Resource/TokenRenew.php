<?php declare(strict_types=1);

namespace App\Security\Api\Resource;

use ApiPlatform\Metadata as APM;
use ApiPlatform\OpenApi\Model\Operation;
use App\Api\Enum\ApiTagEnum;
use App\Security\Api\Processor\TokenRenewProcessor;

#[APM\Post(openapi: new Operation(tags: [ApiTagEnum::SECURITY]), processor: TokenRenewProcessor::class)]
class TokenRenew
{
    #[APM\ApiProperty(writable: false, identifier: true)]
    public ?string $token = null;
}

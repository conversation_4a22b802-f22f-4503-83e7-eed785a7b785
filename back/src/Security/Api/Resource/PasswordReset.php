<?php declare(strict_types=1);

namespace App\Security\Api\Resource;

use ApiPlatform\Metadata as APM;
use ApiPlatform\OpenApi\Model\Operation;
use App\Api\Enum\ApiTagEnum;
use App\Security\Api\Processor\PasswordResetProcessor;
use App\Utils\SecurityUtils;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

#[APM\Post(openapi: new Operation(tags: [ApiTagEnum::SECURITY]), processor: PasswordResetProcessor::class)]
class PasswordReset
{
    #[APM\ApiProperty(writable: false, identifier: true)]
    public ?int $id = null;

    #[Assert\NotNull]
    public ?string $token = null;

    #[Assert\NotNull]
    public ?string $password = null;

    #[Assert\Callback]
    public function assertIsValid(ExecutionContextInterface $context): void
    {
        if (null !== $this->password) {
            foreach (SecurityUtils::getPasswordErrors($this->password) as $message) {
                $context
                    ->buildViolation($message)
                    ->atPath('password')
                    ->addViolation();
            }
        }
    }
}

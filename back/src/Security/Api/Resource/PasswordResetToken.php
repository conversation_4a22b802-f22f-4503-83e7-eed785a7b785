<?php declare(strict_types=1);

namespace App\Security\Api\Resource;

use ApiPlatform\Metadata as APM;
use ApiPlatform\OpenApi\Model\Operation;
use App\Api\Enum\ApiTagEnum;
use App\Security\Api\Provider\PasswordResetTokenProvider;

#[APM\Get(
    uriTemplate: 'password-reset-tokens/{token}.{_format}',
    uriVariables: ['token'],
    openapi: new Operation(tags: [ApiTagEnum::SECURITY]),
    provider: PasswordResetTokenProvider::class
)]
class PasswordResetToken
{
    #[APM\ApiProperty(writable: false, identifier: true)]
    public ?string $token = null;

    public ?string $email = null;
}

<?php declare(strict_types=1);

namespace App\Security\Api\Resource;

use ApiPlatform\Metadata as APM;
use ApiPlatform\OpenApi\Model\Operation;
use App\Api\Enum\ApiTagEnum;
use App\Security\Api\Controller\TokenAction;
use App\Security\Api\Model\TokenOutput;
use Symfony\Component\Validator\Constraints as Assert;

#[APM\Post(controller: TokenAction::class, openapi: new Operation(tags: [ApiTagEnum::SECURITY]), output: TokenOutput::class, name: self::TOKEN_OPERATION)]
final class Token
{
    public const string TOKEN_OPERATION = 'api_token_create';

    #[APM\ApiProperty(identifier: true)]
    #[Assert\NotBlank]
    public ?string $email = null;

    #[Assert\NotBlank]
    public ?string $password = null;
}

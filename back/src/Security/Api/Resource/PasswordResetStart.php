<?php declare(strict_types=1);

namespace App\Security\Api\Resource;

use ApiPlatform\Metadata as APM;
use ApiPlatform\OpenApi\Model\Operation;
use App\Api\Enum\ApiTagEnum;
use App\Security\Api\Processor\PasswordResetStartProcessor;
use Symfony\Component\Validator\Constraints as Assert;

#[APM\Post(openapi: new Operation(tags: [ApiTagEnum::SECURITY]), processor: PasswordResetStartProcessor::class)]
class PasswordResetStart
{
    #[APM\ApiProperty(writable: false, identifier: true)]
    public ?int $id = null;

    #[Assert\NotBlank]
    #[Assert\Email]
    public ?string $email = null;
}

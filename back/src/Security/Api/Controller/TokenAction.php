<?php declare(strict_types=1);

namespace App\Security\Api\Controller;

use App\Entity\User;
use App\Security\Api\Model\TokenOutput;
use App\Security\Token\ApiTokens;
use DateInterval;
use DateTime;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\HttpKernel\Exception\ServiceUnavailableHttpException;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;
use Symfony\Component\Security\Http\Attribute\CurrentUser;

#[AsController]
final readonly class TokenAction
{
    public function __construct(
        private ApiTokens $tokens,
    ) {
    }

    public function __invoke(
        #[CurrentUser]
        ?User $user,
    ): TokenOutput {
        if (null === $user) {
            throw new UnauthorizedHttpException('User not found.');
        }

        if (false === $diff = DateInterval::createFromDateString("+ {$this->tokens->duration} seconds")) {
            throw new ServiceUnavailableHttpException('Token generation error.');
        }

        return new TokenOutput(
            $user->email,
            $this->tokens->generate($user->uuid),
            (new DateTime())->add($diff),
            $this->tokens->duration,
        );
    }
}

<?php declare(strict_types=1);

namespace App\Security\Api\Processor;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\Bridge\Mailer\Mailer;
use App\Entity\User;
use App\Security\Api\Resource\PasswordResetStart;
use DateInterval;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;

final readonly class PasswordResetStartProcessor implements ProcessorInterface
{
    public function __construct(
        private EntityManagerInterface $em,
        private Mailer $mailer,
    ) {
    }

    /**
     * @phpstan-param PasswordResetStart $data
     */
    public function process(
        mixed $data,
        Operation $operation,
        array $uriVariables = [],
        array $context = [],
    ): PasswordResetStart {
        if (null !== $user = $this->em->getRepository(User::class)->findOneBy(['email' => $data->email])) {
            $this->mailer->sendPasswordResetStart($user);
        }

        $data->id = time();

        return $data;
    }
}

<?php declare(strict_types=1);

namespace App\Security\Api\Processor;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\Bridge\Mailer\Mailer;
use App\Entity\User;
use App\Security\Api\Resource\PasswordReset;
use App\Security\Exception\InvalidTokenException;
use App\Security\Token\ResetPasswordTokens;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

final readonly class PasswordResetProcessor implements ProcessorInterface
{
    public function __construct(
        private ResetPasswordTokens $resetPasswordTokens,
        private EntityManagerInterface $em,
        private UserPasswordHasherInterface $userPasswordHasher,
        private Mailer $mailer,
        private LoggerInterface $logger,
    ) {
    }

    /**
     * @phpstan-param PasswordReset $data
     * @throws InvalidTokenException
     */
    public function process(
        mixed $data,
        Operation $operation,
        array $uriVariables = [],
        array $context = [],
    ): PasswordReset {
        if (null === $email = $this->resetPasswordTokens->getEmail($data->token)) {
            $this->logger->error('an invalid password reset token was used', [
                'token' => $data->token,
                'password' => $data->password,
            ]);

            throw new InvalidTokenException();
        }

        /** @var User|null $user */
        $user = $this->em->getRepository(User::class)->findOneBy(['email' => $email]);
        if (null === $user) {
            $this->logger->error('a reset token with an invalid email was used', [
                'email' => $email,
                'token' => $data->token,
                'password' => $data->password,
            ]);

            throw new InvalidTokenException();
        }

        $user->password = $this->userPasswordHasher->hashPassword($user, $data->password);
        $this->em->flush();

        $this->mailer->sendPasswordUpdate($user);

        $data->id = time();
        $data->password = null;

        return $data;
    }
}

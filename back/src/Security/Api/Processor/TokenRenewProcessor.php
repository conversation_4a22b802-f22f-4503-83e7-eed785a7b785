<?php declare(strict_types=1);

namespace App\Security\Api\Processor;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\Security\Security;
use App\Security\Token\ApiTokens;

final readonly class TokenRenewProcessor implements ProcessorInterface
{
    public function __construct(
        private Security $security,
        private ApiTokens $tokens,
    ) {
    }

    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = [])
    {
        $user = $this->security->user;

        $data->token = $this->tokens->generate($user->uuid);

        return $data;
    }
}

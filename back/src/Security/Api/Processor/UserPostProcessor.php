<?php declare(strict_types=1);

namespace App\Security\Api\Processor;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use <PERSON>ymfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

/**
 * @template-implements ProcessorInterface<User, User>
 */
final readonly class UserPostProcessor implements ProcessorInterface
{
    public function __construct(
        private EntityManagerInterface $em,
        private UserPasswordHasherInterface $passwordHasher,
    ) {
    }

    /** @param User $data */
    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = []): User
    {
        // fake a password to please DB
        $data->password = $this->passwordHasher->hashPassword($data, uniqid() . uniqid());

        $this->em->persist($data);
        $this->em->flush();

        return $data;
    }
}

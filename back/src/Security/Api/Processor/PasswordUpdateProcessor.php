<?php declare(strict_types=1);

namespace App\Security\Api\Processor;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\Bridge\Mailer\Mailer;
use App\Security\Api\Resource\PasswordUpdate;
use App\Security\Exception\InvalidTokenException;
use App\Security\Security;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

final readonly class PasswordUpdateProcessor implements ProcessorInterface
{
    public function __construct(
        private Security $security,
        private UserPasswordHasherInterface $userPasswordHasher,
        private EntityManagerInterface $em,
        private Mailer $mailer,
    ) {
    }

    /**
     * @phpstan-param PasswordUpdate $data
     * @throws InvalidTokenException
     */
    public function process(
        mixed $data,
        Operation $operation,
        array $uriVariables = [],
        array $context = [],
    ): PasswordUpdate {
        $user = $this->security->user;

        $user->password = $this->userPasswordHasher->hashPassword($user, $data->newPassword);
        $this->em->flush();

        $this->mailer->sendPasswordUpdate($user);

        $data->id = time();
        $data->oldPassword = null;
        $data->newPassword = null;

        return $data;
    }
}

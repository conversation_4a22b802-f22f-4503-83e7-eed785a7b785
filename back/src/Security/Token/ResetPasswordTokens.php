<?php declare(strict_types=1);

namespace App\Security\Token;

use App\Security\Signer\ArraySigner;
use DateTime;
use function count;
use function is_array;

final readonly class ResetPasswordTokens
{
    public function __construct(
        private ArraySigner $signer,
    ) {
    }

    public function generate(string $email, DateTime $limit): string
    {
        return $this->signer->encode([
            $email,
            $limit->format('YmdHis'),
        ]);
    }

    public function getEmail(string $token): ?string
    {
        $data = $this->signer->decode($token);
        if (is_array($data) && 2 === count($data)) {
            [$email, $limitDate] = $data;
            $now = (new DateTime())->format('YmdHis');
            if ((int)$now > (int)$limitDate) {
                return null;
            }

            return $email;
        }

        return null;
    }
}

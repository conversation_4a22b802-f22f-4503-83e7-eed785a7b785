<?php declare(strict_types=1);

namespace App\Security\Token;

use App\Security\Authenticator\TokenAuthenticator;
use App\Security\Signer\ArraySigner;
use Exception;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\Uid\Uuid;
use Throwable;
use function count;
use function is_int;

final class ApiTokens
{
    private array $needsRenewal = [];

    public function __construct(
        private readonly ArraySigner $signer,
        private readonly LoggerInterface $logger,
        public readonly int $duration = 86400,
    ) {
    }

    public function generate(Uuid $uuid, ?int $duration = null): string
    {
        return $this->signer->encode([
            time(),
            $duration ?? $this->duration,
            $uuid->toRfc4122(),
        ]);
    }

    public function decode(string $token): ?Uuid
    {
        try {
            $stop = static function (string $message): never {
                throw new Exception($message);
            };
            if (null === $parts = $this->signer->decode($token)) {
                $stop('token cannot be decoded');
            }
            if (!array_is_list($parts)) {
                $stop('token is not a list');
            }
            if (3 !== count($parts)) {
                $stop('token is not formed of 4 parts');
            }
            [$time, $duration, $uuid] = $parts;
            if (!is_int($time)) {
                $stop('invalid time');
            }
            if (!is_int($duration)) {
                $stop('invalid duration');
            }
            if (null === $uuid || !Uuid::isValid($uuid)) {
                $stop('invalid uuid');
            }

            // avoid expired tokens
            $limit = $time + $duration;
            $expiresIn = $limit - time();
            if (60 * 10 > $expiresIn) {
                $this->needsRenewal[] = $parts;
            }
            if (0 > $expiresIn) {
                $stop('token is expired');
            }

            return Uuid::fromRfc4122($uuid);
        } catch (Throwable $e) {
            $this->logger->debug($e->getMessage());
        }

        return null;
    }

    #[AsEventListener]
    public function onResponse(ResponseEvent $event): void
    {
        foreach ($this->needsRenewal as $parts) {
            $event->getResponse()->headers->set(
                TokenAuthenticator::TOKEN_RENEW_HEADER,
                $this->signer->encode($parts),
            );
        }
    }
}

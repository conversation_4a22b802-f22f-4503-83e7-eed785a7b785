<?php declare(strict_types=1);

namespace App\Security;

use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Core\Exception\UnsupportedUserException;
use Symfony\Component\Security\Core\Exception\UserNotFoundException;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\PasswordUpgraderInterface;
use Symfony\Component\Security\Core\User\UserCheckerInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Security\Core\User\UserProviderInterface;

final readonly class UserLoader implements UserProviderInterface, UserCheckerInterface, PasswordUpgraderInterface
{
    public function __construct(
        private EntityManagerInterface $em,
    ) {
    }

    public function supportsClass(string $class): bool
    {
        return User::class === $class;
    }

    public function refreshUser(UserInterface $user): User
    {
        if (!$user instanceof User) {
            throw new UnsupportedUserException();
        }

        return $this->loadUserByIdentifier($user->getUserIdentifier());
    }

    public function loadUserByIdentifier(string $identifier): User
    {
        $user = $this->em->getRepository(User::class)->findOneBy(['email' => $identifier]);
        if ($user instanceof User) {
            return $user;
        }

        $e = new UserNotFoundException();
        $e->setUserIdentifier($identifier);
        throw $e;
    }

    public function checkPreAuth(UserInterface $user): void
    {
        // @todo inactive user ?
    }

    public function checkPostAuth(UserInterface $user): void
    {
    }

    public function upgradePassword(PasswordAuthenticatedUserInterface $user, string $newHashedPassword): void
    {
        if ($user instanceof User) {
            $user->password = $newHashedPassword;
            $this->em->persist($user);
            $this->em->flush();
        }
    }
}

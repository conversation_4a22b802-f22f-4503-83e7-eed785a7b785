<?php

declare(strict_types=1);

namespace App\Security\Command;

use App\Bridge\Front\Front;
use App\Entity\User;
use App\Security\Enum\RoleEnum;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Validator\Constraints\Email;
use Symfony\Component\Validator\Constraints\NotNull;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use function count;
use function uniqid;

#[AsCommand(name: 'app:security:create-user')]
class CreateUserCommand extends Command
{
    public function __construct(
        private readonly EntityManagerInterface $em,
        private readonly ValidatorInterface $validator,
        private readonly UserPasswordHasherInterface $passwordHasher,
        private readonly Front $front,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('email', InputArgument::REQUIRED)
            ->addOption('super-admin', null, InputOption::VALUE_NONE, 'Tag user as ' . RoleEnum::SUPER_ADMIN . ', ' . RoleEnum::ADMIN . 'otherwise');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $violations = $this->validator->validate($email = $input->getArgument('email'), [new NotNull(), new Email()]);

        if (0 !== count($violations)) {
            $output->writeln('<error>email is not valid</error>');

            return self::FAILURE;
        }

        if ($this->em->getRepository(User::class)->findOneBy(['email' => $email])) {
            $output->writeln('<error>User already exists</error>');

            return self::FAILURE;
        }

        $user = new User();
        $user->firstname = uniqid();
        $user->lastname = uniqid();
        $user->email = $email;
        $user->role = $input->getOption('super-admin') ? RoleEnum::SUPER_ADMIN : RoleEnum::ADMIN;

        // password can't be null in DB so we fake it
        $user->password = $this->passwordHasher->hashPassword($user, uniqid() . uniqid());

        $this->em->persist($user);
        $this->em->flush();

        $output->writeln([
            '<info>User created</info>',
            'Init your password: ' . $this->front->getResetPasswordUrl($user->email, new DateTime('+12 hours'), true),
        ]);

        return self::SUCCESS;
    }
}

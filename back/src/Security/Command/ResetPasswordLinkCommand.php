<?php declare(strict_types=1);

namespace App\Security\Command;

use App\Bridge\Front\Front;
use App\Entity\User;
use DateInterval;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use function is_int;

#[AsCommand('app:security:reset-password-link', 'Get a reset password link for users')]
class ResetPasswordLinkCommand extends Command
{
    public function __construct(
        private readonly EntityManagerInterface $em,
        private readonly Front $front,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('emails', InputArgument::IS_ARRAY, 'email of user to reset')
            ->addOption('duration', null, InputOption::VALUE_OPTIONAL, 'Duration validity for link (in hours)', 12);
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if (
            !is_int($input->getOption('duration'))
            && !ctype_digit($input->getOption('duration'))
        ) {
            throw new Exception('Duration must be a number');
        }

        $emails = $input->getArgument('emails');

        $users = $this->em->getRepository(User::class)->findBy(['email' => $emails]);

        $duration = (int)$input->getOption('duration');
        $limit = new DateTime();
        /* @phpstan-ignore-next-line */
        $limit->add(DateInterval::createFromDateString("+ {$duration} hours"));

        $date = $limit->format('Y-m-d H:i');
        $output->writeln("Link will be valid to: {$date}");

        $table = new Table($output);
        $table->setHeaders(['Email', 'Link']);
        foreach ($users as $user) {
            $url = $this->front->getResetPasswordUrl($user->email, $limit);

            $table->addRow([$user->email, $url]);
        }

        $table->render();

        return self::SUCCESS;
    }
}

<?php declare(strict_types=1);

namespace App\Security\Serializer;

use App\Entity\User;
use App\Security\Security;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

class UserSerializer implements NormalizerInterface, NormalizerAwareInterface, DenormalizerInterface, DenormalizerAwareInterface
{
    private const string ALREADY_CALLED = '4a87cde4c448136e99dfcc42cd204312';
    private const string DENORMALIZE_ALREADY_CALLED = 'b9f2e8d7a1c3456f88debb32fa409871';

    use NormalizerAwareTrait;
    use DenormalizerAwareTrait;

    public function __construct(
        private readonly Security $security,
    ) {
    }

    /**
     * @param User $object
     */
    public function normalize(mixed $object, ?string $format = null, array $context = []): array
    {
        $context[self::ALREADY_CALLED] = true;
        $data = (array)$this->normalizer->normalize($object, $format, $context);

        $data['@id'] = '/api/users/' . $object->uuid->toRfc4122();

        return $data;
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof User && 'jsonld' === $format && !isset($context[self::ALREADY_CALLED]);
    }

    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = [])
    {
        $context[self::DENORMALIZE_ALREADY_CALLED] = true;

        if (!($this->security->user?->isSuperAdmin() ?? false)) {
            $context[AbstractNormalizer::IGNORED_ATTRIBUTES] = ['role'];
        }

        return $this->denormalizer->denormalize($data, $type, $format, $context);
    }

    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []): bool
    {
        return User::class === $type && 'jsonld' === $format && !isset($context[self::DENORMALIZE_ALREADY_CALLED]);
    }
}

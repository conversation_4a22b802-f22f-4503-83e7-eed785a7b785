<?php declare(strict_types=1);

namespace App\Security\Signer;

use App\Utils\JsonUtils;

final readonly class ArraySigner
{
    public function __construct(
        private StringSigner $signer,
    ) {
    }

    public function encode(array $array): string
    {
        return $this->signer->encode(JsonUtils::encode($array));
    }

    public function decode(string $encoded): ?array
    {
        if (null !== $json = $this->signer->decode($encoded)) {
            return JsonUtils::decode($json);
        }

        return null;
    }
}

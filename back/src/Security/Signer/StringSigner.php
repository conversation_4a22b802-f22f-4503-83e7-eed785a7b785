<?php declare(strict_types=1);

namespace App\Security\Signer;

use App\Utils\JsonUtils;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

final readonly class StringSigner
{
    public function __construct(
        #[Autowire(param: 'kernel.secret')]
        private string $secret,
    ) {
    }

    public function encode(string $string): string
    {
        return base64_encode(JsonUtils::encode([
            $string,
            $this->sign($string),
        ]));
    }

    public function decode(string $encoded): ?string
    {
        if (
            (false !== $decoded = base64_decode($encoded, true))
            && (null !== $data = JsonUtils::decode($decoded))
        ) {
            [$string, $sign] = $data;
            if ($this->sign($string) === $sign) {
                return $string;
            }
        }

        return null;
    }

    private function sign(string $string): string
    {
        return md5($this->secret . '/' . $string);
    }
}

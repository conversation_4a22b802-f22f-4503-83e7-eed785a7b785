<?php declare(strict_types=1);

namespace App\Security\Enum;

abstract class DrivingLicenceEnum
{
    public const string DRIVING_LICENCE_A = 'DRIVING_LICENCE_A';
    public const string DRIVING_LICENCE_B = 'DRIVING_LICENCE_B';
    public const string DRIVING_LICENCE_C = 'DRIVING_LICENCE_C';
    public const string DRIVING_LICENCE_D = 'DRIVING_LICENCE_D';
    public const array ALL = [
        self::DRIVING_LICENCE_A,
        self::DRIVING_LICENCE_B,
        self::DRIVING_LICENCE_C,
        self::DRIVING_LICENCE_D,
    ];
}

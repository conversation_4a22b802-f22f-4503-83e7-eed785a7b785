<?php declare(strict_types=1);

namespace App\Security\Enum;

abstract class RoleEnum
{
    public const string SUPER_ADMIN = 'ROLE_SUPER_ADMIN'; // "Global scope"
    public const string ADMIN = 'ROLE_ADMIN'; // "Agency scope"
    public const string USER = 'ROLE_USER'; // "Agency scope"
    public const string PUBLIC_ACCESS = 'PUBLIC_ACCESS';
    public const array ALL = [
        self::SUPER_ADMIN,
        self::ADMIN,
        self::USER,
    ];
}

<?php declare(strict_types=1);

namespace App\Security\Enum;

abstract class CivilityEnum
{
    public const string MRS = 'MRS';
    public const string MISTER = 'MISTER';
    public const string MISS = 'MISS';
    public const array ALL = [
        self::MRS,
        self::MISTER,
        self::MISS,
    ];

    public static function translate(?string $value): string
    {
        return match ($value) {
            self::MRS => 'Madame',
            self::MISTER => 'Monsieur',
            self::MISS => 'Mademoiselle',
            default => null,
        };
    }
}

<?php

declare(strict_types=1);

namespace App\Command;

use App\Business\Import\AgencyImporter;
use App\Business\Import\CabinetUserImporter;
use App\Business\Import\CantonImporter;
use App\Business\Import\CityImporter;
use App\Business\Import\ImportStore;
use App\Business\Import\UserImporter;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Agency\Agency;
use App\Entity\Location\LocationDepartment;
use App\Utils\CsvUtils;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

#[AsCommand(name: 'app:import:metadesc')]
class ImportLegacyMetaDescCommand extends Command
{
    private const string FILENAME = 'sinfin_cabinets.csv';
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        #[Autowire('%legacy_data_dir%')]
        private readonly string $legacyDataDir,
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if (!is_file($cabinetPath = $this->getPath())) {
            throw new Exception('sinfin_cabinets.csv not found');
        }

        foreach (CsvUtils::mapAssociative($cabinetPath) as $cab) {
            $agency = $this->entityManager->getRepository(Agency::class)->findOneBy(['legacyId' => $cab['id']]);

            if (!$agency) {
                $output->writeln(sprintf('<error>%s</error>', $cab['id']));
                continue;
            }

            $agency->meta->description = mb_strcut($cab['text_seo_desc'], 0, 255);
            $this->entityManager->persist($agency);
        }

        $this->entityManager->flush();

        return self::SUCCESS;
    }

    private function getPath(): string
    {
        return rtrim($this->legacyDataDir, '/') . '/' . self::FILENAME;
    }
}

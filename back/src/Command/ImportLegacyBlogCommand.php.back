<?php

declare(strict_types=1);

namespace App\Command;

use App\Business\Import\BlogImporter;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'app:import:legacy-blog')]
class ImportLegacyBlogCommand extends Command
{
    public function __construct(
        private readonly BlogImporter $blogImporter,
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<error>Blocked for security</error>');

        return self::FAILURE;

        $this->blogImporter->import();

        return self::SUCCESS;
    }
}

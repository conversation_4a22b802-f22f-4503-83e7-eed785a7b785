<?php

declare(strict_types=1);

namespace App\Command;

use App\Business\Import\ProEstimateImporter;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'app:import:legacy-pro-estimate')]
class ImportLegacyProEstimateCommand extends Command
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly ProEstimateImporter $proEstimateImporter,
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->proEstimateImporter->import();

        return self::SUCCESS;
    }
}

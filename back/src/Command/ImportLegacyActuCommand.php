<?php

declare(strict_types=1);

namespace App\Command;

use App\Business\Import\ActuImporter;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'app:import:legacy-actu')]
class ImportLegacyActuCommand extends Command
{
    public function __construct(
        private readonly ActuImporter $actuImporter,
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->actuImporter->import();

        return self::SUCCESS;
    }
}

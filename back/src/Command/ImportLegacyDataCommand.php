<?php

declare(strict_types=1);

namespace App\Command;

use App\Business\Import\AgencyImporter;
use App\Business\Import\CabinetUserImporter;
use App\Business\Import\CantonImporter;
use App\Business\Import\CityImporter;
use App\Business\Import\ImportStore;
use App\Business\Import\UserImporter;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Location\LocationDepartment;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'app:import:legacy-data')]
class ImportLegacyDataCommand extends Command
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly AgencyImporter $agencyImporter,
        private readonly CantonImporter $cantonImporter,
        private readonly CabinetUserImporter $cabinetUserImporter,
        private readonly CityImporter $cityImporter,
        private readonly UserImporter $userImporter,
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->deleteAgencyAndAreaAndCityAndUser();

        $store = new ImportStore();
        $this->getDepartment($store);

        $this->agencyImporter->import($store);
        $output->writeln('<info>Agency finished.</info>');

        $this->userImporter->import($store);
        $output->writeln('<info>User finished.</info>');

        $this->cantonImporter->import($store);
        $output->writeln('<info>Canton finished.</info>');

        $this->cityImporter->import($store);
        $output->writeln('<info>City finished.</info>');

        $this->cabinetUserImporter->import($store);
        $output->writeln('<info>CabUser finished.</info>');

        $output->writeln('<info>Flushing...</info>');
        $this->entityManager->flush();

        $output->writeln('<info>Done.</info>');

        return self::SUCCESS;
    }

    private function getDepartment(ImportStore $store): void
    {
        foreach ($this->entityManager->getRepository(LocationDepartment::class)->findAll() as $department) {
            $store->department[(int)$department->code] = $department;
        }
    }

    private function deleteAgencyAndAreaAndCityAndUser(): void
    {
        $tableAgency = TableEnum::AGENCY;
        $tableArea = TableEnum::LOCATION_AREA;
        $tableCity = TableEnum::LOCATION_CITY;
        $tableUser = TableEnum::USER;
        $query = <<<SQL
        DELETE FROM {$tableAgency};
        DELETE FROM {$tableArea};
        DELETE FROM {$tableCity};
        DELETE FROM {$tableUser} where email NOT IN ("<EMAIL>", "<EMAIL>");
        SQL;

        $this->entityManager->getConnection()->executeStatement($query);
    }
}

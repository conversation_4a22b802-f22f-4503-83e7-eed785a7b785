<?php

declare(strict_types=1);

namespace App\Command;

use App\Bridge\Lyra\Entity\LyraOrder;
use App\Business\Estimate\Entity\Estimate;
use App\Utils\CsvUtils;
use Doctrine\ORM\EntityManagerInterface;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Uid\Uuid;

#[AsCommand(name: 'app:import:lyra-order')]
class ImportLegacyLyraOrderCommand extends Command
{
    private const string FILENAME = 'sinfin_lyra_order.csv';
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        #[Autowire('%legacy_data_dir%')]
        private readonly string $legacyDataDir,
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if (!is_file($lyraPath = $this->getPath())) {
            throw new \Exception('lyra file not found');
        }

        foreach (CsvUtils::mapAssociative($lyraPath) as $lyra) {
            $estimate = $this->entityManager->getRepository(Estimate::class)->findOneBy(['legacyId' => $lyra['devis_id']]);
            if (null === $estimate) {
                dump($lyra);
                continue;
            }
            $l = new LyraOrder();
            $l->uuid = Uuid::fromRfc4122($lyra['uuid']);
            $l->status = $lyra['status'];
            $l->amount = floatval($lyra['amount']);
            $l->isMultiple = (bool)$lyra['isMultiple'];
            $l->isSeenByAgendaPro = (bool)$lyra['getByAgendaPro'];
            $l->estimate = $estimate;
            $l->createdAt = Date::dateTimeFromTimestamp($lyra['created_at']);
            $l->updatedAt = Date::dateTimeFromTimestamp($lyra['updated_at']);
            $l->id = (int)$lyra['id'];
            $this->entityManager->persist($l);
        }

        $this->entityManager->flush();

        return self::SUCCESS;
    }

    private function getPath(): string
    {
        return rtrim($this->legacyDataDir, '/') . '/' . self::FILENAME;
    }
}

<?php

declare(strict_types=1);

namespace App\Command;

use App\Business\Files;
use App\Entity\Upload;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'app:upload:calculate-size', )]
class CalculateUploadSizeCommand extends Command
{
    public function __construct(
        private readonly EntityManagerInterface $em,
        private readonly Files $files,
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $uploads = $this->em->createQueryBuilder()
            ->select('u')
            ->from(Upload::class, 'u')
            ->where('u.width IS NULL OR u.height IS NULL')
            ->getQuery()
            ->toIterable();

        foreach ($uploads as $upload) {
            ['width' => $width, 'height' => $height] = $this->files->calculateWidth($this->files->getPath($upload));
            $upload->width = $width;
            $upload->height = $height;
        }

        $this->em->flush();

        return Command::SUCCESS;
    }
}

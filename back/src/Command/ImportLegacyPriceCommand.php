<?php

declare(strict_types=1);

namespace App\Command;

use App\Business\Import\PriceImporter;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'app:import:legacy-price')]
class ImportLegacyPriceCommand extends Command
{
    public function __construct(
        private readonly PriceImporter $priceImporter,
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->priceImporter->import();

        return self::SUCCESS;
    }
}

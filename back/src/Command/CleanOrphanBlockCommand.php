<?php

declare(strict_types=1);

namespace App\Command;

use App\Entity\Content\ContentBlock;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'app:content:clean-orphan-blocks', description: 'Clean all orphan blocks (i.e. not linked to page + not "favorite")')]
class CleanOrphanBlockCommand extends Command
{
    public function __construct(
        private readonly EntityManagerInterface $em,
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<info>Cleaning orphan blocks...</info>');

        $cleaned = $this->em->getRepository(ContentBlock::class)->cleanOrphans();

        $output->writeln("<info>{$cleaned} blocks cleaned</info>");

        return Command::SUCCESS;
    }
}

<?php

declare(strict_types=1);

namespace App\Command;

use App\Business\Import\EstimateImporter;
use App\Business\Import\PriceImporter;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'app:import:legacy-estimate')]
class ImportLegacyEstimateCommand extends Command
{
    public function __construct(
        private readonly EstimateImporter $estimateImporter,
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->estimateImporter->import();

        return self::SUCCESS;
    }
}

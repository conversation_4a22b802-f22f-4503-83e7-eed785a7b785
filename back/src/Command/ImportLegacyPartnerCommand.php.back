<?php

declare(strict_types=1);

namespace App\Command;

use App\Business\Import\PartnerImporter;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'app:import:legacy-partner')]
class ImportLegacyPartnerCommand extends Command
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly PartnerImporter $partnerImporter,
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<error>Blocked for now</error>');

        return self::FAILURE;

        $this->partnerImporter->import();

        return self::SUCCESS;
    }
}

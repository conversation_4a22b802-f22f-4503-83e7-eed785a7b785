<?php declare(strict_types=1);

namespace App\Business\Estimate\Enum;

// coming from old prod
abstract class ActivityTypeEnum
{
    public const string PACK = 'PACK'; // special case to handle X activity
    public const string TRANS_AMIANTE = 'TRANS_AMIANTE';
    public const string TRANS_ELECTIRICITE = 'TRANS_ELECTIRICITE';
    public const string TRANS_ERP = 'TRANS_ERP';
    public const string LOC_ERP = 'LOC_ERP';
    public const string LOC_ETAT_LIEUX = 'LOC_ETAT_LIEUX';
    public const string LOC_ETAT_LIEUX_SORTIE = 'LOC_ETAT_LIEUX_SORTIE';
    public const string TRANS_GAZ = 'TRANS_GAZ';
    public const string LOC_DECENT = 'LOC_DECENT';
    public const string TRANS_CARREZ = 'TRANS_CARREZ';
    public const string LOC_SURFACE = 'LOC_SURFACE';
    public const string TRANS_PERF_ENERGETIQUE = 'TRANS_PERF_ENERGETIQUE';
    public const string LOC_PERF_ENERGETIQUE = 'LOC_PERF_ENERGETIQUE';
    public const string TRANS_PLOMB = 'TRANS_PLOMB';
    public const string LOC_PLOMB = 'LOC_PLOMB';
    public const string TRANS_TERMITES = 'TRANS_TERMITES';
    public const string LOC_ELECTRICITE = 'LOC_ELECTRICITE';
    public const string LOC_AMIANTE = 'LOC_AMIANTE';
    public const string LOC_GAZ = 'LOC_GAZ';
    public const string TRANS_ASSAINISSEMENT = 'TRANS_ASSAINISSEMENT';
    public const string LOC_ASSAINISSEMENT = 'LOC_ASSAINISSEMENT';
    public const string AUDIT = 'AUDIT';

    public const array ALL = [
        self::PACK,
        self::TRANS_AMIANTE,
        self::TRANS_ELECTIRICITE,
        self::TRANS_ERP,
        self::LOC_ERP,
        self::LOC_GAZ,
        self::TRANS_GAZ,
        self::LOC_DECENT,
        self::TRANS_CARREZ,
        self::LOC_PERF_ENERGETIQUE,
        self::LOC_PERF_ENERGETIQUE,
        self::TRANS_PLOMB,
        self::LOC_PLOMB,
        self::TRANS_TERMITES,
        self::LOC_ELECTRICITE,
        self::LOC_DECENT,
        self::TRANS_CARREZ,
        self::LOC_PERF_ENERGETIQUE,
        self::LOC_PERF_ENERGETIQUE,
        self::TRANS_PLOMB,
        self::LOC_PLOMB,
        self::TRANS_TERMITES,
        self::LOC_ELECTRICITE,
        self::LOC_DECENT,
        self::LOC_ETAT_LIEUX,
        self::LOC_ETAT_LIEUX_SORTIE,
        self::LOC_SURFACE,
        self::TRANS_PERF_ENERGETIQUE,
        self::LOC_AMIANTE,
        self::TRANS_ASSAINISSEMENT,
        self::LOC_ASSAINISSEMENT,
        self::AUDIT,
    ];

    public static function fromLegacy(string $id): ?string
    {
        return match ($id) {
            '1' => self::TRANS_AMIANTE,
            '2' => self::TRANS_ELECTIRICITE,
            '3' => self::TRANS_ERP,
            '4' => self::LOC_ERP,
            '5' => self::LOC_ETAT_LIEUX,
            '6' => self::LOC_ETAT_LIEUX_SORTIE,
            '7' => self::TRANS_GAZ,
            '8' => self::LOC_DECENT,
            '9' => self::TRANS_CARREZ,
            '10' => self::LOC_SURFACE,
            '11' => self::TRANS_PERF_ENERGETIQUE,
            '12' => self::LOC_PERF_ENERGETIQUE,
            '15' => self::TRANS_PLOMB,
            '16' => self::LOC_PLOMB,
            '17' => self::TRANS_TERMITES,
            '18' => self::LOC_ELECTRICITE,
            '19' => self::LOC_AMIANTE,
            '20' => self::LOC_GAZ,
            '21' => self::TRANS_ASSAINISSEMENT,
            '22' => self::LOC_ASSAINISSEMENT,
            '23' => self::AUDIT,
            default => null,
        };
    }
}

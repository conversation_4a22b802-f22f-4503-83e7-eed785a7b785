<?php declare(strict_types=1);

namespace App\Business\Estimate\Enum;

abstract class PropertyTypeEnum
{
    public const string APARTMENT = 'apartment';
    public const string CO_OWNED_HOUSE = 'co_owned_house';
    public const string DETACHED_HOUSE = 'detached_house';
    public const string OTHER = 'other';

    public const array ALL = [
        self::APARTMENT,
        self::CO_OWNED_HOUSE,
        self::DETACHED_HOUSE,
        self::OTHER,
    ];
}

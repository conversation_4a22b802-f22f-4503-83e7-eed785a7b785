<?php declare(strict_types=1);

namespace App\Business\Estimate\Enum;

abstract class BuildYearEnum
{
    public const string PRE_1949 = 'pre_1949';
    public const string BETWEEN_1949_1997 = 'between_1949_1997';
    public const string PAST_1997 = 'past_1997';

    // new one
    public const string PAST_2000 = 'past_2000';
    public const string BETWEEN_1949_1974 = 'between_1949_1974';
    public const string BETWEEN_1975_1977 = 'between_1975_1977';
    public const string BETWEEN_1978_1982 = 'between_1978_1982';
    public const string BETWEEN_1983_1988 = 'between_1983_1988';
    public const string BETWEEN_1987_1997 = 'between_1987_1997';
    public const string BETWEEN_1998_2000 = 'between_1998_2000';

    public const array ALL = [
        self::PRE_1949,
        self::BETWEEN_1949_1974,
        self::BETWEEN_1975_1977,
        self::BETWEEN_1978_1982,
        self::BETWEEN_1983_1988,
        self::BETWEEN_1987_1997,
        self::BETWEEN_1998_2000,
        self::BETWEEN_1949_1997,
        self::PAST_1997,
        self::PAST_2000,
    ];
}

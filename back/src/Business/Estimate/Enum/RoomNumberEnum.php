<?php declare(strict_types=1);

namespace App\Business\Estimate\Enum;

abstract class RoomNumberEnum
{
    public const int ROOM_1 = 1;
    public const int ROOM_2 = 2;
    public const int ROOM_3 = 3;
    public const int ROOM_4 = 4;
    public const int ROOM_5 = 5;
    public const int ROOM_6 = 6;
    public const int ROOM_7_OR_MORE = 7;

    public const array ALL = [
        self::ROOM_1,
        self::ROOM_2,
        self::ROOM_3,
        self::ROOM_4,
        self::ROOM_5,
        self::ROOM_6,
        self::ROOM_7_OR_MORE,
    ];
}

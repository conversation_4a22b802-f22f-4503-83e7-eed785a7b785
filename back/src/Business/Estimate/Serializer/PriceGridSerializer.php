<?php declare(strict_types=1);

namespace App\Business\Estimate\Serializer;

use App\Business\Estimate\Entity\PriceGrid;
use App\Business\Estimate\Enum\PriceGridTypeEnum;
use App\Business\Estimate\Model\Pricing\Pricing1;
use App\Business\Estimate\Model\Pricing\Pricing2;
use App\Business\Estimate\Model\Pricing\Pricing3;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Throwable;

final class PriceGridSerializer implements DenormalizerInterface, DenormalizerAwareInterface
{
    private const string ALREADY_CALLED = '616d35e7916d43fdb4074531457da230';

    use DenormalizerAwareTrait;

    public function __construct(
        private readonly LoggerInterface $logger,
    ) {
    }

    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): PriceGrid
    {
        try {
            $context[self::ALREADY_CALLED] = true;
            $context[AbstractNormalizer::IGNORED_ATTRIBUTES] = ['gridData'];

            $gridData = match ($data['type']) {
                PriceGridTypeEnum::GRID_1 => $this->denormalizer->denormalize($data['gridData'], Pricing1::class, $format, $context),
                PriceGridTypeEnum::GRID_2 => $this->denormalizer->denormalize($data['gridData'], Pricing2::class, $format, $context),
                PriceGridTypeEnum::GRID_3 => $this->denormalizer->denormalize($data['gridData'], Pricing3::class, $format, $context),
                default => throw new BadRequestException(),
            };

            $grid = $this->denormalizer->denormalize($data, PriceGrid::class, $format, $context);
            $grid->gridData = $gridData;

            return $grid;
        } catch (Throwable $exception) {
            $this->logger->error('unable to denormalize price grid', ['exception' => $exception]);

            dd($exception);
            throw new BadRequestException('Unable to denormalize price grid');
        }
    }

    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []): bool
    {
        return PriceGrid::class === $type && !isset($context[self::ALREADY_CALLED]);
    }
}

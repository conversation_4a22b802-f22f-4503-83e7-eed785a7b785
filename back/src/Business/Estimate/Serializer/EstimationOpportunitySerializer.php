<?php declare(strict_types=1);

namespace App\Business\Estimate\Serializer;

use App\Business\Estimate\Entity\Estimate;
use App\Business\Estimate\Model\EstimationOpportunity;
use Psr\Log\LoggerInterface;
use <PERSON>ymfony\Component\Serializer\Normalizer\DenormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use function array_key_exists;
use function in_array;

final class EstimationOpportunitySerializer implements DenormalizerInterface, DenormalizerAwareInterface
{
    private const string ALREADY_CALLED = 'b7e4f1c9a6d823f5e0b47a3d9c8f2156';

    use DenormalizerAwareTrait;

    public function __construct(
        private readonly LoggerInterface $logger,
    ) {
    }

    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): EstimationOpportunity
    {
        $context[self::ALREADY_CALLED] = true;

        $opportunity = $this->denormalizer->denormalize($data, EstimationOpportunity::class, $format, $context);

        if (!$this->isActive($data, 'plomb')) {
            $opportunity->disablePlomb();
        }

        if (!$this->isActive($data, 'amiante')) {
            $opportunity->disableAmiante();
        }

        if (!$this->isActive($data, 'electricite')) {
            $opportunity->disableElectricite();
        }

        if (!$this->isActive($data, 'gaz')) {
            $opportunity->disableGaz();
        }

        if (!$this->isActive($data, 'dpe')) {
            $opportunity->disableDpe();
        }

        if (!$this->isActive($data, 'carrez')) {
            $opportunity->disableCarrez();
        }

        if (!$this->isActive($data, 'mesurage')) {
            $opportunity->disableMesurage();
        }

        if (!$this->isActive($data, 'termites')) {
            $opportunity->disableTermites();
        }

        if (!$this->isActive($data, 'decent')) {
            $opportunity->disableDecent();
        }

        if (!$this->isActive($data, 'etatLieux')) {
            $opportunity->disableEtatLieux();
        }

        if (!$this->isActive($data, 'erp')) {
            $opportunity->disableErp();
        }

        if (!$this->isActive($data, 'audit')) {
            $opportunity->disableAudit();
        }

        if (!$this->isActive($data, 'assainissement')) {
            $opportunity->disableAssainissement();
        }

        return $opportunity;
    }

    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []): bool
    {
        return
            EstimationOpportunity::class === $type
            && !isset($context[self::ALREADY_CALLED])
            && in_array(Estimate::RECALCULATE_ESTIMATE, $context['groups'] ?? [], true);
    }

    private function isActive(array $data, string $activity): bool
    {
        if (!array_key_exists($activity, $data)) {
            return false;
        }

        return true === $data[$activity];
    }
}

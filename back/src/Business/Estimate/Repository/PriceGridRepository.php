<?php declare(strict_types=1);

namespace App\Business\Estimate\Repository;

use App\Business\Estimate\Entity\PriceGrid;
use App\Entity\Agency\Agency;
use App\Entity\Location\LocationCity;
use Doctrine\ORM\EntityRepository;

/**
 * @template-extends EntityRepository<PriceGrid>
 */
class PriceGridRepository extends EntityRepository
{
    public function findByCityAndAgency(?Agency $agency, ?LocationCity $city): ?PriceGrid
    {
        if (null === $agency) {
            return null;
        }

        // @todo

        return null;
    }
}

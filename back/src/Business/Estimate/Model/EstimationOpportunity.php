<?php declare(strict_types=1);

namespace App\Business\Estimate\Model;

use Symfony\Component\Serializer\Attribute\Ignore;

/**
 * We have all opportunity by default and adjust them by results of quiz and agency.
 */
final class EstimationOpportunity
{
    public bool $plomb = true;
    public bool $amiante = true;
    public bool $electricite = true;
    public bool $gaz = true;

    public bool $dpe = true;
    public bool $carrez = true;
    public bool $mesurage = true;
    public bool $termites = true;
    public bool $decent = true;
    public bool $etatLieux = true;
    public bool $erp = true;
    public bool $audit = true;

    public bool $assainissement = false; // not available for now

    public function isGaz(): bool
    {
        return $this->gaz;
    }

    public function disableGaz(): void
    {
        $this->gaz = false;
    }

    public function isDpe(): bool
    {
        return $this->dpe;
    }

    public function disableDpe(): void
    {
        $this->dpe = false;
    }

    public function isCarrez(): bool
    {
        return $this->carrez;
    }

    public function disableCarrez(): void
    {
        $this->carrez = false;
    }

    public function isMesurage(): bool
    {
        return $this->mesurage;
    }

    public function disableMesurage(): void
    {
        $this->mesurage = false;
    }

    public function isTermites(): bool
    {
        return $this->termites;
    }

    public function disableTermites(): void
    {
        $this->termites = false;
    }

    public function isDecent(): bool
    {
        return $this->decent;
    }

    public function disableDecent(): void
    {
        $this->decent = false;
    }

    public function isEtatLieux(): bool
    {
        return $this->etatLieux;
    }

    public function disableEtatLieux(): void
    {
        $this->etatLieux = false;
    }

    public function isErp(): bool
    {
        return $this->erp;
    }

    public function disableErp(): void
    {
        $this->erp = false;
    }

    public function isAudit(): bool
    {
        return $this->audit;
    }

    public function disableAudit(): void
    {
        $this->audit = false;
    }

    public function isPlomb(): bool
    {
        return $this->plomb;
    }

    public function disablePlomb(): void
    {
        $this->plomb = false;
    }

    public function isAmiante(): bool
    {
        return $this->amiante;
    }

    public function disableAmiante(): void
    {
        $this->amiante = false;
    }

    public function isElectricite(): bool
    {
        return $this->electricite;
    }

    public function disableElectricite(): void
    {
        $this->electricite = false;
    }

    public function isAssainissement(): bool
    {
        return $this->assainissement;
    }

    public function enableAssainissement(): void
    {
        $this->assainissement = false;
    }

    public function disableAssainissement(): void
    {
        $this->assainissement = false;
    }

    public function disableAll(): void
    {
        $this->disablePlomb();
        $this->disableAmiante();
        $this->disableElectricite();
        $this->disableGaz();
        $this->disableDpe();
        $this->disableCarrez();
        $this->disableMesurage();
        $this->disableTermites();
        $this->disableDecent();
        $this->disableEtatLieux();
        $this->disableErp();
        $this->disableAudit();
    }

    public function disableAllButAudit(): void
    {
        $this->disableAll();
        $this->audit = false;
    }

    public function countWantedOpportunities(): int
    {
        $count = 0;
        foreach (get_object_vars($this) as $item) {
            if (true === $item) {
                $count++;
            }
        }

        return $count;
    }

    #[Ignore]
    public function getWanted(): array
    {
        $wanted = [];

        foreach (get_object_vars($this) as $item) {
            if (true === $item) {
                $wanted[] = $item;
            }
        }

        return $wanted;
    }
}

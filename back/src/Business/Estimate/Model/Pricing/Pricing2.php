<?php declare(strict_types=1);

namespace App\Business\Estimate\Model\Pricing;

use App\Business\Estimate\Enum\CrepRatioEnum;
use App\Business\Estimate\Enum\PriceGridTypeEnum;
use Symfony\Component\Validator\Constraints as Assert;
use Throwable;

class Pricing2 implements PricingModelInterface
{
    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $audit1Room = null;

    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $audit2Room = null;

    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $audit3Room = null;

    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $audit4Room = null;

    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $audit5Room = null;

    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $audit6Room = null;

    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $audit7Room = null;

    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $audit8Room = null;

    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $basePrice = null; // that's the price for 1 mission in 1 room (except for audit with custom price)

    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $apartmentRatio = null;

    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $priceRoom2 = null;

    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $priceRoom3 = null;

    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $priceRoom4 = null;

    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $priceRoom5 = null;

    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $priceRoom6 = null;

    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $priceRoom7 = null;

    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $priceRoom8 = null;

    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $decrementActivity2 = null;

    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $decrementActivity3 = null;

    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $decrementActivity4 = null;

    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $decrementActivity5 = null;

    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $decrementActivity6 = null;

    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $decrementActivity7 = null;

    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $decrementActivity8 = null;

    #[Assert\NotNull]
    #[Assert\Choice(choices: CrepRatioEnum::ALL)]
    public ?float $crepRatio = null;

    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $amiantePrice = null;

    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $erpPrice = null;

    #[Assert\GreaterThanOrEqual(0)]
    #[Assert\LessThanOrEqual(100)]
    public ?int $appointmentDiscount = null;

    #[Assert\Length(min: 1, max: 300)]
    public ?string $cgv = null;

    public function for(): int
    {
        return PriceGridTypeEnum::GRID_2;
    }

    public function getPriceForAudit(int $roomNumber): ?float
    {
        $roomNumber = min($roomNumber, 8);

        return $this->getPrice("audit{$roomNumber}Room");
    }

    public function getPriceForPrestation(int $roomNumber, int $prestationNumber, bool $hasCrep, bool $hasErp): ?float
    {
        $roomNumber = min($roomNumber, 8);
        if (null === $this->basePrice) {
            return null;
        }

        $price = $this->basePrice;

        if ($hasErp) {
            if (0.0 === (float)$this->erpPrice) {
                return null;
            }

            $price += $this->erpPrice;
            $prestationNumber--;
        }

        for ($i = 2; $i <= $roomNumber; $i++) {
            if (null === $roomPrice = $this->getPrice("priceRoom{$i}")) {
                return null;
            }

            $price += $roomPrice;
        }

        for ($i = 2; $i <= $prestationNumber; $i++) {
            if (null === $prestationPrice = $this->getPrice("decrementActivity{$i}")) {
                return null;
            }
            $price += $prestationPrice;
        }

        if ($hasCrep) {
            if ($i > 8) {
                $i = 8;
            }

            if (null === $prestationPrice = $this->getPrice("decrementActivity{$i}")) {
                return null;
            }

            if ($this->crepRatio === CrepRatioEnum::RATIO_1_5) {
                $price += ceil($prestationPrice/2);
            } elseif ($this->crepRatio === CrepRatioEnum::RATIO_2) {
                $price += ($prestationPrice);
            }
        }

        return $price;
    }

    private function getPrice(string $property): ?float
    {
        try {
            if (property_exists($this, $property)) {
                if (0.0 === $this->{$property}) {
                    return null;
                }

                return $this->{$property};
            }

            return null;
        } catch (Throwable) {
            return null;
        }
    }
}

<?php declare(strict_types=1);

namespace App\Business\Estimate\Model\Pricing;

use App\Business\Estimate\Enum\PriceGridTypeEnum;
use Symfony\Component\Validator\Constraints as Assert;
use Throwable;

class Pricing3 implements PricingModelInterface
{
    #[Assert\NotNull]
    #[Assert\Valid]
    public ?Pricing3Item $activity1 = null;

    #[Assert\NotNull]
    #[Assert\Valid]
    public ?Pricing3Item $activity2 = null;

    #[Assert\NotNull]
    #[Assert\Valid]
    public ?Pricing3Item $activity3 = null;

    #[Assert\NotNull]
    #[Assert\Valid]
    public ?Pricing3Item $activity4 = null;

    #[Assert\NotNull]
    #[Assert\Valid]
    public ?Pricing3Item $activity5 = null;

    #[Assert\NotNull]
    #[Assert\Valid]
    public ?Pricing3Item $activity6 = null;

    #[Assert\NotNull]
    #[Assert\Valid]
    public ?Pricing3Item $activity7 = null;

    #[Assert\NotNull]
    #[Assert\Valid]
    public ?Pricing3Item $auditPrice = null;

    #[Assert\NotNull]
    #[Assert\Valid]
    public ?Pricing3Item $erpPrice = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $supplementCrep = null;

    #[Assert\GreaterThanOrEqual(0)]
    #[Assert\LessThanOrEqual(100)]
    public ?int $appointmentDiscount = null;

    #[Assert\Length(min: 1, max: 300)]
    public ?string $cgv = null;

    public function for(): int
    {
        return PriceGridTypeEnum::GRID_3;
    }

    public function getPriceForAudit(string $propertyType, int $roomNumber): ?float
    {
        return $this->auditPrice->getPrice($propertyType, $roomNumber);
    }

    public function getPriceForErp(string $propertyType, int $roomNumber): ?float
    {
        return $this->erpPrice->getPrice($propertyType, $roomNumber);
    }

    public function getPriceForActivities(int $activityNumber, string $propertyType, int $roomNumber): ?float
    {
        if ($activityNumber < 1) {
            return null;
        }

        $activityNumber = min($activityNumber, 7);

        try {
            $property = "activity{$activityNumber}";

            return $this->{$property}->getPrice($propertyType, $roomNumber);
        } catch (Throwable) {
            return null;
        }
    }
}

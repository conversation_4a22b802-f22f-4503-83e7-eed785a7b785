<?php declare(strict_types=1);

namespace App\Business\Estimate\Model\Pricing;

use Symfony\Component\Validator\Constraints as Assert;

final class Advantages
{
    /** @var Advantage[] */
    #[Assert\Valid]
    #[Assert\All([
        new Assert\Type(Advantage::class),
    ])]
    public array $advantages = [];

    public function getPercent(?string $discountCode): ?int
    {
        if (null === $discountCode) {
            return null;
        }

        foreach ($this->advantages as $advantage) {
            if ($advantage->code === $discountCode) {
                return $advantage->percentage;
            }
        }

        return null;
    }

    public function add(Advantage $advantage): void
    {
        $this->advantages[] = $advantage;
    }
}

<?php declare(strict_types=1);

namespace App\Business\Estimate\Model\Pricing;

use App\Business\Estimate\Enum\DiscountTypeEnum;
use App\Business\Estimate\Enum\PriceGridTypeEnum;
use Symfony\Component\Validator\Constraints as Assert;
use Throwable;
use function count;

class Pricing1 implements PricingModelInterface
{
    /** @var Pricing1Item[] */
    #[Assert\Valid]
    public array $pricings = [];

    #[Assert\Choice(choices: DiscountTypeEnum::ALL)]
    public ?string $discountType = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $discountFor2 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $discountFor3 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $discountFor4 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $discountFor5 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $discountFor6 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $discountFor7AndMore = null;

    #[Assert\NotNull]
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $amiantePrice = null;

    #[Assert\GreaterThanOrEqual(0)]
    #[Assert\LessThanOrEqual(100)]
    public ?int $appointmentDiscountPercent = null;

    #[Assert\Length(min: 0, max: 300)]
    public ?string $cgv = null;

    public function for(): int
    {
        return PriceGridTypeEnum::GRID_1;
    }

    public function getPack(array $wantedActivities): ?Pricing1Item
    {
        foreach ($this->pricings as $pricing) {
            if (!$pricing->isPack()) {
                continue;
            }

            if (
                0 === count(array_diff($wantedActivities, $pricing->types))
                && 0 === count(array_diff($pricing->types, $wantedActivities))
            ) {
                return $pricing;
            }
        }

        return null;
    }

    public function getPriceForActivities(array $wantedActivities, string $propertyType, int $roomNumber): ?float
    {
        $price = 0.0;
        foreach ($wantedActivities as $wantedActivity) {
            if (null === $pricing = $this->getPriceForActivity($wantedActivity)) {
                return null;
            }

            if (null === $p = $pricing->getPrice($propertyType, $roomNumber)) {
                return null;
            }

            $price += $p;
        }

        return $price;
    }

    public function getDiscountForActivity(int $activity): ?float
    {
        if (1 >= $activity) {
            return null;
        }

        $activity = min($activity, 7);

        $property = "discountFor{$activity}";

        if (7 === $activity) {
            $property = "discountFor{$activity}AndMore";
        }

        try {
            if (property_exists($this, $property)) {
                /* @phpstan-ignore-next-line */
                if (0.0 === $this->{$property}) {
                    return null;
                }

                return $this->{$property};
            }

            return null;
        } catch (Throwable) {
            return null;
        }
    }

    private function getPriceForActivity(string $wantedActivity): ?Pricing1Item
    {
        foreach ($this->pricings as $pricing) {
            if ($pricing->type === $wantedActivity) {
                return $pricing;
            }
        }

        return null;
    }
}

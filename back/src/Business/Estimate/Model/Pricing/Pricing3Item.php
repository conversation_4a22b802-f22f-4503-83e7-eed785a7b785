<?php declare(strict_types=1);

namespace App\Business\Estimate\Model\Pricing;

use App\Business\Estimate\Enum\PropertyTypeEnum;
use Symfony\Component\Validator\Constraints as Assert;
use Throwable;

class Pricing3Item
{
    #[Assert\GreaterThanOrEqual(0)]
    public ?float $f1 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $f2 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $f3 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $f4 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $f5 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $f6 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $f7 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $t2 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $t3 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $t4 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $t5 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $t6 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $t7 = null;

    public function getPrice(string $propertyType, int $roomNumber): ?float
    {
        $roomNumber = min($roomNumber, 7);

        $property = null;
        if (PropertyTypeEnum::APARTMENT === $propertyType) {
            $property = "f{$roomNumber}";
        } elseif (
            PropertyTypeEnum::DETACHED_HOUSE === $propertyType
            || PropertyTypeEnum::CO_OWNED_HOUSE === $propertyType
        ) {
            $roomNumber = max($roomNumber, 2); // no t1
            $property = "t{$roomNumber}";
        }

        try {
            if (property_exists($this, $property)) {
                if (0.0 === (float)$this->{$property}) {
                    return null;
                }

                return $this->{$property};
            }

            return null;
        } catch (Throwable) {
            return null;
        }
    }
}

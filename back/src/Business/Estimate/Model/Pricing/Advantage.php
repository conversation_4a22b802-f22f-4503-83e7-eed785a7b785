<?php declare(strict_types=1);

namespace App\Business\Estimate\Model\Pricing;

use Symfony\Component\Validator\Constraints as Assert;

final class Advantage
{
    public function __construct(
        #[Assert\NotBlank(allowNull: true)]
        #[Assert\Length(min: 0, max: 20)]
        public string $code,
        #[Assert\GreaterThanOrEqual(0)]
        #[Assert\LessThanOrEqual(100)]
        public int $percentage,
    ) {
    }
}

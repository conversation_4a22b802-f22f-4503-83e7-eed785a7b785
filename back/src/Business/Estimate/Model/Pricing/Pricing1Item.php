<?php declare(strict_types=1);

namespace App\Business\Estimate\Model\Pricing;

use App\Business\Estimate\Enum\ActivityTypeEnum;
use App\Business\Estimate\Enum\PropertyTypeEnum;
use Symfony\Component\Validator\Constraints as Assert;
use Throwable;

class Pricing1Item
{
    #[Assert\NotNull]
    #[Assert\Choice(choices: ActivityTypeEnum::ALL)]
    public ?string $type = null;

    #[Assert\When(
        expression: 'this.type === "' . ActivityTypeEnum::PACK . '"',
        constraints: [new Assert\Count(min: 1), new Assert\All([
            new Assert\NotBlank(),
            new Assert\Choice(choices: ActivityTypeEnum::ALL),
        ])]
    )]
    #[Assert\When(
        expression: 'this.type !== "' . ActivityTypeEnum::PACK . '"',
        constraints: [new Assert\IsNull()]
    )]
    public ?array $types = null;

    #[Assert\When(
        expression: 'this.type === "' . ActivityTypeEnum::PACK . '"',
        constraints: [new Assert\NotBlank()]
    )]
    #[Assert\When(
        expression: 'this.type !== "' . ActivityTypeEnum::PACK . '"',
        constraints: [new Assert\IsNull()]
    )]
    public ?string $packName = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $f1 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $f2 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $f3 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $f4 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $f5 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $f6 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $f7 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $t2 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $t3 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $t4 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $t5 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $t6 = null;

    #[Assert\GreaterThanOrEqual(0)]
    public ?float $t7 = null;

    public function isPack(): bool
    {
        return ActivityTypeEnum::PACK === $this->type;
    }

    public function getPrice(string $propertyType, int $roomNumber): ?float
    {
        $roomNumber = min($roomNumber, 7);

        $property = null;
        if (PropertyTypeEnum::APARTMENT === $propertyType) {
            $property = "f{$roomNumber}";
        } elseif (
            PropertyTypeEnum::DETACHED_HOUSE === $propertyType
            || PropertyTypeEnum::CO_OWNED_HOUSE === $propertyType
        ) {
            $roomNumber = max($roomNumber, 2); // no t1
            $property = "t{$roomNumber}";
        }

        try {
            if (property_exists($this, $property)) {
                if (0.0 === $this->{$property}) {
                    return null;
                }

                return $this->{$property};
            }

            return null;
        } catch (Throwable) {
            return null;
        }
    }
}

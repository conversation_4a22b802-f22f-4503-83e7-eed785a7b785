<?php declare(strict_types=1);

namespace App\Business\Estimate;

use App\Business\Estimate\Entity\Estimate;
use App\Business\Estimate\Enum\ActivityTypeEnum;
use App\Business\Estimate\Enum\BuildYearEnum;
use App\Business\Estimate\Enum\EstimateTypeEnum;
use App\Business\Estimate\Enum\PropertyTypeEnum;
use App\Business\Estimate\Model\EstimationOpportunity;
use function in_array;

/**
 * Apparently, it's not blocking, it's just for a pre-fill of quiz...
 * So we need to call this only on the first quiz result.
 */
final readonly class EstimateOpportunity
{
    public function getOpportunityForEstimate(Estimate $quiz): EstimationOpportunity
    {
        $opportunity = new EstimationOpportunity();

        $opportunity->disableErp(); // just to mime current prod

        $opportunity->disableDecent();
        $opportunity->disableEtatLieux();

        $this->limitByType($opportunity, $quiz->type);
        $this->limitByPropertyType($opportunity, $quiz->propertyType);
        $this->limitByTypeAndYear($opportunity, $quiz->type, $quiz->buildYear);

        if (!$quiz->electricityOlderThan15Years) {
            $opportunity->disableElectricite();
        }

        if (!$quiz->gazOlderThan15Years) {
            $opportunity->disableGaz();
        }

        if (!$quiz->city->hasTermite) {
            $opportunity->disableTermites();
        }

        return $opportunity;
    }

    /**
     * @return string[]
     */
    public function opportunityToActivity(string $estimateType, EstimationOpportunity $opportunity): array
    {
        if (!in_array($estimateType, EstimateTypeEnum::ALL)) {
            return [];
        }

        if (
            EstimateTypeEnum::AUDIT === $estimateType
            && $opportunity->isAudit()
        ) {
            return [ActivityTypeEnum::AUDIT];
        }

        if (EstimateTypeEnum::SELL === $estimateType) {
            $activities = [];

            if ($opportunity->isAmiante()) {
                $activities[] = ActivityTypeEnum::TRANS_AMIANTE;
            }

            if ($opportunity->isElectricite()) {
                $activities[] = ActivityTypeEnum::TRANS_ELECTIRICITE;
            }

            if ($opportunity->isErp()) {
                $activities[] = ActivityTypeEnum::TRANS_ERP;
            }

            if ($opportunity->isGaz()) {
                $activities[] = ActivityTypeEnum::TRANS_GAZ;
            }

            if ($opportunity->isCarrez()) {
                $activities[] = ActivityTypeEnum::TRANS_CARREZ;
            }

            if ($opportunity->isDpe()) {
                $activities[] = ActivityTypeEnum::TRANS_PERF_ENERGETIQUE;
            }

            if ($opportunity->isPlomb()) {
                $activities[] = ActivityTypeEnum::TRANS_PLOMB;
            }

            if ($opportunity->isTermites()) {
                $activities[] = ActivityTypeEnum::TRANS_TERMITES;
            }

            return $activities;
        }

        if (EstimateTypeEnum::RENT === $estimateType) {
            $activities = [];
            if ($opportunity->isErp()) {
                $activities[] = ActivityTypeEnum::LOC_ERP;
            }

            if ($opportunity->isEtatLieux()) {
                $activities[] = ActivityTypeEnum::LOC_ETAT_LIEUX;
            }

            if ($opportunity->isDecent()) {
                $activities[] = ActivityTypeEnum::LOC_DECENT;
            }

            if ($opportunity->isMesurage()) {
                $activities[] = ActivityTypeEnum::LOC_SURFACE;
            }

            if ($opportunity->isDpe()) {
                $activities[] = ActivityTypeEnum::LOC_PERF_ENERGETIQUE;
            }

            if ($opportunity->isPlomb()) {
                $activities[] = ActivityTypeEnum::LOC_PLOMB;
            }

            if ($opportunity->isElectricite()) {
                $activities[] = ActivityTypeEnum::LOC_ELECTRICITE;
            }

            if ($opportunity->isAmiante()) {
                $activities[] = ActivityTypeEnum::LOC_AMIANTE;
            }

            if ($opportunity->isGaz()) {
                $activities[] = ActivityTypeEnum::LOC_GAZ;
            }

            return $activities;
        }

        return [];
    }

    private function limitByType(EstimationOpportunity &$opportunity, string $type): void
    {
        if (EstimateTypeEnum::AUDIT === $type) {
            $opportunity->disablePlomb();
            $opportunity->disableAmiante();
            $opportunity->disableElectricite();
            $opportunity->disableGaz();
            $opportunity->disableDpe();
            $opportunity->disableCarrez();
            $opportunity->disableMesurage();
            $opportunity->disableTermites();
            $opportunity->disableErp();

            return;
        }

        $opportunity->disableAudit();

        if (EstimateTypeEnum::RENT === $type) {
            $opportunity->disableAmiante();
            $opportunity->disableCarrez();
            $opportunity->disableTermites();

            return;
        }

        if (EstimateTypeEnum::SELL === $type) {
            $opportunity->disableMesurage();

            return;
        }
    }

    private function limitByPropertyType(EstimationOpportunity &$opportunity, string $propertyType): void
    {
        if (PropertyTypeEnum::OTHER === $propertyType) {
            $opportunity->disableAll();

            return;
        }

        if (PropertyTypeEnum::DETACHED_HOUSE === $propertyType) {
            $opportunity->disableCarrez();

            return;
        }
    }

    private function limitByTypeAndYear(EstimationOpportunity &$opportunity, string $type, string $year): void
    {
        if (BuildYearEnum::BETWEEN_1949_1997 === $year || BuildYearEnum::PAST_1997 === $year) {
            $opportunity->disablePlomb();
            $opportunity->disablePlomb();
        }

        if (EstimateTypeEnum::SELL === $type && BuildYearEnum::PAST_1997 === $year) {
            $opportunity->disableAmiante();
        }
    }
}

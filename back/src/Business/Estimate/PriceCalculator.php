<?php declare(strict_types=1);

namespace App\Business\Estimate;

use App\Business\Estimate\Entity\Estimate;
use App\Business\Estimate\Entity\PriceGrid;
use App\Business\Estimate\Enum\DiscountTypeEnum;
use App\Business\Estimate\Enum\EstimateTypeEnum;
use App\Business\Estimate\Enum\PriceGridTypeEnum;
use App\Business\Estimate\Enum\PropertyTypeEnum;
use App\Business\Estimate\Model\Pricing\Pricing1;
use App\Business\Estimate\Model\Pricing\Pricing2;
use App\Business\Estimate\Model\Pricing\Pricing3;
use App\Entity\Agency\Agency;
use App\Entity\Location\LocationArea;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use RuntimeException;

final readonly class PriceCalculator
{
    public function __construct(
        private EntityManagerInterface $em,
        private LoggerInterface $logger,
        private EstimateOpportunity $estimateOpportunity,
    ) {
    }

    public function calculate(Estimate $estimate): void
    {
        if (null === $priceGrid = $this->getPriceGrid($estimate->city->area, $estimate->agency)) {
            return;
        }

        match ($priceGrid->type) {
            PriceGridTypeEnum::GRID_1 => $this->calculateGrid1($estimate, $priceGrid),
            PriceGridTypeEnum::GRID_2 => $this->calculateGrid2($estimate, $priceGrid),
            PriceGridTypeEnum::GRID_3 => $this->calculateGrid3($estimate, $priceGrid),
            default => throw new RuntimeException("Not supposed to calculate price for grid {$priceGrid->type}."),
        };

        if ($estimate->discountCode) {
            $percent = $priceGrid->advantages->getPercent($estimate->discountCode);

            $estimate->price *= (1 - ($percent / 100));
        }

        if (
            PriceGridTypeEnum::GRID_1 === $priceGrid->type
            && null !== $priceGrid->gridData->appointmentDiscountPercent
            && 0 !== $priceGrid->gridData->appointmentDiscountPercent
        ) {
            $estimate->appointmentPrice = $estimate->price * (1 - ($priceGrid->gridData->appointmentDiscountPercent / 100));
        }

        if (
            (PriceGridTypeEnum::GRID_2 === $priceGrid->type || PriceGridTypeEnum::GRID_3 === $priceGrid->type)
            && null !== $priceGrid->gridData->appointmentDiscount
            && 0 !== $priceGrid->gridData->appointmentDiscount
        ) {
            $estimate->appointmentPrice = $estimate->price * (1 - ($priceGrid->gridData->appointmentDiscount / 100));
        }

        if (null !== $estimate->price) {
            $estimate->price = round($estimate->price, 2);
        }

        if (null !== $estimate->appointmentPrice) {
            $estimate->appointmentPrice = round($estimate->appointmentPrice, 2);
        }
    }

    public function getPriceGrid(LocationArea $area, Agency $agency): ?PriceGrid
    {
        $priceGrid = $this->em->getRepository(PriceGrid::class)->findOneBy([
            'agency' => $agency,
            'area' => $area,
            'isActive' => true,
        ]);

        if (null !== $priceGrid) {
            return $priceGrid;
        }

        return $this->em->getRepository(PriceGrid::class)->findOneBy([
            'agency' => $agency,
            'area' => null,
            'isActive' => true,
        ]);
    }

    private function calculateGrid1(Estimate $estimate, PriceGrid $priceGrid): void
    {
        if (!$priceGrid->gridData instanceof Pricing1) {
            $this->logger->error('Not supposed to calculate this grid', [
                'estimate' => $estimate,
                'priceGrid' => $priceGrid,
            ]);

            $estimate->price = null;

            return;
        }

        $wantedActivities = $this->estimateOpportunity->opportunityToActivity($estimate->type, $estimate->opportunityWanted);

        if (null !== $pack = $priceGrid->gridData->getPack($wantedActivities)) {
            $estimate->price = $pack->getPrice($estimate->propertyType, $estimate->roomNumber);
        } else {
            $estimate->price = $priceGrid->gridData->getPriceForActivities($wantedActivities, $estimate->propertyType, $estimate->roomNumber);
        }

        if (null === $estimate->price) {
            return;
        }

        // business rule => erp not included in discount
        $activityCount = $estimate->opportunityWanted->countWantedOpportunities();
        if ($estimate->opportunityWanted->isErp()) {
            $activityCount--;
        }

        if (null !== $discount = $priceGrid->gridData->getDiscountForActivity($activityCount)) {
            if (DiscountTypeEnum::FIXED === $priceGrid->gridData->discountType) {
                $estimate->price -= $discount;
            } elseif (DiscountTypeEnum::PERCENT === $priceGrid->gridData->discountType) {
                $estimate->price *= (1 - ($discount / 100));
            }
        }
    }

    private function calculateGrid2(Estimate $estimate, PriceGrid $priceGrid): void
    {
        if (!$priceGrid->gridData instanceof Pricing2) {
            $this->logger->error('Not supposed to calculate this grid', [
                'estimate' => $estimate,
                'priceGrid' => $priceGrid,
            ]);

            $estimate->price = null;

            return;
        }

        if (EstimateTypeEnum::AUDIT === $estimate->type) {
            $estimate->price = $priceGrid->gridData->getPriceForAudit($estimate->roomNumber);
        } else {
            $estimate->price = $priceGrid->gridData->getPriceForPrestation(
                $estimate->roomNumber,
                $estimate->opportunityWanted->countWantedOpportunities(),
                $estimate->opportunityWanted->isPlomb(),
                $estimate->opportunityWanted->isErp(),
            ); // @todo
        }

        if (null === $estimate->price) {
            return;
        }

        // new crazy business rule
        if (PropertyTypeEnum::APARTMENT === $estimate->propertyType) {
            if ($estimate->opportunityWanted->isErp()) {
                $estimate->price -= $priceGrid->gridData->erpPrice;
                $estimate->price *= $priceGrid->gridData->apartmentRatio;
                $estimate->price += $priceGrid->gridData->erpPrice;
            } else {
                $estimate->price *= $priceGrid->gridData->apartmentRatio;
            }
        }
    }

    private function calculateGrid3(Estimate $estimate, PriceGrid $priceGrid): void
    {
        if (!$priceGrid->gridData instanceof Pricing3) {
            $this->logger->error('Not supposed to calculate this grid', [
                'estimate' => $estimate,
                'priceGrid' => $priceGrid,
            ]);

            $estimate->price = null;

            return;
        }

        if (EstimateTypeEnum::AUDIT === $estimate->type) {
            $estimate->price = $priceGrid->gridData->getPriceForAudit($estimate->propertyType, $estimate->roomNumber);
        } else {
            $wanted = $estimate->opportunityWanted->countWantedOpportunities();

            if ($estimate->opportunityWanted->isErp()) {
                $wanted--;

                $erpPrice = $priceGrid->gridData->getPriceForErp($estimate->propertyType, $estimate->roomNumber);

                if (null === $erpPrice) {
                    $estimate->price = null;

                    return;
                }
            }

            if (0 >= $wanted) {
                $estimate->price = null;

                return;
            }

            $estimate->price = $priceGrid->gridData->getPriceForActivities($wanted, $estimate->propertyType, $estimate->roomNumber);

            if (
                null === $estimate->price
                || 0.0 === $estimate->price
            ) {
                $estimate->price = null;
                return;
            }

            if (isset($erpPrice)) {
                $estimate->price += $erpPrice;
            }

            if (
                null !== $priceGrid->gridData->supplementCrep
                && 0.0 !== $priceGrid->gridData->supplementCrep
                && $estimate->opportunityWanted->isPlomb()
            ) {
                $estimate->price += $priceGrid->gridData->supplementCrep;
            }
        }
    }
}

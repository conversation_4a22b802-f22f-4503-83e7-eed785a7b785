<?php declare(strict_types=1);

namespace App\Business\Estimate;

use App\Business\Estimate\Model\AppointmentProposal;
use DateTime;
use function in_array;

abstract class AppointmentFactory
{
    private const int SATURDAY = 6;
    private const int SUNDAY = 7;

    public static function createForPrices(?float $price, ?float $appointmentPrice): AppointmentProposal
    {
        return new AppointmentProposal(
            firstDate: $firstDate = self::getWorkableDate(4)->setTime(9, 0),
            secondDate: self::getWorkableDate(5, $firstDate)->setTime(14, 0),
            firstPrice: $price,
            secondPrice: $appointmentPrice ?? $price,
        );
    }

    private static function getWorkableDate(int $days, ?DateTime $firstDate = null): DateTime
    {
        $date = new DateTime();
        $date->modify("+{$days} days");

        while (
            in_array($date->format('N'), [self::SATURDAY, self::SUNDAY])
            || $firstDate?->format('N') === $date->format('N')
        ) {
            $date->modify('+1 day');
        }

        return $date;
    }
}

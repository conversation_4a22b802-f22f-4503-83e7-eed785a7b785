<?php

declare(strict_types=1);

namespace App\Business\Estimate\Entity;

use ApiPlatform\Doctrine\Orm\Filter\BooleanFilter;
use ApiPlatform\Doctrine\Orm\Filter\OrderFilter;
use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata as APM;
use App\Api\Filter\UuidFilter;
use App\Business\Estimate\Enum\PriceGridTypeEnum;
use App\Business\Estimate\Model\Pricing\Advantages;
use App\Business\Estimate\Model\Pricing\PricingModelInterface;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Agency\Agency;
use App\Entity\Location\LocationArea;
use App\Entity\Traits\CreatedAtTrait;
use App\Entity\Traits\UpdatedAtTrait;
use App\Entity\Traits\UuidTrait;
use App\Security\Voter\PriceGridVoter;
use Doctrine\ORM\Mapping as ORM;
use Dunglas\DoctrineJsonOdm\Type\JsonDocumentType;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity]
#[ORM\Table(TableEnum::PRICE_GRID)]
#[ApiResource]
#[APM\GetCollection(paginationClientItemsPerPage: true, paginationClientEnabled: true, order: ['createdAt' => 'ASC'], security: 'is_granted("' . PriceGridVoter::GET_COLLECTION . '")')]
#[APM\Get(security: 'is_granted("' . PriceGridVoter::GET . '", object)')]
#[ORM\UniqueConstraint(columns: ['agency_uuid', 'area_uuid', 'type'])]
#[APM\Put(securityPostDenormalize: 'is_granted("' . PriceGridVoter::PUT . '", object)')]
#[APM\Post(securityPostDenormalize: 'is_granted("' . PriceGridVoter::POST . '", object)')]
#[APM\Delete(securityPostDenormalize: 'is_granted("' . PriceGridVoter::DELETE . '", object)')]
#[APM\ApiFilter(SearchFilter::class, properties: ['type' => 'exact'])]
#[APM\ApiFilter(BooleanFilter::class, properties: ['isActive'])]
#[APM\ApiFilter(UuidFilter::class, properties: ['agency'])]
#[APM\ApiFilter(OrderFilter::class, properties: ['area.name' => 'ASC'])]
class PriceGrid
{
    use UuidTrait;
    use CreatedAtTrait;
    use UpdatedAtTrait;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'agency_uuid', referencedColumnName: 'uuid', nullable: false, onDelete: 'CASCADE')]
    #[Assert\NotNull]
    public ?Agency $agency = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'area_uuid', referencedColumnName: 'uuid', onDelete: 'SET NULL')]
    #[APM\ApiProperty(readableLink: true, writableLink: false)]
    public ?LocationArea $area = null; // can be null for global price

    #[ORM\Column]
    #[Assert\NotNull]
    #[Assert\Choice(choices: PriceGridTypeEnum::ALL)]
    public ?int $type = null;

    #[ORM\Column(type: JsonDocumentType::NAME)]
    #[Assert\NotNull]
    #[Assert\Valid]
    public ?PricingModelInterface $gridData = null;

    #[ORM\Column]
    #[Assert\NotNull]
    #[Assert\Length(min: 1, max: 255)]
    public ?string $name = null;

    #[ORM\Column]
    public bool $isActive = false;

    #[ORM\Column(type: JsonDocumentType::NAME)]
    #[Assert\Valid]
    public ?Advantages $advantages = null;

    public function __construct()
    {
        $this->defineUuid();

        $this->advantages ??= new Advantages();
    }
}

<?php

declare(strict_types=1);

namespace App\Business\Estimate\Entity;

use ApiPlatform\Doctrine\Orm\Filter\OrderFilter;
use ApiPlatform\Metadata as APM;
use App\Api\Filter\QFilter;
use App\Business\Estimate\Api\Processor\ProEstimateProcessor;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Location\LocationCity;
use App\Entity\Traits\CreatedAtTrait;
use App\Entity\Traits\UuidTrait;
use App\Security\Enum\CivilityEnum;
use App\Security\Enum\RoleEnum;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

/** we keep legacy naming because fuck that shit */
#[ORM\Entity]
#[ORM\Table(TableEnum::PRO_ESTIMATE)]
#[APM\GetCollection(security: 'is_granted("' . RoleEnum::SUPER_ADMIN . '")')]
#[APM\Get(security: 'is_granted("' . RoleEnum::SUPER_ADMIN . '")')]
#[APM\Post(security: 'is_granted("' . RoleEnum::PUBLIC_ACCESS . '")', processor: ProEstimateProcessor::class)]
#[APM\ApiFilter(OrderFilter::class, properties: ['createdAt' => 'DESC'])]
#[APM\ApiFilter(QFilter::class, properties: ['email', 'firstname', 'lastname'])]
class ProEstimate
{
    use UuidTrait;
    use CreatedAtTrait;

    #[ORM\Column(length: 30, nullable: true)]
    #[Assert\NotNull]
    #[Assert\Choice(choices: CivilityEnum::ALL, multiple: false)]
    public ?string $civility = null;

    #[ORM\Column]
    #[Assert\Length(max: 255)]
    #[Assert\NotNull]
    #[Assert\NotBlank]
    public ?string $firstname = null;

    #[ORM\Column]
    #[Assert\Length(max: 255)]
    #[Assert\NotBlank]
    public ?string $lastname = null;

    #[ORM\Column]
    #[Assert\Length(max: 255)]
    #[Assert\NotBlank(allowNull: true)]
    public ?string $company = null;

    #[ORM\Column(nullable: true)]
    #[Assert\Length(max: 255)]
    #[Assert\NotBlank(allowNull: true)]
    public ?string $phone = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'city_uuid', referencedColumnName: 'uuid', nullable: false, onDelete: 'CASCADE')]
    #[Assert\NotNull]
    #[APM\ApiProperty(readableLink: true, writableLink: false)]
    public ?LocationCity $city = null;

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    #[Assert\Email]
    public ?string $email = null;

    #[ORM\Column(nullable: true)]
    #[Assert\NotBlank(allowNull: true)]
    #[Assert\Length(max: 255)]
    public ?string $customMissions = null;

    #[ORM\Column]
    #[Assert\NotNull]
    public ?bool $risqueNatTechno = null;

    #[ORM\Column]
    #[Assert\NotNull]
    public ?bool $perfEnerg = null;

    #[ORM\Column]
    #[Assert\NotNull]
    public ?bool $diagTechGlobal = null;

    #[ORM\Column]
    #[Assert\NotNull]
    public ?bool $milliemesCopro = null;

    #[ORM\Column]
    #[Assert\NotNull]
    public ?bool $diagTech = null;

    #[ORM\Column]
    #[Assert\NotNull]
    public ?bool $diagAvantTravaux = null;

    #[ORM\Column]
    #[Assert\NotNull]
    public ?bool $logementDecent = null;

    #[ORM\Column]
    #[Assert\NotNull]
    public ?bool $elec = null;

    #[ORM\Column]
    #[Assert\NotNull]
    public ?bool $etatLieuEntrant = null;

    #[ORM\Column]
    #[Assert\NotNull]
    public ?bool $surfaceHabitable = null;

    #[ORM\Column]
    #[Assert\NotNull]
    public ?bool $termites = null;

    #[ORM\Column]
    #[Assert\NotNull]
    public ?bool $amiante = null;

    #[ORM\Column]
    #[Assert\NotNull]
    public ?bool $plomb = null;

    #[ORM\Column]
    #[Assert\NotNull]
    public ?bool $gaz = null;

    #[ORM\Column]
    #[Assert\NotNull]
    public ?bool $loiCarrez = null;

    #[ORM\Column]
    #[Assert\NotNull]
    public ?bool $accessHandi = null;

    #[ORM\Column]
    #[Assert\NotNull]
    public ?bool $assainissementAuto = null;

    #[ORM\Column]
    #[Assert\NotNull]
    public ?bool $etatLieu = null;

    #[ORM\Column]
    #[Assert\NotNull]
    public ?bool $merules = null;

    public function __construct()
    {
        $this->defineUuid();
    }
}

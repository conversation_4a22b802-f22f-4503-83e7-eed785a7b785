<?php

declare(strict_types=1);

namespace App\Business\Estimate\Entity;

use ApiPlatform\Doctrine\Orm\Filter\OrderFilter;
use ApiPlatform\Metadata as APM;
use App\Api\Filter\QFilter;
use App\Api\Provider\MyEstimateCollectionProvider;
use App\Business\Estimate\Api\Processor\EstimateCalculatorProcessor;
use App\Business\Estimate\Enum\BuildYearEnum;
use App\Business\Estimate\Enum\EstimateTypeEnum;
use App\Business\Estimate\Enum\PropertyTypeEnum;
use App\Business\Estimate\Enum\RoomNumberEnum;
use App\Business\Estimate\Model\AppointmentProposal;
use App\Business\Estimate\Model\EstimationOpportunity;
use App\Business\Estimate\Model\Pricing\PricingModelInterface;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Agency\Agency;
use App\Entity\Location\LocationCity;
use App\Entity\Traits\CreatedAtTrait;
use App\Entity\Traits\UuidTrait;
use App\Security\Voter\EstimateVoter;
use DateTime;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Dunglas\DoctrineJsonOdm\Type\JsonDocumentType;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\Ignore;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity]
#[ORM\Table(TableEnum::ESTIMATE)]
#[APM\GetCollection(paginationClientItemsPerPage: true, order: ['createdAt' => 'DESC'], security: 'is_granted("' . EstimateVoter::GET_COLLECTION . '")')]
#[APM\GetCollection(uriTemplate: 'my-estimates', order: ['createdAt' => 'DESC'], security: 'is_granted("' . EstimateVoter::GET_COLLECTION . '")', provider: MyEstimateCollectionProvider::class)]
#[APM\Get(security: 'is_granted("' . EstimateVoter::GET . '", object)')]
#[APM\Post(uriTemplate: '/estimates/calculate', description: 'Estimate calculation', denormalizationContext: ['groups' => [self::FIRST_ESTIMATE]], securityPostDenormalize: 'is_granted("' . EstimateVoter::POST . '", object)', processor: EstimateCalculatorProcessor::class)]
#[APM\Post(uriTemplate: '/estimates/recalculate', description: 'Estimate recalculation', denormalizationContext: ['groups' => [self::RECALCULATE_ESTIMATE]], securityPostDenormalize: 'is_granted("' . EstimateVoter::POST . '", object)', processor: EstimateCalculatorProcessor::class)]
#[APM\Post(uriTemplate: '/estimates', description: 'Estimate saving (send email + persist in DB)', denormalizationContext: ['groups' => [self::RECALCULATE_ESTIMATE, self::SAVE_ESTIMATE]], securityPostDenormalize: 'is_granted("' . EstimateVoter::POST . '", object)', processor: EstimateCalculatorProcessor::class)]
#[APM\Put(denormalizationContext: ['groups' => [self::PUT_ESTIMATE]])]
#[APM\ApiFilter(OrderFilter::class, properties: ['createdAt' => 'DESC', 'customerEmail' => 'ASC', 'customerPhone' => 'ASC'])]
#[APM\ApiFilter(QFilter::class, properties: ['customerEmail', 'customerPhone'])]
class Estimate
{
    public const string FIRST_ESTIMATE = 'estimate:first';
    public const string RECALCULATE_ESTIMATE = 'estimate:recalculate';
    public const string SAVE_ESTIMATE = 'estimate:save';
    public const string PUT_ESTIMATE = 'estimate:update';

    use UuidTrait;
    use CreatedAtTrait;

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\Choice(choices: EstimateTypeEnum::ALL)]
    #[Groups([self::FIRST_ESTIMATE, self::RECALCULATE_ESTIMATE])]
    public ?string $type = null;

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\Choice(choices: PropertyTypeEnum::ALL)]
    #[Groups([self::FIRST_ESTIMATE, self::RECALCULATE_ESTIMATE])]
    public ?string $propertyType = null;

    #[ORM\Column(nullable: true)]
    #[Assert\When(
        expression: 'this.propertyType !== "' . PropertyTypeEnum::OTHER . '"',
        constraints: [new Assert\IsNull()]
    )]
    #[Assert\When(
        expression: 'this.propertyType === "' . PropertyTypeEnum::OTHER . '"',
        constraints: [new Assert\NotBlank()]
    )]
    #[Groups([self::FIRST_ESTIMATE, self::RECALCULATE_ESTIMATE])]
    public ?string $customPropertyType = null;

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\Choice(choices: RoomNumberEnum::ALL)]
    #[Groups([self::FIRST_ESTIMATE, self::RECALCULATE_ESTIMATE])]
    public ?int $roomNumber = null;

    #[ORM\Column]
    #[Assert\When(
        expression: 'this.type !== "' . EstimateTypeEnum::AUDIT . '"',
        constraints: [new Assert\NotBlank(), new Assert\Choice(choices: BuildYearEnum::ALL)]
    )]
    // #[Assert\NotBlank]
    // #[Assert\Choice(choices: BuildYearEnum::ALL)]
    #[Groups([self::FIRST_ESTIMATE, self::RECALCULATE_ESTIMATE])]
    public ?string $buildYear = null;

    #[ORM\Column]
    #[Assert\NotNull]
    #[Groups([self::FIRST_ESTIMATE, self::RECALCULATE_ESTIMATE])]
    public ?bool $gazOlderThan15Years = null;

    #[ORM\Column]
    #[Assert\NotNull]
    #[Groups([self::FIRST_ESTIMATE, self::RECALCULATE_ESTIMATE])]
    public ?bool $electricityOlderThan15Years = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'city_uuid', referencedColumnName: 'uuid', nullable: false, onDelete: 'CASCADE')]
    #[Assert\NotNull]
    #[Groups([self::FIRST_ESTIMATE, self::RECALCULATE_ESTIMATE])]
    #[APM\ApiProperty(readableLink: true, writableLink: false)]
    public ?LocationCity $city = null;

    #[ORM\Column(type: JsonDocumentType::NAME)]
    #[Assert\IsNull(groups: [self::FIRST_ESTIMATE])]
    #[Assert\NotNull(groups: [self::RECALCULATE_ESTIMATE])]
    #[Assert\Valid(groups: [self::RECALCULATE_ESTIMATE])]
    #[Groups([self::RECALCULATE_ESTIMATE])]
    public ?EstimationOpportunity $opportunityWanted = null;

    #[ORM\Column(nullable: true)]
    #[APM\ApiProperty(writable: false)]
    public ?float $price = null;

    #[ORM\Column(nullable: true)]
    #[APM\ApiProperty(writable: false)]
    public ?float $appointmentPrice = null;

    #[ORM\Column(nullable: true)]
    #[APM\ApiProperty(writable: false)]
    public ?string $customPrice = null;

    #[ORM\Column(nullable: true)]
    #[Groups([self::RECALCULATE_ESTIMATE])]
    public ?DateTime $appointmentDate = null;

    #[ORM\Column(nullable: true)]
    public ?string $appointmentDateTextual = null;

    #[ORM\Column(length: 20, nullable: true)]
    #[Assert\Length(min: 1, max: 20)]
    #[Groups([self::FIRST_ESTIMATE, self::RECALCULATE_ESTIMATE])]
    public ?string $discountCode = null;

    #[ORM\Column(length: 200)]
    #[Assert\NotNull]
    #[Assert\Length(min: 1, max: 200)]
    #[Assert\Email]
    #[Groups([self::FIRST_ESTIMATE, self::RECALCULATE_ESTIMATE, self::PUT_ESTIMATE])]
    public ?string $customerEmail = null;

    #[ORM\Column(length: 20)]
    #[Assert\NotNull]
    #[Assert\Length(min: 1, max: 20)]
    #[Groups([self::FIRST_ESTIMATE, self::RECALCULATE_ESTIMATE, self::PUT_ESTIMATE])]
    public ?string $customerPhone = null;

    #[ORM\Column(length: 30, nullable: true)]
    #[Groups([self::FIRST_ESTIMATE, self::RECALCULATE_ESTIMATE, self::PUT_ESTIMATE])]
    public ?string $customerCivility = null;

    #[ORM\Column(nullable: true)]
    #[Groups([self::FIRST_ESTIMATE, self::RECALCULATE_ESTIMATE, self::PUT_ESTIMATE])]
    public ?string $customerFirstname = null;

    #[ORM\Column(nullable: true)]
    #[Groups([self::FIRST_ESTIMATE, self::RECALCULATE_ESTIMATE, self::PUT_ESTIMATE])]
    public ?string $customerLastname = null;

    #[ORM\Column(nullable: true)]
    #[Groups([self::FIRST_ESTIMATE, self::RECALCULATE_ESTIMATE, self::PUT_ESTIMATE])]
    public ?string $customerAddress = null;

    #[ORM\Column(nullable: true)]
    #[Groups([self::FIRST_ESTIMATE, self::RECALCULATE_ESTIMATE, self::PUT_ESTIMATE])]
    public ?string $customerZip = null;

    #[ORM\Column(nullable: true)]
    #[Groups([self::FIRST_ESTIMATE, self::RECALCULATE_ESTIMATE, self::PUT_ESTIMATE])]
    public ?string $customerCity = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    #[Groups([self::FIRST_ESTIMATE, self::RECALCULATE_ESTIMATE, self::PUT_ESTIMATE])]
    public ?string $customerCommentary = null;

    #[ORM\Column(nullable: true)]
    #[APM\ApiProperty(writable: false)]
    public ?string $emailObject = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    #[APM\ApiProperty(writable: false)]
    public ?string $emailSign = null;

    #[ORM\Column(nullable: true)]
    #[APM\ApiProperty(writable: false)]
    public ?string $execution = null;

    #[ORM\Column]
    #[APM\ApiProperty(writable: false)]
    public bool $offerConsent = false;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(name: 'agency_uuid', referencedColumnName: 'uuid', nullable: true, onDelete: 'SET NULL')]
    #[APM\ApiProperty(writable: false, readableLink: true)]
    public ?Agency $agency = null;

    #[ORM\Column(options: ['default' => 0])]
    #[APM\ApiProperty(writable: false)]
    public bool $isPaid = false;

    public ?AppointmentProposal $proposal = null;

    #[ORM\Column(type: JsonDocumentType::NAME)]
    #[Ignore]
    public ?PricingModelInterface $priceGridData = null;

    #[ORM\Column(nullable: true)]
    #[Ignore]
    #[APM\ApiProperty(writable: false)]
    public ?int $legacyId = null;


    public function __construct()
    {
        $this->defineUuid();
    }

    public function isAppointment(): bool
    {
        return null !== $this->appointmentDate;
    }

    public function unableToCalculate(): bool
    {
        return null === $this->price;
    }
}

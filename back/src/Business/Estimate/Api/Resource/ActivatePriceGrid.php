<?php declare(strict_types=1);

namespace App\Business\Estimate\Api\Resource;

use ApiPlatform\Metadata\Post;
use App\Business\Estimate\Api\Processor\ActivatePriceGridProcessor;
use App\Business\Estimate\Enum\PriceGridTypeEnum;
use App\Entity\Agency\Agency;
use App\Security\Voter\ActivatePriceGridVoter;
use Symfony\Component\Validator\Constraints as Assert;

#[Post(
    securityPostDenormalize: 'is_granted("' . ActivatePriceGridVoter::POST . '", object)',
    processor: ActivatePriceGridProcessor::class,
)]
class ActivatePriceGrid
{
    #[Assert\NotNull]
    public ?Agency $agency = null;

    #[Assert\Choice(choices: PriceGridTypeEnum::ALL)]
    public ?int $type = null;
}

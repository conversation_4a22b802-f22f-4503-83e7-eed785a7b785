<?php declare(strict_types=1);

namespace App\Business\Estimate\Api\Processor;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\Bridge\Mailer\Mailer;
use App\Business\Estimate\Entity\Estimate;
use App\Business\Estimate\EstimateOpportunity;
use App\Business\Estimate\PriceCalculator;
use Doctrine\ORM\EntityManagerInterface;

/**
 * @template-implements ProcessorInterface<Estimate, Estimate>
 */
final readonly class EstimatePutProcessor implements ProcessorInterface
{
    public function __construct(
        private EntityManagerInterface $em,
        private EstimateOpportunity $estimateOpportunity,
        private PriceCalculator $priceCalculator,
        private Mailer $mailer,
    ) {
    }

    /** @param Estimate $data */
    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = []): ?Estimate
    {
        $this->em->persist($data);
        $this->em->flush();

        // @todo send email / handle payment

        return $data;
    }
}

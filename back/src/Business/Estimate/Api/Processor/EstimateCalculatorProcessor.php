<?php declare(strict_types=1);

namespace App\Business\Estimate\Api\Processor;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\Bridge\Mailer\Mailer;
use App\Business\Estimate\AppointmentFactory;
use App\Business\Estimate\Entity\Estimate;
use App\Business\Estimate\EstimateOpportunity;
use App\Business\Estimate\Model\Pricing\EmptyGrid;
use App\Business\Estimate\PriceCalculator;
use App\Entity\Agency\Agency;
use Doctrine\ORM\EntityManagerInterface;
use function in_array;

/**
 * @template-implements ProcessorInterface<Estimate, Estimate>
 */
final readonly class EstimateCalculatorProcessor implements ProcessorInterface
{
    public function __construct(
        private EntityManagerInterface $em,
        private EstimateOpportunity $estimateOpportunity,
        private PriceCalculator $priceCalculator,
        private Mailer $mailer,
    ) {
    }

    /** @param Estimate $data */
    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = []): ?Estimate
    {
        $denormalizationContext = $operation->getDenormalizationContext();

        $data->agency = $data->city->area?->agency;

        // special case, if no agency, we can't calculate
        if (null === $data->agency) {
            return $data;
        }

        // return recommended opportunity for first estimate only
        if (
            in_array(Estimate::FIRST_ESTIMATE, $denormalizationContext['groups'] ?? [])
            && !in_array(Estimate::RECALCULATE_ESTIMATE, $denormalizationContext['groups'] ?? [])
        ) {
            $data->opportunityWanted = $this->estimateOpportunity->getOpportunityForEstimate($data);
        }

        $this->priceCalculator->calculate($data);
        $data->proposal = AppointmentFactory::createForPrices($data->price, $data->appointmentPrice);

        if (
            in_array(Estimate::SAVE_ESTIMATE, $denormalizationContext['groups'] ?? [])
            || $data->price === null
        ) {
            $data->priceGridData = $this->priceCalculator->getPriceGrid($data->city->area, $data->agency)?->gridData ?? new EmptyGrid();
            $this->saveEstimate($data);
        }

        return $data;
    }

    private function saveEstimate(Estimate $estimate): void
    {
        if (null !== $estimate->appointmentDate) {
            if (
                $estimate->appointmentDate->format('Y-m-d H:i:s') !== $estimate->proposal?->firstDate?->format('Y-m-d H:i:s')
                && $estimate->appointmentDate->format('Y-m-d H:i:s') !== $estimate->proposal?->secondDate?->format('Y-m-d H:i:s')
            ) {
                $estimate->appointmentDate = null;
                $estimate->appointmentPrice = null;
            } else {
                if ($estimate->appointmentDate->format('Y-m-d H:i:s') === $estimate->proposal->firstDate->format('Y-m-d H:i:s')) {
                    $estimate->appointmentPrice = $estimate->proposal->firstPrice;
                } elseif ($estimate->appointmentDate->format('Y-m-d H:i:s') === $estimate->proposal->secondDate->format('Y-m-d H:i:s')) {
                    $estimate->appointmentPrice = $estimate->proposal->secondPrice;
                } else {
                    $estimate->appointmentDate = null;
                    $estimate->appointmentPrice = null;
                }
            }
        }

        $this->em->persist($estimate);
        $this->em->flush();

        if ($estimate->price !== null) {
            $this->mailer->sendEstimateToClient($estimate, $estimate->customerEmail);
        }

        if ($estimate->agency?->contact?->email !== null) {
            $this->mailer->sendEstimateToAgency($estimate, $estimate->agency->contact->email);
        }
    }
}

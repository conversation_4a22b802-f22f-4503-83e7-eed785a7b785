<?php declare(strict_types=1);

namespace App\Business\Estimate\Api\Processor;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\Bridge\Mailer\Enum\MailerTypeCodeEnum;
use App\Bridge\Mailer\Mailer;
use App\Business\Estimate\Entity\ProEstimate;
use Doctrine\ORM\EntityManagerInterface;
use Throwable;

/**
 * @template-implements ProcessorInterface<ProEstimate, ProEstimate>
 */
final readonly class ProEstimateProcessor implements ProcessorInterface
{
    public function __construct(
        private EntityManagerInterface $em,
        private Mailer $mailer,
    ) {
    }

    /** @param ProEstimate $data */
    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = []): ProEstimate
    {
        $this->em->persist($data);
        $this->em->flush();

        try {
            $this->mailer->byType(MailerTypeCodeEnum::PRO_ESTIMATE, ['estimate' => $data]);
        } catch (Throwable) {
        }

        return $data;
    }
}

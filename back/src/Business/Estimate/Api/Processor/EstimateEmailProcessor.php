<?php declare(strict_types=1);

namespace App\Business\Estimate\Api\Processor;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\Bridge\Mailer\Mailer;
use App\Business\Estimate\Api\Resource\EstimateEmail;
use App\Business\Estimate\Entity\Estimate;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Uid\UuidV4;
use Throwable;

/**
 * @template-implements ProcessorInterface<EstimateEmail, EstimateEmail>
 */
final readonly class EstimateEmailProcessor implements ProcessorInterface
{
    public function __construct(
        private EntityManagerInterface $em,
        private Mailer $mailer,
    ) {
    }

    /** @param EstimateEmail $data */
    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = []): EstimateEmail
    {
        $estimate = $this->em->getRepository(Estimate::class)->find($data->uuid?->toBinary() ?? (new UuidV4())->toBinary());

        if (null === $estimate) {
            throw new NotFoundHttpException();
        }

        try {
            $this->mailer->sendEstimateToClient($estimate, $estimate->customerEmail);
        } catch (Throwable) {
            throw new BadRequestHttpException();
        }

        return $data;
    }
}

<?php

declare(strict_types=1);

namespace App\Business\Estimate\Api\Processor;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\Business\Estimate\Api\Resource\ActivatePriceGrid;
use App\Doctrine\Enum\TableEnum;
use Doctrine\ORM\EntityManagerInterface;

/**
 * @template-implements ProcessorInterface<ActivatePriceGrid, ActivatePriceGrid>
 */
final readonly class ActivatePriceGridProcessor implements ProcessorInterface
{
    public function __construct(
        private EntityManagerInterface $em,
    ) {
    }

    /** @param ActivatePriceGrid $data */
    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = []): ActivatePriceGrid
    {
        $this->em->wrapInTransaction(function () use ($data) {
            $tablePriceGrid = TableEnum::PRICE_GRID;

            $desactivateQuery = <<<SQL
            UPDATE {$tablePriceGrid}
            SET is_active = 0
            WHERE agency_uuid = ?
            SQL;

            $this->em->getConnection()->executeStatement($desactivateQuery, [$data->agency->uuid->toBinary()]);

            if (null !== $data->type) {
                $activateQuery = <<<SQL
                UPDATE {$tablePriceGrid}
                SET is_active = 1
                WHERE agency_uuid = ?
                AND type = ?
                SQL;

                $this->em->getConnection()->executeStatement($activateQuery, [$data->agency->uuid->toBinary(), $data->type]);
            }

            return true;
        });

        return $data;
    }
}

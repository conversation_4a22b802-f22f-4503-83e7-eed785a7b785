<?php declare(strict_types=1);

namespace App\Business;

use App\Entity\Upload;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use RuntimeException;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\HeaderUtils;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Mime\MimeTypeGuesserInterface;
use Symfony\Component\String\UnicodeString;
use Throwable;
use function rtrim;

final readonly class Files
{
    public function __construct(
        #[Autowire('%upload_dir%')]
        private string $dir,
        private Filesystem $fs,
        private EntityManagerInterface $em,
        private LoggerInterface $logger,
        private MimeTypeGuesserInterface $mimeTypeGuesser,
    ) {
    }

    public function createUpload(UploadedFile $file, Request $request): ?Upload
    {
        try {
            $directory = rtrim($this->dir, '/');

            $this->fs->mkdir($directory);

            $upload = new Upload();
            $upload->name = $file->getClientOriginalName();
            $upload->filename = $file->getClientOriginalName();
            $upload->type = $file->getMimeType() ?? $file->getClientMimeType();
            $upload->size = $file->getSize() ?: 0;
            $upload->alt = $request->get('alt');
            $upload->isPublic = (bool)$request->get('isPublic', false);

            ['width' => $width, 'height' => $height] = $this->calculateWidth($file->getRealPath());
            $upload->width = $width;
            $upload->height = $height;

            $file->move($directory, $upload->uuid->toRfc4122());

            $this->em->persist($upload);
            $this->em->flush();

            return $upload;
        } catch (Throwable $e) {
            $this->logger->error('Unable to create upload', ['exception' => $e]);
        }

        return null;
    }

    public function getResponse(Upload $upload, bool $isDownload = false): Response
    {
        $path = $this->getPath($upload);

        return new BinaryFileResponse($path, headers: [
            'Content-Type' => "{$upload->type}; charset=utf-8",
            'Content-Disposition' => HeaderUtils::makeDisposition(
                disposition: $isDownload ? HeaderUtils::DISPOSITION_ATTACHMENT : HeaderUtils::DISPOSITION_INLINE,
                filename: $this->cleanFilename($upload->filename),
                filenameFallback: 'agenda.jpg',
            ),
        ]);
    }

    public function getPath(Upload $upload): string
    {
        return rtrim($this->dir, '/') . '/' . $upload->uuid->toRfc4122();
    }

    public function fromUrl(string $url, ?string $name = null): ?Upload
    {
        try {
            $directory = rtrim($this->dir, '/');
            $this->fs->mkdir($directory);

            $upload = new Upload();
            $local = fopen($path = $directory . '/' . $upload->uuid->toRfc4122(), 'w');
            $remote = fopen($url, 'r');

            if (false === $local || false === $remote) {
                throw new RuntimeException('Unable to open stream');
            }

            stream_copy_to_stream($remote, $local);

            $name ??= $upload->uuid->toRfc4122();

            $upload->name = $name;
            $upload->filename = $name;
            $upload->type = $this->mimeTypeGuesser->guessMimeType($path);
            $upload->size = filesize($path) ?: 0;
            $upload->alt = $name;

            fclose($local);
            fclose($remote);

            return $upload;
        } catch (Throwable $e) {
            $this->logger->error('Unable to create upload', ['exception' => $e]);

            return null;
        }
    }

    public function calculateWidth(string $path): array
    {
        try {
            $size = getimagesize($path);
        } catch (Throwable $e) {
            $this->logger->info('Unable to open image', ['exception' => $e]);

            return ['width' => null, 'height' => null];
        }

        if (false === $size) {
            return ['width' => null, 'height' => null];
        }

        [$width, $height] = $size;

        return ['width' => $width, 'height' => $height];
    }

    private function cleanFilename(string $filename): string
    {
        return (new UnicodeString($filename))->ascii()->replace('/', '')->toString();
    }
}

<?php declare(strict_types=1);

namespace App\Business\Breadcrumb;

use App\Business\Breadcrumb\Entity\Breadcrumb;
use App\Doctrine\Enum\TableEnum;
use Doctrine\ORM\EntityManagerInterface;
use Generator;
use Psr\Log\LoggerInterface;
use Symfony\Component\Uid\Uuid;
use Throwable;
use function count;

final readonly class Breadcrumbs
{
    public function __construct(
        private EntityManagerInterface $em,
        private LoggerInterface $logger,
    ) {
    }

    public function calculate(): void
    {
        try {
            $tableContent = TableEnum::CONTENT;

            $query = <<<SQL
            SELECT BIN_TO_UUID(uuid) as uuid, name, slug, BIN_TO_UUID(parent_uuid) as parent_uuid
            FROM {$tableContent}
            SQL;

            $breadcrumbs = $this->em->getConnection()->fetchAllAssociative($query);

            $values = [];
            $params = [];
            foreach ($this->buildFrom($breadcrumbs) as $uuid => $breadcrumb) {
                $values[] = '(?, ?, ?, ?, ?, "fake", "fake")';
                $params[] = Uuid::fromRfc4122($uuid)->toBinary();
                $params[] = $breadcrumb->uuids;
                $params[] = $breadcrumb->names;
                $params[] = $breadcrumb->slugs;
                $params[] = $breadcrumb->depth;
            }

            if (0 === count($values)) {
                return;
            }

            $placeholders = implode(', ', $values);

            $query = <<<SQL
            INSERT INTO {$tableContent} (uuid, breadcrumb_uuids, breadcrumb_names, breadcrumb_slugs, breadcrumb_depth, name, slug)
            VALUES {$placeholders}
            ON DUPLICATE KEY UPDATE
            breadcrumb_uuids = VALUES(breadcrumb_uuids),
            breadcrumb_names = VALUES(breadcrumb_names),
            breadcrumb_slugs = VALUES(breadcrumb_slugs),
            breadcrumb_depth = VALUES(breadcrumb_depth)
            SQL;

            $this->em->getConnection()->executeStatement($query, $params);
        } catch (Throwable $e) {
            $this->logger->error('Unable to calculate breadcrumbs', ['exception' => $e]);
        }
    }

    /**
     * @return Generator<string, Breadcrumb>
     */
    protected function buildFrom(
        array $groups,
        int|string $parentUuid = null,
        array $ids = [],
        array $names = [],
        array $slugs = [],
    ): Generator {
        foreach ($groups as $group) {
            if ($parentUuid === $group['parent_uuid']) {
                // yield current
                yield $group['uuid'] => new Breadcrumb(
                    Breadcrumb::flatten($ids),
                    Breadcrumb::flatten($names),
                    Breadcrumb::flatten($slugs),
                    count($ids),
                );

                // yield children
                yield from $this->buildFrom(
                    $groups,
                    $group['uuid'],
                    [...$ids, $group['uuid']],
                    [...$names, $group['name']],
                    [...$slugs, $group['slug']],
                );
            }
        }
    }
}

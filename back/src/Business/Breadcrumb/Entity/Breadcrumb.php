<?php declare(strict_types=1);

namespace App\Business\Breadcrumb\Entity;

use App\Business\Breadcrumb\Model\Breadcrumb as BreadcrumbModel;
use App\Business\Breadcrumb\Model\BreadcrumbPart;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;
use function count;
use function implode;
use function str_replace;

#[ORM\Embeddable]
final class Breadcrumb
{
    public const string SEPARATOR = '/';

    public function __construct(
        #[ORM\Column(length: 1000, options: ['default' => self::SEPARATOR])]
        public string $uuids = self::SEPARATOR,
        #[ORM\Column(length: 1000, options: ['default' => self::SEPARATOR])]
        public string $names = self::SEPARATOR,
        #[ORM\Column(length: 1000, options: ['default' => self::SEPARATOR])]
        public string $slugs = self::SEPARATOR,
        #[ORM\Column(options: ['default' => 0])]
        public int $depth = 0,
    ) {
    }

    /**
     * @param array<string|int> $parts
     */
    public static function flatten(array $parts): string
    {
        return str_replace(
            self::SEPARATOR . self::SEPARATOR,
            self::SEPARATOR,
            self::SEPARATOR . implode(self::SEPARATOR, $parts) . self::SEPARATOR,
        );
    }

    public function toApiModel(): BreadcrumbModel
    {
        $model = new BreadcrumbModel(
            $this->uuids,
            $this->names,
            $this->slugs,
        );

        $uuids = array_values(array_filter(explode(self::SEPARATOR, $this->uuids)));
        $names = array_values(array_filter(explode(self::SEPARATOR, $this->names)));
        $slugs = array_values(array_filter(explode(self::SEPARATOR, $this->slugs)));

        for ($i = 0; $i < count($uuids) && $i < 10; $i++) {
            if (!isset($uuids[$i], $names[$i], $slugs[$i])) {
                continue;
            }

            $model->parts[] = new BreadcrumbPart(
                Uuid::fromRfc4122($uuids[$i]),
                $names[$i],
                $slugs[$i],
            );
        }

        return $model;
    }
}

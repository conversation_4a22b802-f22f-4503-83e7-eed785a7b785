<?php

declare(strict_types=1);

namespace App\Business\Breadcrumb\Command;

use App\Business\Breadcrumb\Breadcrumbs;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'app:breadcrumb:calculate', description: 'Recalculate breadcrumb')]
class CalculateBreadcrumbCommand extends Command
{
    public function __construct(
        private readonly Breadcrumbs $breadcrumbs,
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->breadcrumbs->calculate();

        return Command::SUCCESS;
    }
}

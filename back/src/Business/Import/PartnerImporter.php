<?php declare(strict_types=1);

namespace App\Business\Import;

use App\Business\Files;
use App\Entity\Location\LocationDepartment;
use App\Entity\Partner;
use App\Utils\CsvUtils;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use function strlen;

final class PartnerImporter
{
    private const string FILENAME = 'sinfin_partenaires.csv';

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly Files $files,
        #[Autowire('%legacy_data_dir%')]
        private readonly string $legacyDataDir,
    ) {
    }

    public function import(): void
    {
        if (!is_file($partnerPath = $this->getPath(self::FILENAME))) {
            throw new Exception('sinfin_partenaires file not found');
        }

        foreach (CsvUtils::mapAssociative($partnerPath) as $partner) {
            $upload = null;
            if (
                !empty($partner['url_logo'])
                && null !== $upload = $this->files->fromUrl($partner['url_logo'], $partner['nom_site'])
            ) {
                $this->entityManager->persist($upload);
            }

            $p = new Partner();
            $p->name = $partner['nom_site'];
            $p->link = $partner['url_site'];
            $p->activity = $partner['activite'];
            $p->upload = $upload;
            $p->department = $this->resolveDepartment($partner['departement']);

            $this->entityManager->persist($p);
        }

        $this->entityManager->flush();
    }

    private function getPath(string $filename): string
    {
        return rtrim($this->legacyDataDir, '/') . '/' . $filename;
    }

    private function resolveDepartment(string $departement): ?LocationDepartment
    {
        if ('France entière' === $departement) {
            return null;
        }

        preg_match('/\((\d+)\)/', $departement, $matches);
        $number = $matches[1] ?? '';

        if (2 !== strlen($number)) {
            $number = '0' . $number;
        }

        return $this->entityManager->getRepository(LocationDepartment::class)->findOneBy(['code' => $number]);
    }
}

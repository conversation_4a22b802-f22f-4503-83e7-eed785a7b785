<?php declare(strict_types=1);

namespace App\Business\Import;

use App\Business\Estimate\Entity\Estimate;
use App\Business\Estimate\Enum\BuildYearEnum;
use App\Business\Estimate\Enum\EstimateTypeEnum;
use App\Business\Estimate\Enum\PropertyTypeEnum;
use App\Business\Estimate\Enum\RoomNumberEnum;
use App\Business\Estimate\Model\EstimationOpportunity;
use App\Business\Estimate\Model\Pricing\EmptyGrid;
use App\Entity\Agency\Agency;
use App\Entity\Location\LocationCity;
use App\Utils\CsvUtils;
use App\Utils\JsonUtils;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Serializer\SerializerInterface;
use function in_array;

final class EstimateImporter
{
    private const string ESTIMATE_FILE = 'sinfin_devis.csv';
    private const string AGENCY_FILE = 'sinfin_cabinets.csv';

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        #[Autowire('%legacy_data_dir%')]
        private readonly string $legacyDataDir,
        #[Autowire(service: JsonUtils::ODM_SERIALIZER)]
        private SerializerInterface $serializer,
    ) {
    }

    public function import(): void
    {
        $agencies = $this->getAgencies();

        $i = 0;
        foreach (CsvUtils::mapAssociative($this->getPath(self::ESTIMATE_FILE)) as $est) {
            if (
                empty($est['cp'])
                || !in_array((int)$est['nb_pieces'], RoomNumberEnum::ALL)
            ) {
                continue;
            }

            $typeDevis = match ($est['type_devis']) {
                'Vendre' => EstimateTypeEnum::SELL,
                'Louer' => EstimateTypeEnum::RENT,
                'Audit énergétique' => EstimateTypeEnum::AUDIT,
                default => null,
            };

            if (null === $typeDevis) {
                continue;
            }

            $typeBien = match ($est['type_bien']) {
                'Maison copropriété' => PropertyTypeEnum::CO_OWNED_HOUSE,
                'Appartement' => PropertyTypeEnum::APARTMENT,
                'Maison individuelle' => PropertyTypeEnum::DETACHED_HOUSE,
                'Autre' => PropertyTypeEnum::OTHER,
                default => null,
            };

            if (null === $typeBien) {
                continue;
            }

            $buildYear = match ($est['annee_construction']) {
                'Avant 1949' => BuildYearEnum::PRE_1949,
                'Entre 1949 et 1974' => BuildYearEnum::BETWEEN_1949_1974,
                'Entre 1975 et 1977' => BuildYearEnum::BETWEEN_1975_1977,
                'Entre 1978 et 1982' => BuildYearEnum::BETWEEN_1978_1982,
                'Entre 1983 et 1988' => BuildYearEnum::BETWEEN_1983_1988,
                'Entre 1989 et 1997' => BuildYearEnum::BETWEEN_1987_1997,
                'Entre 1998 et 2000' => BuildYearEnum::BETWEEN_1998_2000,
                'Entre 1949 et 1997' => BuildYearEnum::BETWEEN_1949_1997,
                'Après 1997' => BuildYearEnum::PAST_1997,
                'Après 2000' => BuildYearEnum::PAST_2000,
                default => null,
            };

            if (null === $buildYear) {
                continue;
            }

            if (null === $city = $this->entityManager->getRepository(LocationCity::class)->findOneBy(['zip' => $est['cp'], 'name' => $est['commune']])) {
                if (null === $city = $this->entityManager->getRepository(LocationCity::class)->findOneBy(['zip' => $est['cp']])) {
                    continue;
                }
            }

            $i++;

            $estimate = new Estimate();
            $estimate->agency = $agencies[$est['cabinet_id']] ?? null;
            $estimate->city = $city;
            $estimate->type = $typeDevis;
            $estimate->propertyType = $typeBien;
            $estimate->roomNumber = (int)$est['nb_pieces'];
            $estimate->buildYear = $buildYear;
            $estimate->gazOlderThan15Years = (bool)$est['install_gaz'];
            $estimate->electricityOlderThan15Years = (bool)$est['install_elec'];
            $estimate->customerPhone = mb_strcut($est['tel'], 0, 19);
            $estimate->customerEmail = mb_strcut($est['email'] ?? '<EMAIL>', 0, 199);

            $estimate->opportunityWanted = new EstimationOpportunity();

            if (EstimateTypeEnum::AUDIT === $typeDevis) {
                $estimate->opportunityWanted->disableAllButAudit();
            } else {
                $estimate->opportunityWanted->disableAudit();
            }

            if (0 === (int)$est['plomb']) {
                $estimate->opportunityWanted->disablePlomb();
            }

            if (0 === (int)$est['amiante']) {
                $estimate->opportunityWanted->disableAmiante();
            }

            if (0 === (int)$est['elec']) {
                $estimate->opportunityWanted->disableElectricite();
            }

            if (0 === (int)$est['gaz']) {
                $estimate->opportunityWanted->disableGaz();
            }

            if (0 === (int)$est['dpe']) {
                $estimate->opportunityWanted->disableDpe();
            }

            if (0 === (int)$est['loi_carrez']) {
                $estimate->opportunityWanted->disableCarrez();
            }

            if (0 === (int)$est['mesurage']) {
                $estimate->opportunityWanted->disableMesurage();
            }

            if (0 === (int)$est['termites']) {
                $estimate->opportunityWanted->disableTermites();
            }

            if (0 === (int)$est['logement_decent']) {
                $estimate->opportunityWanted->disableDecent();
            }

            if (0 === (int)$est['etat_lieux']) {
                $estimate->opportunityWanted->disableEtatLieux();
            }

            if (0 === (int)$est['ernmt']) {
                $estimate->opportunityWanted->disableErp();
            }

            if (1 === (int)$est['assainissement']) {
                $estimate->opportunityWanted->enableAssainissement();
            }

            $estimate->price = (float)$est['prix'];
            if ($est['prix'] !== $est['prix_reduc']) {
                $estimate->appointmentPrice = (float)$est['prix_reduc'];
            }

            $estimate->customPrice = $est['prix_custom'];
            $estimate->appointmentDateTextual = $est['date_fr'];
            $estimate->discountCode = mb_strcut($est['code_avantage'], 0, 19);
            $estimate->customerLastname = $est['nom_client'];
            $estimate->customerFirstname = $est['prenom_client'];
            $estimate->customerAddress = $est['adresse_client'];
            $estimate->customerCommentary = $est['commentaire_client'];
            $estimate->emailObject = $est['objet_email'];
            $estimate->emailSign = $est['signature_email'];
            $estimate->execution = $est['execution'];
            $estimate->offerConsent = (bool)$est['offer_consent'];
            $estimate->legacyId = (int)$est['id'];
            $estimate->priceGridData = new EmptyGrid();

            $this->entityManager->persist($estimate);
            $this->entityManager->flush();

            $this->entityManager->getConnection()->executeStatement('UPDATE est_estimate SET created_at = ? WHERE uuid = ?', [
                (new DateTime())->setTimestamp((int)$est['date'])->format('Y-m-d H:i:s'),
                $estimate->uuid->toBinary(),
            ]);

            if (($i % 1000) === 0) {
                $this->entityManager->clear();
                $agencies = $this->getAgencies();
                dump('clear');
            }
            dump($i);
        }
    }

    public function getAgencies()
    {
        $agencies = [];

        foreach (CsvUtils::mapAssociative($this->getPath(self::AGENCY_FILE)) as $agency) {
            if (null !== $a = $this->entityManager->getRepository(Agency::class)->findOneBy(['legacyId' => $agency['id']])) {
                $agencies[$agency['id']] = $a;
            }
        }

        return $agencies;
    }

    private function getPath(string $filename): string
    {
        return rtrim($this->legacyDataDir, '/') . '/' . $filename;
    }
}

<?php declare(strict_types=1);

namespace App\Business\Import;

use App\Business\Estimate\Entity\PriceGrid;
use App\Business\Estimate\Enum\ActivityTypeEnum;
use App\Business\Estimate\Enum\DiscountTypeEnum;
use App\Business\Estimate\Enum\PriceGridTypeEnum;
use App\Business\Estimate\Model\Pricing\Advantage;
use App\Business\Estimate\Model\Pricing\Pricing1;
use App\Business\Estimate\Model\Pricing\Pricing1Item;
use App\Business\Estimate\Model\Pricing\Pricing2;
use App\Entity\Agency\Agency;
use App\Entity\Location\LocationArea;
use App\Utils\CsvUtils;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Generator;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use function in_array;

final class PriceImporter
{
    private const string AGENCY_FILE = 'sinfin_cabinets.csv';
    private const string AREA_FILE = 'sinfin_cantons.csv';
    private const string GRID_FILE = 'sinfin_grilles.csv';
    private const string DATA_GRID_1_FILE = 'sinfin_datas_grille1.csv';
    private const string DATA_GRID_2_FILE = 'sinfin_datas_grille2.csv';
    private const string DATA_GRID_1_TAB_FILE = 'sinfin_grille1_tab.csv';
    private const string DATA_GRID_1_PACK_FILE = 'sinfin_grille1_packs.csv';

    private const string DATA_GRID_1_PACK_TRANSAC_FILE = 'sinfin_grille1_packs_transactions.csv';

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        #[Autowire('%legacy_data_dir%')]
        private readonly string $legacyDataDir,
    ) {
    }

    public function import(): void
    {
        $agency = $this->getAgency();
        $area = $this->getArea($agency);

        $priceGrid1 = [];
        foreach ($this->generatePriceGrid1() as $id => $grid) {
            $priceGrid1[$id] = $grid;
        }

        foreach ($this->generatePriceItem1() as $gridId => $price) {
            if (isset($priceGrid1[$gridId])) {
                $priceGrid1[$gridId]->pricings[] = $price;
            }
        }

        foreach ($this->generatePackForPricing1() as $gridId => $price) {
            if (isset($priceGrid1[$gridId])) {
                $priceGrid1[$gridId]->pricings[] = $price;
            }
        }

        $priceGrid2 = [];
        foreach ($this->generatePriceGrid2() as $id => $grid) {
            $priceGrid2[$id] = $grid;
        }

        if (!is_file($gridPath = $this->getPath(self::GRID_FILE))) {
            throw new Exception('grid file not found');
        }

        $inserted = [];
        foreach (CsvUtils::mapAssociative($gridPath) as $grid) {
            if (null === $gridAgency = $agency[$grid['id_cabinet']] ?? null) {
                continue;
            }

            $gridArea = ('' === $grid['id_canton']) ? null : ($area[$grid['id_canton']] ?? false);
            if (false === $gridArea) {
                continue;
            }

            $gridType = match ($grid['type_grille'] ?? '') {
                '1' => PriceGridTypeEnum::GRID_1,
                '2' => PriceGridTypeEnum::GRID_2,
                default => null,
            };

            if (null === $gridType) {
                continue;
            }

            $pricing = match ($gridType) {
                PriceGridTypeEnum::GRID_1 => $priceGrid1[$grid['id_data_grille1']] ?? null,
                PriceGridTypeEnum::GRID_2 => $priceGrid2[$grid['id_data_grille2']] ?? null,
            };

            if (null === $pricing) {
                continue;
            }

            $priceGrid = new PriceGrid();
            $priceGrid->agency = $gridAgency;
            $priceGrid->area = $gridArea;
            $priceGrid->type = $gridType;
            $priceGrid->gridData = $pricing;
            $priceGrid->name = $grid['nom'];
            $priceGrid->isActive = (bool)$grid['active'];
            if (!empty($grid['code_avantage'])) {
                $priceGrid->advantages->add(new Advantage(
                    $grid['code_avantage'],
                    (int)$grid['valeur_code_avantage']
                ));
            }

            $key = "{$gridAgency->uuid->toRfc4122()}:{($gridArea?->uuid->toRfc4122() ?? 'null')}:{$gridType}";

            if (in_array($key, $inserted, true)) {
                continue;
            }

            $this->entityManager->persist($priceGrid);

            $inserted[] = $key;
        }

        $this->entityManager->flush();
    }

    private function getPath(string $filename): string
    {
        return rtrim($this->legacyDataDir, '/') . '/' . $filename;
    }

    private function getAgency(): array
    {
        if (!is_file($agencyPath = $this->getPath(self::AGENCY_FILE))) {
            throw new Exception('sinfin_cabinets file not found');
        }

        $agency = [];
        foreach (CsvUtils::mapAssociative($agencyPath) as $cab) {
            $agency[$cab['id']] = $this->entityManager->getRepository(Agency::class)->findOneBy(['legacyId' => $cab['id']]);
        }

        return $agency;
    }

    private function getArea(array $agency): array
    {
        if (!is_file($areaPath = $this->getPath(self::AREA_FILE))) {
            throw new Exception('sinfin_cantons file not found');
        }

        $area = [];
        foreach (CsvUtils::mapAssociative($areaPath) as $canton) {
            $area[$canton['id']] = $this->entityManager->getRepository(LocationArea::class)->findOneBy(['agency' => $agency[$canton['id_cabinet']] ?? null, 'name' => utf8_encode(utf8_decode($canton['nom']))]);
        }

        return $area;
    }

    private function generatePriceGrid1(): Generator
    {
        if (!is_file($grid1 = $this->getPath(self::DATA_GRID_1_FILE))) {
            throw new Exception('data grid file not found');
        }

        foreach (CsvUtils::mapAssociative($grid1) as $legPrice) {
            $price = new Pricing1();
            $price->discountType = ('1' === $legPrice['type']) ? DiscountTypeEnum::PERCENT : DiscountTypeEnum::FIXED;
            $price->discountFor2 = (float)$legPrice['prestations_2'];
            $price->discountFor3 = (float)$legPrice['prestations_3'];
            $price->discountFor4 = (float)$legPrice['prestations_4'];
            $price->discountFor5 = (float)$legPrice['prestations_5'];
            $price->discountFor6 = (float)$legPrice['prestations_6'];
            $price->discountFor7AndMore = (float)$legPrice['prestations_7'];
            $price->amiantePrice = (float)$legPrice['prelev_amiante'];
            $price->appointmentDiscountPercent = (int)$legPrice['reduc_rdv'];

            yield $legPrice['id'] => $price;
        }
    }

    private function generatePriceItem1(): Generator
    {
        if (!is_file($grid1 = $this->getPath(self::DATA_GRID_1_TAB_FILE))) {
            throw new Exception('sinfin tab file not found');
        }

        foreach (CsvUtils::mapAssociative($grid1) as $tab) {
            if ('0' === $tab['id_data_grille1']) {
                continue;
            }

            if (null === $type = ActivityTypeEnum::fromLegacy($tab['id_type'])) {
                continue;
            }

            $price = new Pricing1Item();
            $price->type = $type;
            $price->f1 = (float)$tab['f1'];
            $price->f2 = (float)$tab['f2'];
            $price->f3 = (float)$tab['f3'];
            $price->f4 = (float)$tab['f4'];
            $price->f5 = (float)$tab['f5'];
            $price->f6 = (float)$tab['f6'];
            $price->f7 = (float)$tab['f7'];

            $price->t2 = (float)$tab['t2'];
            $price->t3 = (float)$tab['t3'];
            $price->t4 = (float)$tab['t4'];
            $price->t5 = (float)$tab['t5'];
            $price->t6 = (float)$tab['t6'];
            $price->t7 = (float)$tab['t7'];

            yield $tab['id_data_grille1'] => $price;
        }
    }

    private function generatePackForPricing1(): Generator
    {
        if (!is_file($packActivityPath = $this->getPath(self::DATA_GRID_1_PACK_TRANSAC_FILE))) {
            throw new Exception('DATA_GRID_1_PACK_TRANSAC_FILE file not found');
        }

        $packActivity = [];
        foreach (CsvUtils::mapAssociative($packActivityPath) as $pack) {
            if (null === $type = ActivityTypeEnum::fromLegacy($pack['id_transaction'])) {
                continue;
            }

            $packActivity[$pack['id_pack']] ??= [];
            $packActivity[$pack['id_pack']][] = $type;
        }

        if (!is_file($packPath = $this->getPath(self::DATA_GRID_1_PACK_FILE))) {
            throw new Exception('DATA_GRID_1_PACK_FILE file not found');
        }

        foreach (CsvUtils::mapAssociative($packPath) as $pack) {
            if (empty($packActivity[$pack['id']])) {
                continue;
            }

            $price = new Pricing1Item();
            $price->type = ActivityTypeEnum::PACK;
            $price->types = array_unique($packActivity[$pack['id']]);
            $price->packName = $pack['libelle'];
            $price->f1 = (float)$pack['f1'];
            $price->f2 = (float)$pack['f2'];
            $price->f3 = (float)$pack['f3'];
            $price->f4 = (float)$pack['f4'];
            $price->f5 = (float)$pack['f5'];
            $price->f6 = (float)$pack['f6'];
            $price->f7 = (float)$pack['f7'];

            $price->t2 = (float)$pack['t2'];
            $price->t3 = (float)$pack['t3'];
            $price->t4 = (float)$pack['t4'];
            $price->t5 = (float)$pack['t5'];
            $price->t6 = (float)$pack['t6'];
            $price->t7 = (float)$pack['t7'];

            yield $pack['id_tarif1'] => $price;
        }
    }

    private function generatePriceGrid2(): Generator
    {
        if (!is_file($grid2 = $this->getPath(self::DATA_GRID_2_FILE))) {
            throw new Exception('data grid file not found');
        }

        foreach (CsvUtils::mapAssociative($grid2) as $legPrice) {
            $price = new Pricing2();
            $price->audit1Room = (float)$legPrice['audit_1'];
            $price->audit2Room = (float)$legPrice['audit_2'];
            $price->audit3Room = (float)$legPrice['audit_3'];
            $price->audit4Room = (float)$legPrice['audit_4'];
            $price->audit5Room = (float)$legPrice['audit_5'];
            $price->audit6Room = (float)$legPrice['audit_6'];
            $price->audit7Room = (float)$legPrice['audit_7'];
            $price->audit8Room = (float)$legPrice['audit_8'];

            $price->basePrice = (float)$legPrice['tarif'];
            $price->apartmentRatio = (float)$legPrice['coef_appart'];

            $price->priceRoom2 = (float)$legPrice['pieces_2'];
            $price->priceRoom3 = (float)$legPrice['pieces_3'];
            $price->priceRoom4 = (float)$legPrice['pieces_4'];
            $price->priceRoom5 = (float)$legPrice['pieces_5'];
            $price->priceRoom6 = (float)$legPrice['pieces_6'];
            $price->priceRoom7 = (float)$legPrice['pieces_7'];
            $price->priceRoom8 = (float)$legPrice['pieces_8'];

            $price->decrementActivity2 = (float)$legPrice['missions_2'];
            $price->decrementActivity3 = (float)$legPrice['missions_3'];
            $price->decrementActivity4 = (float)$legPrice['missions_4'];
            $price->decrementActivity5 = (float)$legPrice['missions_5'];
            $price->decrementActivity6 = (float)$legPrice['missions_6'];
            $price->decrementActivity7 = (float)$legPrice['missions_7'];
            $price->decrementActivity8 = (float)$legPrice['missions_8'];

            $price->crepRatio = (float)$legPrice['coef_crep'];
            $price->amiantePrice = (float)$legPrice['prelev_amiante'];
            $price->erpPrice = (float)$legPrice['tarif_crnmt'];
            $price->appointmentDiscount = (int)$legPrice['reduc_rdv'];

            yield $legPrice['id'] => $price;
        }
    }
}

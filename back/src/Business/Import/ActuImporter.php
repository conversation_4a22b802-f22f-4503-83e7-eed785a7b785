<?php declare(strict_types=1);

namespace App\Business\Import;

use App\Business\Files;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Agency\Agency;
use App\Utils\CsvUtils;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Uid\UuidV4;

final class ActuImporter
{
    private const string FILENAME = 'sinfin_actualites.csv';
    private const string AGENCY_FILENAME = 'sinfin_cabinets.csv';

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly Files $files,
        #[Autowire('%legacy_data_dir%')]
        private readonly string $legacyDataDir,
    ) {
    }

    public function import(): void
    {
        $agency = [];
        if (!is_file($agencyPath = $this->getPath(self::AGENCY_FILENAME))) {
            throw new Exception('actu file not found');
        }

        foreach (CsvUtils::mapAssociative($agencyPath) as $cab) {
            $agency[$cab['id']] = $this->entityManager->getRepository(Agency::class)->findOneBy(['legacyId' => $cab['id']]);
        }

        if (!is_file($actuPath = $this->getPath(self::FILENAME))) {
            throw new Exception('actu file not found');
        }

        foreach (CsvUtils::mapAssociative($actuPath) as $actu) {
            $cabinet = $agency[$actu['id_cabinet']] ?? null;

            if (null === $cabinet) {
                continue;
            }

            $upload = null;
            if (
                !empty($actu['img_url'])
                && null !== $upload = $this->files->fromUrl($actu['img_url'], $actu['img_url'])
            ) {
                $this->entityManager->persist($upload);
                $this->entityManager->flush();
            }

            $table = TableEnum::AGENCY_POST;
            $query = <<<SQL
            INSERT INTO {$table} (uuid, agency_uuid, upload_uuid, title, content, url, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, NULL)
            SQL;

            $this->entityManager->getConnection()->executeStatement($query, [
                ($uuid = new UuidV4())->toBinary(),
                $cabinet->uuid->toBinary(),
                $upload?->uuid->toBinary(),
                utf8_encode(utf8_decode($actu['titre'])),
                utf8_encode(utf8_decode($actu['texte'])),
                $uuid->toRfc4122(),
                isset($actu['created_at']) ? (new DateTime())->setTimestamp((int)$actu['created_at'])->format('Y-m-d H:i:s') : null,
            ]);
        }
    }

    private function getPath(string $filename): string
    {
        return rtrim($this->legacyDataDir, '/') . '/' . $filename;
    }
}

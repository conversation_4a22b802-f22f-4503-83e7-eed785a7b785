<?php declare(strict_types=1);

namespace App\Business\Import;

use App\Bridge\Lyra\Entity\LyraSeller;
use App\Business\Files;
use App\Entity\Agency\Agency;
use App\Entity\Agency\Model\Schedule;
use App\Entity\Agency\Model\ScheduleDay;
use App\Entity\Agency\Model\SchedulePeriod;
use App\Utils\CsvUtils;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Uid\Uuid;

final class AgencyImporter
{
    private const string AGENDA_ASSET_URL = 'https://www.agendadiagnostics.fr/assets/gestionnaire_fichiers/';
    private const string FILENAME = 'sinfin_cabinets.csv';

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly Files $files,
        #[Autowire('%legacy_data_dir%')]
        private readonly string $legacyDataDir,
    ) {
    }

    public function import(ImportStore $store): void
    {
        if (!is_file($cabinetPath = $this->getPath())) {
            throw new Exception('sinfin_cabinets.csv not found');
        }

        foreach (CsvUtils::mapAssociative($cabinetPath) as $cab) {
            $agency = new Agency();
            $agency->legacyId = (int)$cab['id'];
            $agency->name = $cab['nom'];

            if (
                !empty($cab['bandeau_url'])
                && null !== $upload = $this->files->fromUrl(self::AGENDA_ASSET_URL . $cab['bandeau_url'], $cab['bandeau_url'])
            ) {
                $this->entityManager->persist($upload);
                $agency->upload = $upload;
            }

            $agency->freeCallId = $cab['id_web_app'];

            $agency->location->city = $cab['ville'];
            $agency->location->postcode = $cab['cp'];
            $agency->location->address1 = $cab['adresse'];
            $agency->location->address2 = $cab['complement_adresse1'];
            $agency->location->latitude = (float)$cab['latitude'];
            $agency->location->longitude = (float)$cab['longitude'];

            $agency->contact->name = $cab['nom_contact'];
            $agency->contact->email = $cab['email_contact'];
            $agency->contact->phone = mb_strcut(str_replace(' ', '', $cab['tel_contact']), 0, 10);
            if (
                !empty($cab['img_url'])
                && null !== $upload = $this->files->fromUrl(self::AGENDA_ASSET_URL . $cab['img_url'], $cab['img_url'])
            ) {
                $this->entityManager->persist($upload);
                $agency->contatUpload = $upload;
            }

            $agency->certifications = $cab['certifications'];

            $agency->advantage->code = $cab['code_avantage'];
            $agency->advantage->percent = (int)$cab['valeur_code_avantage'];

            $agency->description = $cab['text_seo'];

            $agency->ratingWidget->html = $cab['widget_avis'];
            $agency->ratingWidget->js = $cab['widget_avis_js'];

            if (Uuid::isValid($cab['lyra_seller_id'] ?? '')) {
                $lyra = $this->entityManager->find(LyraSeller::class, Uuid::fromRfc4122($cab['lyra_seller_id']));
            } else {
                $lyra = null;
            }

            $agency->lyraSeller = $lyra;
            $agency->enablePaymentThreeTimeNoFee = '1' === $cab['lyra_multiple_enable'];

            $agency->schedule = $this->resolveSchedule($cab);

            $agency->displayAppointment = '1' === $cab['rdv_check'];

            $agency->meta->title = $cab['text_seo_title'];
            $agency->meta->description = mb_substr($cab['text_seo_description'] ?? '', 0, 255);

            $this->entityManager->persist($agency);

            $store->agency[$cab['id']] = $agency;
        }
    }

    private function getPath(): string
    {
        return rtrim($this->legacyDataDir, '/') . '/' . self::FILENAME;
    }

    private function resolveSchedule(array $csvData): Schedule
    {
        $schedule = new Schedule();
        $schedule->monday = $this->resolveScheduleDay($csvData['horaire_1']);
        $schedule->tuesday = $this->resolveScheduleDay($csvData['horaire_2']);
        $schedule->wednesday = $this->resolveScheduleDay($csvData['horaire_3']);
        $schedule->thursday = $this->resolveScheduleDay($csvData['horaire_4']);
        $schedule->friday = $this->resolveScheduleDay($csvData['horaire_5']);
        $schedule->saturday = $this->resolveScheduleDay($csvData['horaire_6']);
        $schedule->sunday = $this->resolveScheduleDay($csvData['horaire_7']);

        return $schedule;
    }

    private function resolveScheduleDay(string $schedule): ?ScheduleDay
    {
        if ('Fermé' === trim($schedule)) {
            return null;
        }

        $parts = explode('-', $schedule);
        if ('Fermé' === trim($parts[0])) {
            $morning = null;
        } else {
            $morningParts = explode(' à ', $parts[0]);
            $morning = new SchedulePeriod();
            $morning->openAt = new DateTime($this->cleanHours($morningParts[0]));
            $morning->closeAt = new DateTime($this->cleanHours($morningParts[1]));
        }

        if ('Fermé' === trim($parts[1])) {
            $afternoon = null;
        } else {
            $afternoonParts = explode(' à ', $parts[1]);
            $afternoon = new SchedulePeriod();
            $afternoon->openAt = new DateTime($this->cleanHours($afternoonParts[0]));
            $afternoon->closeAt = new DateTime($this->cleanHours($afternoonParts[1]));
        }

        $scheduleDay = new ScheduleDay();
        $scheduleDay->morning = $morning;
        $scheduleDay->afternoon = $afternoon;

        return $scheduleDay;
    }

    private function cleanHours(string $hours): string
    {
        $hours = trim($hours);
        $hours = str_replace(['H', 'h'], ':', $hours);

        if ('0900' === $hours) {
            $hours = '09:00';
        }

        if (!str_contains($hours, ':')) {
            $hours .= ':00';
        }

        return str_replace('.', '', $hours);
    }
}

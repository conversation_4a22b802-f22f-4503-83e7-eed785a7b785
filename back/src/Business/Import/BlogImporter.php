<?php declare(strict_types=1);

namespace App\Business\Import;

use App\Business\Enum\PostCategoryEnum;
use App\Business\Files;
use App\Doctrine\Enum\TableEnum;
use App\Utils\CsvUtils;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Uid\UuidV4;

final class BlogImporter
{
    private const string FILENAME = 'blog_article.csv';
    private const string IMAGE_FILENAME = 'blog_images.csv';
    private const string CATE_FILENAME = 'blog_cate.csv';
    private const string AGENDA_ASSET_URL = 'https://www.agendadiagnostics.fr/';

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly Files $files,
        #[Autowire('%legacy_data_dir%')]
        private readonly string $legacyDataDir,
    ) {
    }

    public function import(): void
    {
        if (!is_file($imagePath = $this->getPath(self::IMAGE_FILENAME))) {
            throw new Exception('image file not found');
        }

        $images = [];
        foreach (CsvUtils::mapAssociative($imagePath) as $image) {
            $images[$image['contentid']] = $image['value'];
        }

        if (!is_file($catePath = $this->getPath(self::CATE_FILENAME))) {
            throw new Exception('cate file not found');
        }

        $category = [];
        foreach (CsvUtils::mapAssociative($catePath) as $cate) {
            $category[$cate['contentid']] = $cate['value'];
        }

        if (!is_file($articlePath = $this->getPath(self::FILENAME))) {
            throw new Exception('blog file not found');
        }

        foreach (CsvUtils::mapAssociative($articlePath) as $article) {
            if ('1' === $article['deleted']) {
                continue;
            }

            $upload = null;
            if (
                !empty($images[$article['id']])
                && null !== $upload = $this->files->fromUrl(self::AGENDA_ASSET_URL . $images[$article['id']], $images[$article['id']])
            ) {
                $this->entityManager->persist($upload);
                $this->entityManager->flush();
            }

            $articleCategory = $this->getCategory($category, $article['id']);

            $table = TableEnum::POST;
            $query = <<<SQL
            INSERT INTO {$table} (uuid, upload_uuid, title, long_title, category, description, content, url, is_published, created_at, updated_at, seo_title, seo_description)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            SQL;

            $this->entityManager->getConnection()->executeStatement($query, [
                (new UuidV4())->toBinary(),
                $upload?->uuid->toBinary(),
                $article['pagetitle'],
                $article['longtitle'],
                $articleCategory,
                $desc = utf8_encode(utf8_decode($article['description'])),
                $article['content'],
                '/' . $article['uri'],
                $article['published'],
                isset($article['createdon']) ? (new DateTime())->setTimestamp((int)$article['createdon'])->format('Y-m-d H:i:s') : null,
                isset($article['editedon']) ? (new DateTime())->setTimestamp((int)$article['editedon'])->format('Y-m-d H:i:s') : null,
                $article['pagetitle'],
                $desc,
            ]);
        }
    }

    private function getPath(string $filename): string
    {
        return rtrim($this->legacyDataDir, '/') . '/' . $filename;
    }

    private function getCategory(array $category, string $id): string
    {
        if (isset($category[$id])) {
            $category = $category[$id];
        } else {
            $category = null;
        }

        return match ($category) {
            '336' => PostCategoryEnum::DIAGNOSTIC_DDT,
            '337' => PostCategoryEnum::DIAGNOSTIC_LOCATION,
            '338' => PostCategoryEnum::DIAGNOSTIC_AMIANTE,
            '339' => PostCategoryEnum::DIAGNOSTIC_DPE,
            '340' => PostCategoryEnum::LEGISLATION_JURISPRUDENCE,
            default => PostCategoryEnum::OTHER,
        };
    }
}

<?php declare(strict_types=1);

namespace App\Business\Import;

use App\Entity\Location\LocationArea;
use App\Utils\CsvUtils;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

final class CantonImporter
{
    private const string FILENAME = 'sinfin_cantons.csv';

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        #[Autowire('%legacy_data_dir%')]
        private readonly string $legacyDataDir,
    ) {
    }

    public function import(ImportStore $store): void
    {
        if (!is_file($cantonPath = $this->getPath())) {
            throw new Exception('canton file not found');
        }

        foreach (CsvUtils::mapAssociative($cantonPath) as $canton) {
            $locationArea = new LocationArea();
            $locationArea->name = $canton['nom'];
            if (
                0 !== ($idCab = (int)$canton['id_cabinet'])
                && isset($store->agency[$idCab])
            ) {
                $locationArea->agency = $store->agency[$idCab];
            }

            $locationArea->department = $store->department[$canton['id_departement']];

            $this->entityManager->persist($locationArea);
            $store->locationArea[$canton['id']] = $locationArea;
        }
    }

    private function getPath(): string
    {
        return rtrim($this->legacyDataDir, '/') . '/' . self::FILENAME;
    }
}

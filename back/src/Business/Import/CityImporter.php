<?php declare(strict_types=1);

namespace App\Business\Import;

use App\Entity\Location\LocationCity;
use App\Utils\CsvUtils;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

final class CityImporter
{
    private const string FILENAME = 'sinfin_communes.csv';

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        #[Autowire('%legacy_data_dir%')]
        private readonly string $legacyDataDir,
    ) {
    }

    public function import(ImportStore $store): void
    {
        if (!is_file($communePath = $this->getPath())) {
            throw new Exception('communes file not found');
        }

        foreach (CsvUtils::mapAssociative($communePath) as $commune) {
            $locationCity = new LocationCity();

            $locationCity->name = utf8_encode(utf8_decode($commune['nom']));
            $locationCity->zip = $commune['cp'];
            $locationCity->hasTermite = (bool)$commune['termites'];

            if ('0' !== $commune['id_canton']) {
                $locationCity->area = $store->locationArea[$commune['id_canton']];
            }

            $this->entityManager->persist($locationCity);
        }
    }

    private function getPath(): string
    {
        return rtrim($this->legacyDataDir, '/') . '/' . self::FILENAME;
    }
}

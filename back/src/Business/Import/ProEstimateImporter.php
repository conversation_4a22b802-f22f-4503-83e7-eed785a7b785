<?php declare(strict_types=1);

namespace App\Business\Import;

use App\Doctrine\Enum\TableEnum;
use App\Entity\Location\LocationCity;
use App\Security\Enum\CivilityEnum;
use App\Utils\CsvUtils;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Uid\UuidV4;

final class ProEstimateImporter
{
    private const string PRO_ESTIMATE_FILE = 'sinfin_devis_pro.csv';

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        #[Autowire('%legacy_data_dir%')]
        private readonly string $legacyDataDir,
    ) {
    }

    public function import(): void
    {
        if (!is_file($proEstimatePath = $this->getPath(self::PRO_ESTIMATE_FILE))) {
            throw new Exception('pro file not found');
        }

        foreach (CsvUtils::mapAssociative($proEstimatePath) as $proEstimate) {
            $city = $this->entityManager->getRepository(LocationCity::class)->findOneBy(['name' => $proEstimate['cp'], 'zip' => $proEstimate['tel']]); // not a mess, legacy table is fucked up

            if (!$city) {
                continue;
            }

            $civility = 'Monsieur' === $proEstimate['civilite'] ? CivilityEnum::MISTER : CivilityEnum::MISS;

            $tableProEstimate = TableEnum::PRO_ESTIMATE;
            $query = <<<SQL
            INSERT INTO {$tableProEstimate} (
                uuid, city_uuid, civility, firstname, lastname, company, email,
                custom_missions, risque_nat_techno, perf_energ, diag_tech_global,
                milliemes_copro, diag_tech, diag_avant_travaux, logement_decent,
                elec, etat_lieu_entrant, surface_habitable, termites, amiante,
                plomb, gaz, loi_carrez, access_handi, assainissement_auto,
                etat_lieu, merules, created_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
            )
            SQL;

            $this->entityManager->getConnection()->executeStatement($query, [
                (new UuidV4())->toBinary(),
                $city->uuid->toBinary(),
                $civility,
                $proEstimate['prenom'],
                $proEstimate['nom'],
                $proEstimate['societe'],
                $proEstimate['email'],
                $proEstimate['mission_spec'],
                $proEstimate['risque_nat_techno'],
                $proEstimate['perf_energ'],
                $proEstimate['diag_tech_global'],
                $proEstimate['milliemes_copro'],
                $proEstimate['diag_tech'],
                $proEstimate['diag_avant_travaux'],
                $proEstimate['logement_decent'],
                $proEstimate['elec'],
                $proEstimate['etat_lieu_entrant'],
                $proEstimate['surface_habitable'],
                $proEstimate['termites'],
                $proEstimate['amiante'],
                $proEstimate['plomb'],
                $proEstimate['gaz'],
                $proEstimate['loi_carrez'],
                $proEstimate['access_handi'],
                $proEstimate['assainissement_auto'],
                $proEstimate['etat_lieu'],
                $proEstimate['merlues'], // not a mess, legacy is fuckedup
                (new DateTime())->setTimestamp((int)$proEstimate['date'])->format('c'),
            ]);
        }
    }

    private function getPath(string $filename): string
    {
        return rtrim($this->legacyDataDir, '/') . '/' . $filename;
    }
}

<?php declare(strict_types=1);

namespace App\Business\Import;

use App\Business\Files;
use App\Entity\User;
use App\Security\Enum\RoleEnum;
use App\Utils\CsvUtils;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use function in_array;

final class UserImporter
{
    private const string AGENDA_ASSET_URL = 'https://www.agendadiagnostics.fr/';
    private const string FILENAME = 'modx_user_attributes.csv';
    private const string FRANCHISE = 'Franchisé';

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly Files $files,
        #[Autowire('%legacy_data_dir%')]
        private readonly string $legacyDataDir,
    ) {
    }

    public function import(ImportStore $store): void
    {
        if (!is_file($userPath = $this->getPath())) {
            throw new Exception('user file not found');
        }

        $insertedEmail = ['<EMAIL>', '<EMAIL>'];
        foreach (CsvUtils::mapAssociative($userPath) as $modUser) {
            if (in_array($modUser['email'], $insertedEmail, true)) {
                continue;
            }
            $insertedEmail[] = $modUser['email'];

            $user = new User();
            $user->password = 'unmatchable';
            $user->email = $modUser['email'];
            $user->firstname = $modUser['fullname'];
            $user->lastname = $modUser['fullname'];

            if (self::FRANCHISE === $modUser['comment']) {
                $user->role = RoleEnum::ADMIN;
            } else {
                $user->role = RoleEnum::USER;
            }

            if (
                !empty($modUser['photo'])
                && null !== $upload = $this->files->fromUrl(self::AGENDA_ASSET_URL . $modUser['photo'], $modUser['photo'])
            ) {
                $this->entityManager->persist($upload);
                $user->upload = $upload;
            }

            $this->entityManager->persist($user);
            $store->user[$modUser['internalKey']] = $user;
        }
    }

    private function getPath(): string
    {
        return rtrim($this->legacyDataDir, '/') . '/' . self::FILENAME;
    }
}

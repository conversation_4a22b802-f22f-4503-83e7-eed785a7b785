<?php declare(strict_types=1);

namespace App\Business\Import;

use App\Entity\Agency\AgencyUser;
use App\Utils\CsvUtils;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

final class CabinetUserImporter
{
    private const string FILENAME = 'sinfin_cabinet_user.csv';

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        #[Autowire('%legacy_data_dir%')]
        private readonly string $legacyDataDir,
    ) {
    }

    public function import(ImportStore $store): void
    {
        if (!is_file($cabUserPath = $this->getPath())) {
            throw new Exception('sinfin_cabinet_user file not found');
        }

        foreach (CsvUtils::mapAssociative($cabUserPath) as $cabUser) {
            if (
                !isset($store->agency[$cabUser['id_cabinet']])
                || !isset($store->user[$cabUser['id_user']])
            ) {
                continue;
            }

            $agencyUser = new AgencyUser();
            $agencyUser->agency = $store->agency[$cabUser['id_cabinet']];
            $agencyUser->user = $store->user[$cabUser['id_user']];

            $this->entityManager->persist($agencyUser);
        }
    }

    private function getPath(): string
    {
        return rtrim($this->legacyDataDir, '/') . '/' . self::FILENAME;
    }
}

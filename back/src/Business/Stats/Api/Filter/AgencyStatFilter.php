<?php declare(strict_types=1);

namespace App\Business\Stats\Api\Filter;

use ApiPlatform\Metadata\FilterInterface;
use Symfony\Component\PropertyInfo\Type;

class AgencyStatFilter implements FilterInterface
{
    public function getDescription(string $resourceClass): array
    {
        return [
            'startAt' => [
                'type' => Type::BUILTIN_TYPE_STRING,
            ],
            'endAt' => [
                'type' => Type::BUILTIN_TYPE_STRING,
            ],
        ];
    }
}

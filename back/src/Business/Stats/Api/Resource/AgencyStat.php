<?php declare(strict_types=1);

namespace App\Business\Stats\Api\Resource;

use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\GetCollection;
use App\Business\Stats\Api\Filter\AgencyStatFilter;
use App\Business\Stats\Api\Provider\AgencyStatCollectionProvider;
use App\Security\Enum\RoleEnum;
use DateTime;

#[GetCollection(
    security: 'is_granted("' . RoleEnum::ADMIN . '")',
    provider: AgencyStatCollectionProvider::class
)]
#[ApiFilter(AgencyStatFilter::class)]
class AgencyStat
{
    public readonly string $id;

    public function __construct(
        public ?DateTime $day = null,
        public ?int $estimate = null,
        public ?int $appointment = null,
    ) {
        $this->id = uniqid();
    }
}

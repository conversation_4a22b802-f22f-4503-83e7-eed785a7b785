<?php declare(strict_types=1);

namespace App\Business\Stats\Api\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Business\Stats\Api\Resource\AgencyStat;
use App\Doctrine\Enum\TableEnum;
use App\Entity\User;
use App\Security\Security;
use App\Utils\SqlUtils;
use DateInterval;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Generator;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Throwable;
use function count;

/**
 * @template-implements ProviderInterface<AgencyStat>
 */
final readonly class AgencyStatCollectionProvider implements ProviderInterface
{
    public function __construct(
        private EntityManagerInterface $em,
        private Security $security,
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): Generator
    {
        $filters = $context['filters'] ?? [];

        if (!isset($filters['startAt'], $filters['endAt'])) {
            throw new BadRequestHttpException();
        }

        $start = (new DateTime($filters['startAt']))->setTime(0, 0);
        $end = (new DateTime($filters['endAt']))->setTime(23, 59);

        $interval = $start->diff($end);

        $start = $start->sub($interval);

        do {
            if ($this->security->user->isSuperAdmin()) {
                $tableEstimate = TableEnum::ESTIMATE;
                $query = <<<SQL
                SELECT
                    DATE(created_at) AS created_at,
                    COUNT(CASE WHEN (appointment_date IS NULL OR (appointment_date_textual IS NULL AND appointment_date_textual = '')) THEN 1 END) AS devis,
                    COUNT(CASE WHEN (appointment_date IS NOT NULL OR (appointment_date_textual IS NOT NULL AND appointment_date_textual != '')) THEN 1 END) AS appointment
                FROM {$tableEstimate}
                WHERE DATE(created_at) = DATE(?)
                GROUP BY DATE(created_at)
                ORDER BY DATE(created_at) DESC
                SQL;

                $results = $this->em->getConnection()->executeQuery($query, [$start->format('c')]);
            } else {
                $agencies = $this->getAgencyForUser($this->security->user);

                if (0 === count($agencies)) {
                    return [];
                }

                $tableEstimate = TableEnum::ESTIMATE;
                $placeholders = SqlUtils::createPlaceholdersFor($agencies);

                $query = <<<SQL
                SELECT
                    DATE(created_at) AS created_at,
                    COUNT(CASE WHEN (appointment_date IS NULL OR (appointment_date_textual IS NULL AND appointment_date_textual = '')) THEN 1 END) AS devis,
                    COUNT(CASE WHEN (appointment_date IS NOT NULL OR (appointment_date_textual IS NOT NULL AND appointment_date_textual != '')) THEN 1 END) AS appointment
                FROM {$tableEstimate}
                WHERE agency_uuid IN ({$placeholders})
                AND DATE(created_at) = DATE({$start})
                GROUP BY DATE(created_at)
                ORDER BY DATE(created_at) DESC
                SQL;

                $results = $this->em->getConnection()->executeQuery($query, [...$agencies, $start->format('c')]);
            }

            if ($row = $results->fetchAssociative()) {
                yield new AgencyStat(
                    new DateTime($row['created_at']),
                    $row['devis'],
                    $row['appointment'],
                );
            } else {
                yield new AgencyStat(
                    $start,
                    0,
                    0,
                );
            }

            $start = $start->add(new DateInterval('P1D'));
        } while ($start <= $end);
    }

    private function getAgencyForUser(User $user): array
    {
        try {
            $tableAgencyUser = TableEnum::AGENCY_USER;
            $query = <<<SQL
            SELECT agency_uuid
            FROM {$tableAgencyUser}
            WHERE user_id = ?
            SQL;

            return $this->em->getConnection()->fetchFirstColumn($query, [$user->uuid->toBinary()]);
        } catch (Throwable $e) {
            return [];
        }
    }
}

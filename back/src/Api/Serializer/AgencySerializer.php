<?php declare(strict_types=1);

namespace App\Api\Serializer;

use App\Doctrine\Enum\TableEnum;
use App\Entity\Agency\Agency;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use <PERSON><PERSON>fony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\Normalizer\NormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Throwable;
use function count;
use function is_string;

/**
 * @method array getSupportedTypes(?string $format)
 */
class AgencySerializer implements NormalizerInterface, NormalizerAwareInterface
{
    private const string ALREADY_CALLED = '3e2f9a8b7c4d12f3a69e0721d4b5f7c9';

    use NormalizerAwareTrait;

    public function __construct(
        private readonly EntityManagerInterface $em,
        private readonly LoggerInterface $logger,
    ) {
    }

    /**
     * @param Agency $object
     */
    public function normalize(mixed $object, ?string $format = null, array $context = [])
    {
        $context[self::ALREADY_CALLED] = true;

        if (isset($context['request_uri']) && is_string($context['request_uri']) && str_starts_with($context['request_uri'], '/api/agencies-map')) {
            $context[AbstractNormalizer::IGNORED_ATTRIBUTES] = [
                'advantage',
                'certifications',
                'description',
                'displayAppointment',
                'displayCallIfAvailableNow',
                'enablePaymentThreeTimeNoFee',
                'freeCallId',
                'isAvailable',
                'linkToOpen',
                'lyraSeller',
                'lyraSellerOld',
                'meta',
                'ratingWidget',
            ];
        } else {
            try {
                $tablePriceGrid = TableEnum::PRICE_GRID;
                $query = <<<SQL
                SELECT DISTINCT(type)
                FROM {$tablePriceGrid}
                WHERE agency_uuid = ?
                AND area_uuid IS NULL
                AND is_active = 1
                SQL;

                $types = $this->em->getConnection()->fetchFirstColumn($query, [$object->uuid->toBinary()]);

                if (1 === count($types)) {
                    $object->priceGridType = reset($types);
                }
            } catch (Throwable $e) {
                $this->logger->error('Unable to retrieve agency grid type', [
                    'exception' => $e,
                ]);
            }
        }

        return $this->normalizer->normalize($object, $format, $context);
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof Agency && !isset($context[self::ALREADY_CALLED]);
    }
}

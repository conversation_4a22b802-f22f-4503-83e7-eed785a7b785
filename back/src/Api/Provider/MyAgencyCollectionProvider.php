<?php declare(strict_types=1);

namespace App\Api\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Agency\Agency;
use App\Security\Security;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Uid\Uuid;

/**
 * @template-implements ProviderInterface<Agency>
 */
final readonly class MyAgencyCollectionProvider implements ProviderInterface
{
    public function __construct(
        private EntityManagerInterface $em,
        private Security $security,
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): \Generator
    {
        $tableAgencyUser = TableEnum::AGENCY_USER;

        $query = <<<SQL
            SELECT agency_uuid
            FROM {$tableAgencyUser}
            WHERE user_uuid = ? 
        SQL;

        $agencies = $this->em->getConnection()->fetchFirstColumn($query, [$this->security->user->uuid->toBinary()]);

        foreach ($agencies as $agency) {
            yield $this->em->getRepository(Agency::class)->find(Uuid::fromBinary($agency));
        }
    }
}

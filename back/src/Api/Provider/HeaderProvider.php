<?php declare(strict_types=1);

namespace App\Api\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Api\Flattener;
use App\ApiResource\FlatHeader;
use App\Entity\Header\Header;
use App\Entity\Header\HeaderLink;
use App\Entity\Header\HeaderNav;
use Doctrine\ORM\EntityManagerInterface;

/**
 * @template-implements ProviderInterface<FlatHeader>
 */
final readonly class HeaderProvider implements ProviderInterface
{
    public function __construct(
        private EntityManagerInterface $em,
        private Flattener $flattener,
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): FlatHeader
    {
        // @todo improve to choose the "default" one?

        /** @var Header|null $header */
        $header = $this->em->getRepository(Header::class)->findDefault();
        if (null !== $header) {
            /** @var HeaderNav[] $navs */
            $navs = $this->em->getRepository(HeaderNav::class)->findBy(['header' => $header], ['position' => 'ASC', 'createdAt' => 'ASC']);

            /** @var HeaderLink[] $links */
            $links = $this->em->getRepository(HeaderLink::class)->findBy(['header' => $header], ['position' => 'ASC', 'createdAt' => 'ASC']);
        }

        return $this->flattener->flattenHeader(
            header: $header ?? new Header(),
            navs: $navs ?? [],
            links: $links ?? [],
        );
    }
}

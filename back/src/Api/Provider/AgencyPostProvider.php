<?php declare(strict_types=1);

namespace App\Api\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Entity\Agency\AgencyPost;
use Doctrine\ORM\EntityManagerInterface;

/**
 * @template-implements ProviderInterface<AgencyPost>
 */
final readonly class AgencyPostProvider implements ProviderInterface
{
    public function __construct(
        private EntityManagerInterface $em,
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): ?AgencyPost
    {
        if (null === ($context['filters']['slug'] ?? null)) {
            return null;
        }

        return $this->em->getRepository(AgencyPost::class)->findOneBy(['url' => $context['filters']['slug']]);
    }
}

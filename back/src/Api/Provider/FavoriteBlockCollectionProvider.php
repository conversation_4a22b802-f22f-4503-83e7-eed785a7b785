<?php declare(strict_types=1);

namespace App\Api\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\ApiResource\FlatBlock;
use App\Entity\Content\ContentBlock;
use Doctrine\ORM\EntityManagerInterface;
use Generator;

/**
 * @template-implements ProviderInterface<FlatBlock>
 */
final readonly class FavoriteBlockCollectionProvider implements ProviderInterface
{
    public function __construct(
        private EntityManagerInterface $em,
    ) {
    }

    /**
     * @return Generator<FlatBlock>
     */
    public function provide(Operation $operation, array $uriVariables = [], array $context = []): Generator
    {
        /** @var ContentBlock $block */
        foreach ($this->em->getRepository(ContentBlock::class)->getFavorites() as $block) {
            yield $block->toFlat();
        }
    }
}

<?php declare(strict_types=1);

namespace App\Api\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Entity\Post;
use Doctrine\ORM\EntityManagerInterface;

/**
 * @template-implements ProviderInterface<Post>
 */
final readonly class Post<PERSON>rovider implements ProviderInterface
{
    public function __construct(
        private EntityManagerInterface $em,
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): ?Post
    {
        if (null === ($context['filters']['slug'] ?? null)) {
            return null;
        }

        return $this->em->getRepository(Post::class)->findOneBy(['url' => $context['filters']['slug']]);
    }
}

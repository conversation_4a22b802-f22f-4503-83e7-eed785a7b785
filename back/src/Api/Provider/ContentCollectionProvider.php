<?php declare(strict_types=1);

namespace App\Api\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Api\Model\FilterHelper;
use App\ApiResource\FlatContent;
use App\Entity\Content\Content;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\QueryBuilder;
use Generator;
use Symfony\Component\Uid\Uuid;

/**
 * @template-implements ProviderInterface<FlatContent>
 */
final readonly class ContentCollectionProvider implements ProviderInterface
{
    public function __construct(
        private EntityManagerInterface $em,
        private ContentProvider $contentProvider,
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): Generator
    {
        $qb = $this->em->createQueryBuilder()->select('c')->from(Content::class, 'c')->orderBy('c.position', 'ASC');

        $filters = new FilterHelper($context['filters'] ?? []);
        if (!$filters->isEmpty()) {
            if ($parentUuid = $filters->getUuid('parent')) {
                $this->filterParent($qb, $parentUuid);
            }

            if (null !== $exist = $filters->getExists('parent')) {
                $this->filterExist($qb, 'parent', $exist);
            }

            if (null !== $status = $filters->getBool('status')) {
                $this->filterStatus($qb, $status);
            }
        }

        foreach ($qb->getQuery()->toIterable() as $content) {
            yield $this->contentProvider->provide($operation, ['uuid' => $content->uuid], $context);
        }
    }

    private function filterParent(QueryBuilder $qb, Uuid $parentUuid): void
    {
        $qb->andWhere($qb->expr()->eq('c.parent', ':parent'))->setParameter('parent', $parentUuid->toBinary());
    }

    private function filterExist(QueryBuilder $qb, string $property, bool $exist): void
    {
        $condition = $exist ? 'IS NOT NULL' : 'IS NULL';

        $qb->andWhere("c.{$property} {$condition}");
    }

    private function filterStatus(QueryBuilder $qb, bool $status): void
    {
        $qb->andWhere($qb->expr()->eq('c.status', ':status'))->setParameter('status', $status);
    }
}

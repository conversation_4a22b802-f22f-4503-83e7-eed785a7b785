<?php declare(strict_types=1);

namespace App\Api\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\Pagination\Pagination;
use ApiPlatform\State\ProviderInterface;
use App\Business\Estimate\Entity\Estimate;
use App\Contract\Uuids;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Agency\Agency;
use App\Security\Security;
use App\Utils\SqlUtils;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Uid\Uuid;

/**
 * @template-implements ProviderInterface<Agency>
 */
final readonly class MyEstimateCollectionProvider implements ProviderInterface
{
    public function __construct(
        private EntityManagerInterface $em,
        private Security $security,
        private Pagination $pagination,
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): \Generator
    {
        $tableAgencyUser = TableEnum::AGENCY_USER;

        $query = <<<SQL
            SELECT agency_uuid
            FROM {$tableAgencyUser}
            WHERE user_uuid = ? 
        SQL;

        $uuids = $this->em->getConnection()->fetchFirstColumn($query, [$this->security->user->uuid->toBinary()]);
        $p = SqlUtils::createPlaceholdersFor($uuids);

        $limit = $this->pagination->getLimit();
        $offset = 0;

        if ($context['request'] instanceof Request) {
            $limit = $context['request']->get('itemsPerPage', $limit);
            $page = $context['request']->get('page', 1);
            $offset = ($page - 1) * $limit;
        }


        $tableEstimate = TableEnum::ESTIMATE;
        $query = <<<SQL
        SELECT uuid
        FROM {$tableEstimate}
        WHERE agency_uuid IN ({$p})
        ORDER BY created_at ASC
        LIMIT {$limit} OFFSET {$offset}
        SQL;


        $uuids = $this->em->getConnection()->fetchFirstColumn($query, [...$uuids]);

        if (0 === count($uuids)) {
            return [];
        }

        yield from $this->em->getRepository(Estimate::class)->findBy(['uuid' => $uuids]);

    }
}

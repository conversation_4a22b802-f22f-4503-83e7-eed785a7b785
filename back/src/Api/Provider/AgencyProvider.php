<?php declare(strict_types=1);

namespace App\Api\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Entity\Agency\Agency;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Uid\Uuid;

/**
 * @template-implements ProviderInterface<Agency>
 */
final readonly class AgencyProvider implements ProviderInterface
{
    public function __construct(
        private EntityManagerInterface $em,
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): ?Agency
    {
        /** @var Request $request */
        $request = $context['request'] ?? null;

        $id = $request?->get('legacyId') ?? $uriVariables['legacyId'] ?? null;

        if (null === $id) {
            return null;
        }

        if (is_numeric($id)) {
            return $this->em->getRepository(Agency::class)->findOneBy(['legacyId' => $id]);
        }

        if (Uuid::isValid($id)) {
            $uuid = Uuid::fromRfc4122($id);

            return $this->em->getRepository(Agency::class)->findOneBy(['uuid' => $uuid]);
        }

        return null;
    }
}

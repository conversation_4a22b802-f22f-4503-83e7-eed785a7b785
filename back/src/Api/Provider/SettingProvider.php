<?php declare(strict_types=1);

namespace App\Api\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Entity\Setting;
use Doctrine\ORM\EntityManagerInterface;

final readonly class SettingProvider implements ProviderInterface
{
    public function __construct(
        private EntityManagerInterface $em,
    ) {
    }

    public function provide(
        Operation $operation,
        array $uriVariables = [],
        array $context = [],
    ): ?Setting {
        return $this->em->getRepository(Setting::class)->findDefault() ?: (new Setting());
    }
}

<?php declare(strict_types=1);

namespace App\Api\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Contract\Uuids;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Agency\Agency;
use Doctrine\ORM\EntityManagerInterface;
use Generator;
use Symfony\Component\HttpFoundation\Request;
use function count;

/**
 * @template-implements ProviderInterface<Agency>
 */
final readonly class AgencyMapCollectionProvider implements ProviderInterface
{
    public function __construct(
        private EntityManagerInterface $em,
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): Generator
    {
        $request = $context['request'];

        if (
            !$request instanceof Request
            || null === $request->get('search')
        ) {
            yield from $this->em->createQueryBuilder()->select('a')->from(Agency::class, 'a')->getQuery()->toIterable();

            return;
        }

        $search = $request->get('search');

        $tableAgency = TableEnum::AGENCY;
        $tableArea = TableEnum::LOCATION_AREA;
        $tableCity = TableEnum::LOCATION_CITY;

        $query = <<<SQL
        SELECT DISTINCT(agency.uuid), city.zip
        FROM {$tableCity} city
        INNER JOIN {$tableArea} area ON area.uuid = city.area_uuid
        INNER JOIN {$tableAgency} agency ON agency.uuid = area.agency_uuid
        WHERE city.zip LIKE :city_zip
        OR city.zip LIKE :city_zip_cut
        OR city.name LIKE :city_name
        ORDER BY city.zip ASC
        SQL;

        $uuids = $this->em->getConnection()->fetchFirstColumn($query, [
            'city_zip' => $search . '%',
            'city_zip_cut' => $search . '%',
            'city_name' => $search . '%',
        ]);

        if (0 === count($uuids)) {
            $uuids = $this->em->getConnection()->fetchFirstColumn($query, [
                'city_zip' => $search . '%',
                'city_zip_cut' => mb_substr($search, 0, 2) . '%',
                'city_name' => $search . '%',
            ]);
        }

        $uuids = Uuids::fromBinaries($uuids);

        yield from $this->em->createQueryBuilder()
            ->select('a')
            ->from(Agency::class, 'a')
            ->where('a.uuid IN (:uuids)')
            ->setParameter('uuids', $uuids->toBinaries())
            ->getQuery()->toIterable();
    }
}

<?php declare(strict_types=1);

namespace App\Api\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\ApiResource\FlatBlock;
use App\ApiResource\FlatContent;
use App\ApiResource\Model\FlatPage;
use App\ApiResource\Model\FlatSeo;
use App\Entity\Content\Content;
use App\Entity\Content\ContentBlock;
use App\Entity\Content\ContentPage;
use App\Entity\Content\ContentPageBlock;
use Doctrine\ORM\EntityManagerInterface;

/**
 * @template-implements ProviderInterface<FlatContent>
 */
final readonly class ContentProvider implements ProviderInterface
{
    public function __construct(
        private EntityManagerInterface $em,
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): ?FlatContent
    {
        if (isset($uriVariables['uuid'])) {
            $content = $this->em->find(Content::class, $uriVariables['uuid']);

            return $this->formatItem($content);
        }

        if (
            null !== ($slug = $uriVariables['slug'] ?? null)
            || null !== ($slug = $context['filters']['slug'] ?? null)
        ) {
            $content = $this->em->getRepository(Content::class)->getOneBySlug($slug);

            return $this->formatItem($content);
        }

        return null;
    }

    private function formatItem(?Content $content): ?FlatContent
    {
        if (null === $content) {
            return null;
        }

        $flatContent = new FlatContent();
        $flatContent->uuid = $content->uuid;
        $flatContent->name = $content->name;
        $flatContent->slug = $content->slug;
        $flatContent->breadcrumb = $content->breadcrumb->toApiModel();
        $flatContent->position = $content->position;
        $flatContent->status = $content->status;

        $flatContent->seo = new FlatSeo();
        $flatContent->seo->title = $content->seoTitle;
        $flatContent->seo->description = $content->seoDescription;

        $parent = null;
        if ($content->parent) {
            $parent = new FlatContent();
            $parent->uuid = $content->parent->uuid;
        }
        $flatContent->parent = $parent;

        $flatContent->page = $this->processPage($content->contentPage);

        return $flatContent;
    }

    private function processPage(?ContentPage $page): ?FlatPage
    {
        if (null === $page) {
            return null;
        }

        $flatPage = new FlatPage();
        $flatPage->uuid = $page->uuid;

        $blocks = $this->em->createQueryBuilder()
            ->select('b')
            ->from(ContentPageBlock::class, 'cb')
            ->innerJoin(ContentBlock::class, 'b', 'WITH', 'b.uuid = cb.block')
            ->where('cb.page = :page')
            ->setParameter('page', $page->uuid->toBinary())
            ->orderBy('cb.position', 'ASC')
            ->getQuery()
            ->getResult();

        $flatPage->blocks = array_map(function (ContentBlock $block): FlatBlock {
            $fb = new FlatBlock();
            $fb->uuid = $block->uuid;
            $fb->name = $block->name;
            $fb->type = $block->type;
            $fb->parameters = $block->parameters;

            return $fb;
        }, $blocks);

        return $flatPage;
    }
}

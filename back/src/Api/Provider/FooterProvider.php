<?php declare(strict_types=1);

namespace App\Api\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Api\Flattener;
use App\ApiResource\FlatFooter;
use App\Entity\Footer\Footer;
use App\Entity\Footer\FooterLink;
use App\Entity\Footer\FooterNav;
use App\Entity\Footer\FooterSocial;
use Doctrine\ORM\EntityManagerInterface;

/**
 * @template-implements ProviderInterface<FlatFooter>
 */
final readonly class FooterProvider implements ProviderInterface
{
    public function __construct(
        private EntityManagerInterface $em,
        private Flattener $flattener,
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): FlatFooter
    {
        // @todo improve to choose the "default" one?

        /** @var Footer|null $footer */
        $footer = $this->em->getRepository(Footer::class)->findDefault();
        if (null !== $footer) {
            /** @var FooterNav[] $navs */
            $navs = $this->em->getRepository(FooterNav::class)->findBy(['footer' => $footer], ['position' => 'ASC', 'createdAt' => 'ASC']);

            /** @var FooterLink[] $links */
            $links = $this->em->getRepository(FooterLink::class)->findBy(['footer' => $footer], ['position' => 'ASC', 'createdAt' => 'ASC']);

            /** @var FooterSocial[] $socials */
            $socials = $this->em->getRepository(FooterSocial::class)->findBy(['footer' => $footer], ['position' => 'ASC', 'createdAt' => 'ASC']);
        }

        return $this->flattener->flattenFooter(
            $footer ?? new Footer(),
            $navs ?? [],
            $links ?? [],
            $socials ?? []
        );
    }
}

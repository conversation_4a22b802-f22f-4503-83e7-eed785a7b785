<?php declare(strict_types=1);

namespace App\Api;

use ApiPlatform\Metadata\Get;
use ApiPlatform\State\ProviderInterface;
use App\Api\Provider\ContentProvider;
use App\ApiResource\FlatContent;
use App\ApiResource\FlatFooter;
use App\ApiResource\FlatHeader;
use App\ApiResource\Model\FlatHeaderLink;
use App\ApiResource\Model\FlatLink;
use App\ApiResource\Model\FlatLogo;
use App\ApiResource\Model\FlatNav;
use App\ApiResource\Model\FlatSocial;
use App\ApiResource\Model\FlatSocialLink;
use App\ApiResource\Model\HeaderLink as HeaderLinkModel;
use App\ApiResource\Model\Link;
use App\Entity\Content\Content;
use App\Entity\Footer\Footer;
use App\Entity\Footer\FooterLink;
use App\Entity\Footer\FooterNav;
use App\Entity\Footer\FooterSocial;
use App\Entity\Header\Header;
use App\Entity\Header\HeaderLink;
use App\Entity\Header\HeaderNav;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Uid\Uuid;

final readonly class Flattener
{
    public function __construct(
        #[Autowire(service: ContentProvider::class)]
        private ProviderInterface $contentProvider,
    ) {
    }

    public function flattenContent(?Content $content = null): ?FlatContent
    {
        if (null === $content) {
            return null;
        }

        $content = $this->contentProvider->provide(new Get(), ['uuid' => $content->uuid]);
        if ($content instanceof FlatContent) {
            return $content;
        }

        return null;
    }

    /**
     * @param HeaderNav[] $navs
     * @param HeaderLink[] $links
     */
    public function flattenHeader(Header $header, array $navs = [], array $links = []): FlatHeader
    {
        $flat = new FlatHeader();

        $flat->logo = new FlatLogo();
        $flat->logo->upload = $header->upload;
        $flat->logo->link = new FlatLink();
        $flat->logo->link->url = $header->url;
        $flat->logo->link->label = $header->label;
        $flat->logo->link->anchor = $header->anchor;
        $flat->logo->link->content = $this->flattenContent($header->content);

        $flat->navs = $this->buildFlatNav($navs);
        $flat->links = array_map($this->flattenHeaderLink(...), $links);

        return $flat;
    }

    public function flattenFooter(Footer $footer, array $navs = [], array $links = [], array $socials = []): FlatFooter
    {
        $flat = new FlatFooter();

        $flat->logo = new FlatLogo();
        $flat->logo->upload = $footer->upload;
        $flat->logo->link = new FlatLink();
        $flat->logo->link->url = $footer->url;
        $flat->logo->link->label = $footer->label;
        $flat->logo->link->anchor = $footer->anchor;
        $flat->logo->link->content = $this->flattenContent($footer->content);

        $flat->navs = $this->buildFlatNav($navs);
        $flat->links = array_map($this->flattenFooterLink(...), $links);
        $flat->socials = array_map($this->flattenFooterSocial(...), $socials);

        return $flat;
    }

    /** @param HeaderNav[]|FooterNav[] $navs */
    private function buildFlatNav(array $navs, Uuid $parent = null): array
    {
        $flats = [];
        foreach ($navs as $nav) {
            if ($nav->parent?->uuid === $parent) {
                $flat = new FlatNav();
                $flat->uuid = $nav->uuid;
                $flat->link = new FlatLink();
                $flat->link->uuid = $nav->uuid;
                $flat->link->url = $nav->url;
                $flat->link->label = $nav->label;
                $flat->link->anchor = $nav->anchor;
                $flat->link->content = $this->flattenContent($nav->content);
                $flat->children = $this->buildFlatNav($navs, $nav->uuid);

                $flats[] = $flat;
            }
        }

        return $flats;
    }

    private function flattenHeaderLink(HeaderLink $link): HeaderLinkModel
    {
        $flat = new HeaderLinkModel();
        $flat->uuid = $link->uuid;
        $flat->link = new FlatHeaderLink();
        $flat->link->uuid = $link->uuid;
        $flat->link->content = $this->flattenContent($link->content);
        $flat->link->url = $link->url;
        $flat->link->label = $link->label;
        $flat->link->upload = $link->upload;
        $flat->link->anchor = $link->anchor;

        return $flat;
    }

    private function flattenFooterLink(FooterLink $link): Link
    {
        $flat = new Link();
        $flat->uuid = $link->uuid;
        $flat->link = new FlatLink();
        $flat->link->uuid = $link->uuid;
        $flat->link->content = $this->flattenContent($link->content);
        $flat->link->url = $link->url;
        $flat->link->label = $link->label;
        $flat->link->anchor = $link->anchor;

        return $flat;
    }

    private function flattenFooterSocial(FooterSocial $social): FlatSocial
    {
        $flat = new FlatSocial();

        $flat->uuid = $social->uuid;
        $flat->link = new FlatSocialLink();
        $flat->link->url = $social->url;
        $flat->link->label = $social->label;
        $flat->link->upload = $social->upload;
        $flat->link->anchor = $social->anchor;

        return $flat;
    }
}

<?php declare(strict_types=1);

namespace App\Api\Model;

use App\Utils\IriUtils;
use ArrayAccess;
use Symfony\Component\Uid\Uuid;
use function count;
use function ctype_digit;
use function in_array;
use function is_array;
use function is_bool;
use function is_int;
use function is_scalar;
use function is_string;
use function preg_match;

/**
 * @implements ArrayAccess<string, mixed>
 */
class FilterHelper implements ArrayAccess
{
    public function __construct(
        public array $data = [],
    ) {
    }

    public function __get(string $name): mixed
    {
        return $this->get($name);
    }

    public function __set(string $name, mixed $value): void
    {
        $this->set($name, $value);
    }

    public function __isset(string $name): bool
    {
        return $this->offsetExists($name);
    }

    public static function toId(mixed $value): ?int
    {
        return IriUtils::toId($value);
    }

    public static function toUuid(mixed $value): ?Uuid
    {
        return IriUtils::toUuid($value);
    }

    public static function toBool(mixed $data): ?bool
    {
        if (is_bool($data)) {
            return $data;
        }
        if (is_int($data)) {
            return 0 !== $data;
        }
        if (is_string($data)) {
            $data = mb_strtolower($data);
            if (in_array($data, ['1', 'on', 'true', 'y', 'yes', 'oui', 'o'], true)) {
                return true;
            }
            if (in_array($data, ['0', 'off', 'false', 'n', 'no', 'non', 'n'], true)) {
                return false;
            }
        }

        return null;
    }

    public static function toInt(mixed $data): ?int
    {
        if (is_int($data)) {
            return $data;
        }
        if (is_string($data) && ctype_digit($data)) {
            return (int)$data;
        }

        return null;
    }

    public function count(array $except = []): int
    {
        $count = 0;
        foreach ($this->data as $key => $value) {
            if (null !== $value && !in_array($key, $except, true)) {
                $count++;
            }
        }

        return $count;
    }

    public function get(string $offset): mixed
    {
        $m = [];
        if (preg_match('/([^[]+)\[([^]]+)\]/i', $offset, $m)) {
            if (null !== $array = $this->getArray($m[1])) {
                return $array[$m[2]] ?? null;
            }
        }

        return $this->data[$offset] ?? null;
    }

    public function set(string $offset, mixed $value): self
    {
        $this->data[$offset] = $value;

        return $this;
    }

    public function getInt(string $offset): ?int
    {
        return self::toInt($this->get($offset));
    }

    public function getArray(string $offset, bool $keepEmptyArray = false): ?array
    {
        if (null === $data = $this->get($offset)) {
            return null;
        }
        if (is_array($data)) {
            if (!$keepEmptyArray && 0 === count($data)) {
                return null;
            }

            return $data;
        }
        if (is_scalar($data)) {
            return [$data];
        }

        return null;
    }

    public function getBool(string $offset, ?bool $default = null): ?bool
    {
        if (null === $data = $this->get($offset)) {
            return $default;
        }

        return (null === $value = self::toBool($data)) ? $default : $value;
    }

    public function getId(string $offset): ?int
    {
        if (is_int($id = self::toId($this->get($offset)))) {
            return $id;
        }

        return null;
    }

    public function getUuid(string $offset): ?Uuid
    {
        return self::toUuid($this->get($offset));
    }

    public function getExists(string $property, ?bool $default = null): ?bool
    {
        if (!isset($this->getArray('exists')[$property])) {
            return $default;
        }

        return (null === $value = self::toBool($this->getArray('exists')[$property])) ? $default : $value;
    }

    public function offsetExists(mixed $offset): bool
    {
        return isset($this->data[$offset]);
    }

    public function offsetGet(mixed $offset): mixed
    {
        return $this->data[$offset] ?? null;
    }

    public function offsetSet(mixed $offset, mixed $value): void
    {
        $this->data[$offset] = $value;
    }

    public function offsetUnset(mixed $offset): void
    {
        unset($this->data[$offset]);
    }

    public function isEmpty(): bool
    {
        return 0 === count($this->data);
    }
}

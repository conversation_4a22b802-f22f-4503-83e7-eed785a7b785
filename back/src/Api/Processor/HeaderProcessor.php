<?php declare(strict_types=1);

namespace App\Api\Processor;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\ApiResource\FlatContent;
use App\ApiResource\FlatHeader;
use App\Entity\Content\Content;
use App\Entity\Header\Header;
use App\Entity\Header\HeaderLink;
use App\Entity\Header\HeaderNav;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Symfony\Component\Uid\Uuid;
use Throwable;
use function count;

/**
 * @template-implements ProcessorInterface<FlatHeader, FlatHeader>
 */
final readonly class HeaderProcessor implements ProcessorInterface
{
    public function __construct(
        private EntityManagerInterface $em,
        private LoggerInterface $logger,
    ) {
    }

    /** @param FlatHeader $data */
    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = []): ?FlatHeader
    {
        try {
            $this->em->getConnection()->transactional(function () use ($data): void {
                // header
                $header = $this->em->getRepository(Header::class)->findDefault() ?? new Header();
                // $header->logo = $data->logo;
                $header->upload = $data->logo->upload;
                $header->url = $data->logo->link->url;
                $header->label = $data->logo->link->label;
                $header->anchor = $data->logo->link->anchor;
                $header->content = $this->getContentFromFlat($data->logo->link->content);
                $this->em->persist($header);

                // nav
                $navUuids = $this->persistNavs($data->navs, $header);
                $this->cleanOldRelatedEntities(HeaderNav::class, $header, $navUuids);

                // links
                $linkUuids = [];
                foreach ($data->links as $i => $linkData) {
                    $uuid = $linkData->uuid ?? Uuid::v4();
                    $linkUuids[] = $uuid;

                    $link = $this->em->getRepository(HeaderLink::class)->find($uuid) ?? new HeaderLink();
                    $link->uuid = $uuid;
                    $link->url = $linkData->link->url;
                    $link->label = $linkData->link->label;
                    $link->anchor = $linkData->link->anchor;
                    $link->content = $this->getContentFromFlat($linkData->link->content);
                    $link->upload = $linkData->link->upload;
                    $link->position = $i;
                    $link->header = $header;

                    $this->em->persist($link);
                }
                $this->cleanOldRelatedEntities(HeaderLink::class, $header, $linkUuids);

                // save
                $this->em->flush();
            });

            return $data;
        } catch (Throwable $e) {
            $this->logger->error('Unable to process header', [
                'exception' => $e,
            ]);
        }

        throw new UnprocessableEntityHttpException('Unable to process header.');
    }

    private function cleanOldRelatedEntities(string $relatedClass, Header $header, array $uuidsToPreserve): void
    {
        $this->em->createQueryBuilder()
            ->delete($relatedClass, 'e')
            ->where('e.header = :header')
            ->andWhere('e.uuid NOT IN (:uuids)')
            ->setParameter('header', $header->uuid->toBinary())
            ->setParameter('uuids', array_map(fn (Uuid $uuid) => $uuid->toBinary(), $uuidsToPreserve))
            ->getQuery()
            ->execute();
    }

    /**
     * @return array array of all uuids persisted during recursive save
     */
    private function persistNavs(array $navs, Header $header, ?HeaderNav $parent = null): array
    {
        $navUuids = [];
        foreach ($navs as $i => $navData) {
            $uuid = $navData->uuid ?? Uuid::v4();
            $navUuids[] = $uuid;

            $nav = $this->em->getRepository(HeaderNav::class)->find($uuid) ?? new HeaderNav();
            $nav->uuid = $uuid;
            $nav->url = $navData->link->url;
            $nav->label = $navData->link->label;
            $nav->anchor = $navData->link->anchor;
            $nav->content = $this->getContentFromFlat($navData->link->content);
            $nav->position = $i;
            $nav->header = $header;
            $nav->parent = $parent;

            $this->em->persist($nav);

            if (isset($navData->children) && 0 !== count($navData->children)) {
                $navUuids = [
                    ...$navUuids,
                    ...$this->persistNavs($navData->children, $header, $nav),
                ];
            }
        }

        return $navUuids;
    }

    private function getContentFromFlat(?FlatContent $flat): ?Content
    {
        if (null === $flat) {
            return null;
        }

        return $this->em->find(Content::class, $flat->uuid);
    }
}

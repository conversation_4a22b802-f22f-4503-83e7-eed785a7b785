<?php declare(strict_types=1);

namespace App\Api\Processor;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\Entity\Setting;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Throwable;

/**
 * @template-implements ProcessorInterface<Setting, Setting>
 */
final readonly class SettingProcessor implements ProcessorInterface
{
    public function __construct(
        private EntityManagerInterface $em,
        private LoggerInterface $logger,
    ) {
    }

    /** @param Setting $data */
    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = []): ?Setting
    {
        try {
            $this->em->getConnection()->transactional(function () use ($data): void {
                $setting = $this->em->getRepository(Setting::class)->findDefault() ?? new Setting();
                $setting->tagManager = $data->tagManager;
                $setting->searchLink = $data->searchLink;
                $setting->quoteLink = $data->quoteLink;
                $setting->html = $data->html;
                $setting->js = $data->js;

                $this->em->persist($setting);
                $this->em->flush();
            });

            return $data;
        } catch (Throwable $e) {
            $this->logger->error('Unable to process setting', [
                'exception' => $e,
            ]);
        }

        throw new UnprocessableEntityHttpException('Unable to process setting.');
    }
}

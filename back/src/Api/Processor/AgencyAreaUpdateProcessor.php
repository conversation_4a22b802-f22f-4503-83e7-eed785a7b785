<?php declare(strict_types=1);

namespace App\Api\Processor;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\Api\Input\AgencyUpdateAreaInput;
use App\Bridge\Mailer\Mailer;
use App\Contract\Uuids;
use App\Doctrine\Enum\TableEnum;
use App\Entity\Agency\Agency;
use App\Utils\SqlUtils;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * @template-implements ProcessorInterface<AgencyUpdateAreaInput, AgencyAreaUpdateProcessor>
 */
final readonly class AgencyAreaUpdateProcessor implements ProcessorInterface
{
    public function __construct(
        private EntityManagerInterface $em,
        private Mailer $mailer,
        private LoggerInterface $logger,
    ) {
    }

    /** @param AgencyUpdateAreaInput $data */
    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = []): ?Agency
    {
        $this->em->wrapInTransaction(function (EntityManagerInterface $em) use ($data): void {
            $tableLocationArea = TableEnum::LOCATION_AREA;
            $query = <<<SQL
            UPDATE {$tableLocationArea}
            SET agency_uuid = NULL
            WHERE agency_uuid = ?
            SQL;

            $em->getConnection()->executeStatement($query, [$data->agency->uuid->toBinary()]);

            $uuids = new Uuids();
            foreach ($data->areas as $area) {
                $uuids->add($area->uuid);
            }

            if ($uuids->isEmpty()) {
                return;
            }

            $placeholder = SqlUtils::createPlaceholders($uuids->count());

            $query = <<<SQL
            UPDATE {$tableLocationArea}
            SET agency_uuid = ?
            WHERE uuid IN ({$placeholder})
            SQL;

            $em->getConnection()->executeStatement($query, [$data->agency->uuid->toBinary(), ...$uuids->toBinaries()]);
        });

        return $data->agency;
    }
}

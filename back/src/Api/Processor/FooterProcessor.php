<?php declare(strict_types=1);

namespace App\Api\Processor;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\ApiResource\FlatContent;
use App\ApiResource\FlatFooter;
use App\Entity\Content\Content;
use App\Entity\Footer\Footer;
use App\Entity\Footer\FooterLink;
use App\Entity\Footer\FooterNav;
use App\Entity\Footer\FooterSocial;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Symfony\Component\Uid\Uuid;
use Throwable;
use function count;

/**
 * @template-implements ProcessorInterface<FlatFooter, FlatFooter>
 */
final readonly class FooterProcessor implements ProcessorInterface
{
    public function __construct(
        private EntityManagerInterface $em,
        private LoggerInterface $logger,
    ) {
    }

    /** @param FlatFooter $data */
    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = []): ?FlatFooter
    {
        try {
            $this->em->getConnection()->transactional(function () use ($data): void {
                // footer
                $footer = $this->em->getRepository(Footer::class)->findDefault() ?? new Footer();
                $footer->upload = $data->logo->upload;
                $footer->url = $data->logo->link->url;
                $footer->label = $data->logo->link->label;
                $footer->anchor = $data->logo->link->anchor;
                $footer->content = $this->getContentFromFlat($data->logo->link->content);
                $this->em->persist($footer);

                // nav
                $navUuids = $this->persistNavs($data->navs, $footer);
                $this->cleanOldRelatedEntities(FooterNav::class, $footer, $navUuids);

                // links
                $linkUuids = [];
                foreach ($data->links as $i => $linkData) {
                    $uuid = $linkData->uuid ?? Uuid::v4();
                    $linkUuids[] = $uuid;

                    $link = $this->em->getRepository(FooterLink::class)->find($uuid) ?? new FooterLink();
                    $link->uuid = $uuid;
                    $link->url = $linkData->link->url;
                    $link->label = $linkData->link->label;
                    $link->anchor = $linkData->link->anchor;
                    $link->content = $this->getContentFromFlat($linkData->link->content);
                    $link->position = $i;
                    $link->footer = $footer;

                    $this->em->persist($link);
                }
                $this->cleanOldRelatedEntities(FooterLink::class, $footer, $linkUuids);

                // socials
                $socialUuids = [];
                foreach ($data->socials as $socialData) {
                    $uuid = $socialData->uuid ?? Uuid::v4();
                    $socialUuids[] = $uuid;

                    $social = $this->em->getRepository(FooterSocial::class)->find($uuid) ?? new FooterSocial();
                    $social->uuid = $uuid;
                    $social->url = $socialData->link?->url;
                    $social->label = $socialData->link?->label;
                    $social->anchor = $socialData->link?->anchor;
                    $social->upload = $socialData->link?->upload;
                    $social->footer = $footer;
                    $this->em->persist($social);
                }
                $this->cleanOldRelatedEntities(FooterSocial::class, $footer, $socialUuids);

                // save
                $this->em->flush();
            });

            return $data;
        } catch (Throwable $e) {
            $this->logger->error('Unable to process footer', [
                'exception' => $e,
            ]);
        }

        throw new UnprocessableEntityHttpException('Unable to process footer.');
    }

    private function cleanOldRelatedEntities(string $relatedClass, Footer $footer, array $uuidsToPreserve): void
    {
        $this->em->createQueryBuilder()
            ->delete($relatedClass, 'e')
            ->where('e.footer = :footer')
            ->andWhere('e.uuid NOT IN (:uuids)')
            ->setParameter('footer', $footer->uuid->toBinary())
            ->setParameter('uuids', array_map(fn (Uuid $uuid) => $uuid->toBinary(), $uuidsToPreserve))
            ->getQuery()
            ->execute();
    }

    /**
     * @return array array of all uuids persisted during recursive save
     */
    private function persistNavs(array $navs, Footer $footer, ?FooterNav $parent = null): array
    {
        $navUuids = [];
        foreach ($navs as $i => $navData) {
            $uuid = $navData->uuid ?? Uuid::v4();
            $navUuids[] = $uuid;

            $nav = $this->em->getRepository(FooterNav::class)->find($uuid) ?? new FooterNav();
            $nav->uuid = $uuid;
            $nav->url = $navData->link->url;
            $nav->label = $navData->link->label;
            $nav->anchor = $navData->link->anchor;
            $nav->content = $this->getContentFromFlat($navData->link->content);
            $nav->position = $i;
            $nav->footer = $footer;
            $nav->parent = $parent;

            $this->em->persist($nav);

            if (isset($navData->children) && 0 !== count($navData->children)) {
                $navUuids = [
                    ...$navUuids,
                    ...$this->persistNavs($navData->children, $footer, $nav),
                ];
            }
        }

        return $navUuids;
    }

    private function getContentFromFlat(?FlatContent $flat): ?Content
    {
        if (null === $flat) {
            return null;
        }

        return $this->em->find(Content::class, $flat->uuid);
    }
}

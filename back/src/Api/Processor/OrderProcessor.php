<?php declare(strict_types=1);

namespace App\Api\Processor;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\ApiResource\ContentOrder;
use App\Doctrine\Enum\TableEnum;
use App\Utils\SqlUtils;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Throwable;
use function implode;

final readonly class OrderProcessor implements ProcessorInterface
{
    public function __construct(
        private EntityManagerInterface $em,
        private LoggerInterface $logger,
    ) {
    }

    /** @param ContentOrder $data */
    public function process(
        mixed $data,
        Operation $operation,
        array $uriVariables = [],
        array $context = [],
    ): ContentOrder {
        try {
            $table = TableEnum::CONTENT;
            $when = [];
            $in = [];
            $params = [];
            foreach ($data->orders as $position) {
                $when[] = 'WHEN c.uuid = ? THEN ?';
                $params[] = $position->uuid->toBinary();
                $params[] = $position->position;
                $in[] = $position->uuid->toBinary();
            }

            $whenFlat = implode(' ', $when);
            $inPlaceholder = SqlUtils::createPlaceholdersFor($in);

            $query = <<<SQL
            UPDATE {$table} c
            SET c.position = (CASE {$whenFlat} ELSE c.position END)
            WHERE c.uuid IN ({$inPlaceholder})
            SQL;

            $this->em->getConnection()->executeStatement($query, [...$params, ...$in]);
        } catch (Throwable $e) {
            $this->logger->error('cannot save positions', [$e]);
        }

        $data->id = time();

        return $data;
    }
}

<?php declare(strict_types=1);

namespace App\Api\Processor;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\Bridge\Mailer\Enum\MailerTypeCodeEnum;
use App\Bridge\Mailer\Mailer;
use App\Entity\JobApplication;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Throwable;

/**
 * @template-implements ProcessorInterface<JobApplication, JobApplication>
 */
final readonly class JobApplicationProcessor implements ProcessorInterface
{
    public function __construct(
        private EntityManagerInterface $em,
        private Mailer $mailer,
        private LoggerInterface $logger,
        private UrlGeneratorInterface $urlGenerator,
    ) {
    }

    /** @param JobApplication $data */
    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = []): ?JobApplication
    {
        $this->em->persist($data);
        $this->em->flush();

        try {
            $arrayValues = $data->toArray();
            $arrayValues['applicantEmail'] = $data->email;
            unset($arrayValues['email']);

            $emailTo = [];
            if (null !== $data->jobOffer?->agency?->contact?->email) {
                $emailTo = [$data->jobOffer->agency->contact->email];
            }

            if (null !== $data->agency?->contact?->email) {
                $emailTo[] = $data->agency->contact->email;
            }

            $arrayValues['cv_url'] = $this->urlGenerator->generate('_api_/uploads/{uuid}/binary_get', ['uuid' => $data->cv?->uuid->toRfc4122()], UrlGeneratorInterface::ABSOLUTE_URL);
            $arrayValues['coverLetter_url'] = $this->urlGenerator->generate('_api_/uploads/{uuid}/binary_get', ['uuid' => $data->coverLetter?->uuid->toRfc4122()], UrlGeneratorInterface::ABSOLUTE_URL);

            $this->mailer->byType(MailerTypeCodeEnum::JOB_APPLICATION, $arrayValues, $emailTo);
        } catch (Throwable $exception) {
            $this->logger->error('Unable to send job application email', ['exception' => $exception]);
        }

        return $data;
    }
}

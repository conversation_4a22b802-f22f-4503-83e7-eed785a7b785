<?php declare(strict_types=1);

namespace App\Api\Processor;

use ApiPlatform\Metadata\Delete;
use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\ApiResource\FlatBlock;
use App\ApiResource\FlatContent;
use App\ApiResource\Model\FlatPage;
use App\Business\Breadcrumb\Breadcrumbs;
use App\Entity\Content\Content;
use App\Entity\Content\ContentBlock;
use App\Entity\Content\ContentPage;
use App\Entity\Content\ContentPageBlock;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\String\Slugger\AsciiSlugger;
use Symfony\Component\Uid\Uuid;
use function count;
use function is_string;

/**
 * @todo refactor, it's crappy
 * @template-implements ProcessorInterface<FlatContent, FlatContent>
 */
final readonly class ContentProcessor implements ProcessorInterface
{
    public function __construct(
        private EntityManagerInterface $em,
        private Breadcrumbs $breadcrumbs,
    ) {
    }

    /** @param FlatContent $data */
    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = []): ?FlatContent
    {
        if ($operation instanceof Delete) {
            $content = $this->em->find(Content::class, $uriVariables['uuid']);

            if (null === $content) {
                throw new NotFoundHttpException();
            }

            $this->em->remove($content);
            $this->em->flush();

            return null;
        }

        if (null !== $data->uuid) {
            $content = $this->em->find(Content::class, $data->uuid) ?? new Content();
            $content->uuid = $data->uuid;
        } else {
            $content = new Content();
            $data->uuid = $content->uuid;
        }

        if (null === $data->parent?->uuid) {
            $content->parent = null;
        } else {
            $content->parent = $this->em->find(Content::class, $data->parent->uuid);
        }

        $content->slug = $this->slugify($data);
        $content->name = $data->name;
        $content->seoTitle = $data->seo?->title;
        $content->seoDescription = $data->seo?->description;
        $content->position = $data->position;
        $content->status = $data->status;
        $contentPage = $this->handleContentPage($data->page);

        // remove the old ContentPage
        if (null !== $content->contentPage && $content->contentPage !== $contentPage) {
            $this->em->remove($content->contentPage);
        }
        $content->contentPage = $contentPage;
        $this->em->persist($content);

        $this->handleContentPageBlock($data->page?->blocks, $content);

        $this->em->flush();

        $this->em->getRepository(ContentBlock::class)->cleanOrphans();
        $this->breadcrumbs->calculate();

        return $data;
    }

    private function handleContentPage(?FlatPage $page): ?ContentPage
    {
        if (null === $page) {
            return null;
        }

        if (null !== $page->uuid) {
            $contentPage = $this->em->find(ContentPage::class, $page->uuid) ?? new ContentPage();
            $contentPage->uuid = $page->uuid;
        } else {
            $contentPage = new ContentPage();
        }

        $this->em->persist($contentPage);

        return $contentPage;
    }

    private function handleContentPageBlock(?array $blocks, Content $content): void
    {
        // if content page is null, already cleaned by cascade
        if (null === $content->contentPage) {
            return;
        }

        // clean all old related block to the current page
        if (null === $blocks || 0 === count($blocks)) {
            $this->cleanAssociatedBlocks($content->contentPage, []);

            return;
        }

        $uuidsToPreserve = [];
        /* @var FlatBlock $block */
        foreach ($blocks as $i => $flatBlock) {
            if (null !== $flatBlock->uuid) {
                $block = $this->em->find(ContentBlock::class, $flatBlock->uuid) ?? new ContentBlock();
                $block->uuid = $flatBlock->uuid;
            } else {
                $block = new ContentBlock();
            }

            $uuidsToPreserve[] = $block->uuid;
            $block->name = $flatBlock->name;
            $block->type = $flatBlock->type;
            $block->parameters = $flatBlock->parameters;

            $association = $this->em->getRepository(ContentPageBlock::class)->findOneBy(['page' => $content->contentPage, 'block' => $block]) ?? new ContentPageBlock();
            $association->block = $block;
            $association->page = $content->contentPage;
            $association->position = $i;

            $this->em->persist($block);
            $this->em->persist($association);
        }

        $this->cleanAssociatedBlocks($content->contentPage, $uuidsToPreserve);
    }

    private function cleanAssociatedBlocks(ContentPage $contentPage, array $uuidsToPreserve): void
    {
        $qb = $this->em->createQueryBuilder()
            ->delete(ContentPageBlock::class, 'cpb')
            ->where('cpb.page = :contentPage')
            ->setParameter('contentPage', $contentPage->uuid->toBinary());

        if (0 !== count($uuidsToPreserve)) {
            $qb
                ->andWhere('cpb.block NOT IN (:uuidsToPreserve)')
                ->setParameter('uuidsToPreserve', array_map(
                    fn(Uuid $uuid) => $uuid->toBinary(), $uuidsToPreserve,
                ));
        }

        $qb->getQuery()->execute();
    }

    // uniqueness is assured by UniqueSlugValidator if client set it, uniqid appended otherwise
    private function slugify(FlatContent $data): string
    {
        if (is_string($data->slug)) {
            return $data->slug;
        }

        return (new AsciiSlugger())->slug($data->name)->lower()->toString() . '-' . uniqid();
    }
}

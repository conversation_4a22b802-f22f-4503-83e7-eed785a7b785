<?php declare(strict_types=1);

namespace App\Api\Processor;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\Api\Model\LinkUserAgency;
use App\Doctrine\Enum\TableEnum;
use Doctrine\ORM\EntityManagerInterface;
use <PERSON>ymfony\Component\Uid\UuidV4;

/**
 * @template-implements ProcessorInterface<LinkUserAgency, LinkUserAgencyProcessor>
 */
final readonly class LinkUserAgencyProcessor implements ProcessorInterface
{
    public function __construct(
        private EntityManagerInterface $em,
    ) {
    }

    /** @param LinkUserAgency $data */
    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = []): ?LinkUserAgency
    {
        $this->em->wrapInTransaction(function (EntityManagerInterface $em) use ($data): void {
            $tableAgencyUser = TableEnum::AGENCY_USER;
            $query = <<<SQL
            DELETE FROM {$tableAgencyUser} where user_uuid = ?
            SQL;

            $em->getConnection()->executeStatement($query, [$data->user->uuid->toBinary()]);

            foreach ($data->agencies as $agency) {
                $query = <<<SQL
                INSERT INTO {$tableAgencyUser} (uuid, user_uuid, agency_uuid) VALUES (?, ?, ?)
                SQL;

                $em->getConnection()->executeStatement($query, [(new UuidV4())->toBinary(), $data->user->uuid->toBinary(), $agency->uuid->toBinary()]);
            }
        });

        return $data;
    }
}

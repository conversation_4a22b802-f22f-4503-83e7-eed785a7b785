<?php declare(strict_types=1);

namespace App\Api\PropertyAccessor;

use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\PersistentCollection;
use Symfony\Component\PropertyAccess\Exception\AccessException;
use Symfony\Component\PropertyAccess\Exception\InvalidArgumentException;
use Symfony\Component\PropertyAccess\Exception\UnexpectedTypeException;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\PropertyAccess\PropertyPathInterface;
use function is_array;
use function is_object;
use function is_string;
use function property_exists;

readonly class CollectionPropertyAccessor implements PropertyAccessorInterface
{
    public function __construct(
        private PropertyAccessorInterface $decorated,
    ) {
    }

    /**
     * @throws InvalidArgumentException
     * @throws AccessException
     * @throws UnexpectedTypeException
     */
    public function setValue(object|array &$objectOrArray, string|PropertyPathInterface $propertyPath, mixed $value): void
    {
        if (
            is_array($value)
            && is_object($objectOrArray)
            && is_string($propertyPath)
            && property_exists($objectOrArray, $propertyPath)
            && (null !== $target = $this->getValue($objectOrArray, $propertyPath))
            && $target instanceof Collection
        ) {
            // detect what's missing
            $removes = [];
            foreach ($target as $entity) {
                $isIn = false;
                foreach ($value as $item) {
                    $property = 'id'; // id by default

                    if (property_exists($entity, 'code')) {
                        $property = 'code';
                    }

                    if (property_exists($entity, 'uuid')) {
                        $property = 'uuid';
                    }

                    if ($entity->{$property} === $item->{$property}) {
                        $isIn = $target->contains($item);
                    }
                }

                if (!$isIn) {
                    $removes[] = $entity;
                }
            }

            // remove from target
            foreach ($removes as $entity) {
                $target->removeElement($entity);

                // deal with bidirectionnal stuff
                if ($target instanceof PersistentCollection) {
                    $mapping = $target->getMapping();
                    if (isset($mapping['mappedBy'])) {
                        $this->decorated->setValue($entity, $mapping['mappedBy'], null);
                    }
                }
            }

            // add what's new
            foreach ($value as $entity) {
                if (!$target->contains($entity)) {
                    $target->add($entity);
                }

                // deal with bidirectionnal stuff
                if ($target instanceof PersistentCollection) {
                    $mapping = $target->getMapping();
                    if (isset($mapping['mappedBy'])) {
                        $this->decorated->setValue($entity, $mapping['mappedBy'], $target->getOwner());
                    }
                }
            }
        } else {
            $this->decorated->setValue($objectOrArray, $propertyPath, $value);
        }
    }

    /**
     * @throws InvalidArgumentException
     * @throws AccessException
     * @throws UnexpectedTypeException
     */
    public function getValue(object|array $objectOrArray, string|PropertyPathInterface $propertyPath): mixed
    {
        return $this->decorated->getValue($objectOrArray, $propertyPath);
    }

    /**
     * @throws InvalidArgumentException
     */
    public function isWritable(object|array $objectOrArray, string|PropertyPathInterface $propertyPath): bool
    {
        return $this->decorated->isWritable($objectOrArray, $propertyPath);
    }

    /**
     * @throws InvalidArgumentException
     */
    public function isReadable(object|array $objectOrArray, string|PropertyPathInterface $propertyPath): bool
    {
        return $this->decorated->isReadable($objectOrArray, $propertyPath);
    }
}

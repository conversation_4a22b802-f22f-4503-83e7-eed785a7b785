<?php declare(strict_types=1);

namespace App\Api\Action;

use App\Business\Files;
use App\Entity\Upload;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Uid\Uuid;

#[AsController]
final readonly class UploadGetBinaryAction
{
    public function __construct(
        private EntityManagerInterface $em,
        private Files $files,
    ) {
    }

    // @todo see why api platform can't autowire Upload
    public function __invoke(Uuid $uuid, Request $request): Response
    {
        if (null === $upload = $this->em->find(Upload::class, $uuid)) {
            throw new NotFoundHttpException();
        }

        return $this->files->getResponse($upload, $request->query->getBoolean('download'));
    }
}

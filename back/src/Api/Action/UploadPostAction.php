<?php

declare(strict_types=1);

namespace App\Api\Action;

use App\Business\Files;
use App\Entity\Upload;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

#[AsController]
final readonly class UploadPostAction
{
    public function __construct(
        private Files $files,
    ) {
    }

    public function __invoke(Request $request): ?Upload
    {
        if (null === $file = $request->files->get('file')) {
            throw new UnprocessableEntityHttpException();
        }
        if (null === $upload = $this->files->createUpload($file, $request)) {
            throw new UnprocessableEntityHttpException();
        }

        return $upload;
    }
}

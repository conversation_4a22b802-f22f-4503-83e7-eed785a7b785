<?php declare(strict_types=1);

namespace App\Api\Filter;

use ApiPlatform\Doctrine\Orm\Filter\AbstractFilter;
use ApiPlatform\Doctrine\Orm\Util\QueryNameGeneratorInterface;
use ApiPlatform\Metadata\Operation;
use App\Utils\ArrUtils;
use Doctrine\ORM\QueryBuilder;
use Symfony\Component\PropertyInfo\Type;
use function array_keys;
use function explode;
use function implode;
use function in_array;
use function mb_strcut;
use function mb_strpos;
use function trim;
use function uniqid;

class QFilter extends AbstractFilter
{
    private const string PROPERTY = 'q';

    public function getDescription(string $resourceClass): array
    {
        return [
            self::PROPERTY => [
                'property' => implode(', ', array_keys($this->getProperties() ?? [])),
                'type' => Type::BUILTIN_TYPE_STRING,
                'required' => false,
                'is_collection' => false,
            ],
            self::PROPERTY . '[]' => [
                'property' => implode(', ', array_keys($this->getProperties() ?? [])),
                'type' => Type::BUILTIN_TYPE_STRING,
                'required' => false,
                'is_collection' => true,
            ],
        ];
    }

    public static function applyQFilter(
        QueryBuilder $qb,
        string $q,
        array $properties,
    ): void {
        $q = trim($q);
        if ('' !== $q) {
            $root = $qb->getRootAliases()[0];

            $words = ArrUtils::vfu(explode(' ', $q));
            foreach ($words as $word) {
                $parameter = uniqid(self::PROPERTY);

                $or = $qb->expr()->orX();
                foreach ($properties as $property) {
                    if (self::hasValidAlias($qb, $property)) {
                        $or->add($qb->expr()->like($property, ":{$parameter}"));
                    } else {
                        $or->add($qb->expr()->like("{$root}.{$property}", ":{$parameter}"));
                    }
                }

                $qb
                    ->andWhere($or)
                    ->setParameter($parameter, "%{$word}%");
            }
        }
    }

    protected function filterProperty(
        string $property,
        mixed $value,
        QueryBuilder $queryBuilder,
        QueryNameGeneratorInterface $queryNameGenerator,
        string $resourceClass,
        ?Operation $operation = null,
        array $context = [],
    ): void {
        if (self::PROPERTY !== $property) {
            return;
        }

        self::applyQFilter($queryBuilder, $value, array_keys($this->getProperties() ?? []));
    }

    private static function hasValidAlias(QueryBuilder $qb, string $property): bool
    {
        if (false === $i = mb_strpos($property, '.')) {
            return false;
        }

        return in_array(
            mb_strcut($property, 0, $i),
            $qb->getAllAliases(),
            true,
        );
    }
}

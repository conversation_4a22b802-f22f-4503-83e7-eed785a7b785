<?php declare(strict_types=1);

namespace App\Api\Filter;

use ApiPlatform\Metadata\FilterInterface;
use Symfony\Component\PropertyInfo\Type;

class ContentFilter implements FilterInterface
{
    public function getDescription(string $resourceClass): array
    {
        return [
            'parent' => [
                'type' => 'iri',
            ],
            'exists[parent]' => [
                'type' => Type::BUILTIN_TYPE_BOOL,
                'description' => 'Get content only with/without parent',
            ],
            'status' => [
                'type' => Type::BUILTIN_TYPE_BOOL,
                'description' => 'Get content only active/inactive',
            ],
        ];
    }
}

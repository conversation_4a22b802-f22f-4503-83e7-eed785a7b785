<?php declare(strict_types=1);

namespace App\Api\Filter;

use ApiPlatform\Doctrine\Orm\Filter\AbstractFilter;
use ApiPlatform\Doctrine\Orm\Util\QueryNameGeneratorInterface;
use ApiPlatform\Metadata\Operation;
use Doctrine\ORM\QueryBuilder;
use Symfony\Component\PropertyInfo\Type;
use Symfony\Component\Uid\Uuid;
use function in_array;
use function is_array;
use function is_string;
use function str_contains;

class UuidFilter extends AbstractFilter
{
    public function getDescription(string $resourceClass): array
    {
        $description = [];
        foreach (array_keys($this->getProperties()) as $property) {
            if (is_string($property)) {
                $description[$property] = [
                    'type' => Type::BUILTIN_TYPE_STRING,
                ];
            }
        }

        return $description;
    }

    protected function filterProperty(
        string $property,
        mixed $value,
        QueryBuilder $queryBuilder,
        QueryNameGeneratorInterface $queryNameGenerator,
        string $resourceClass,
        ?Operation $operation = null,
        array $context = [],
    ): void {
        if (in_array($property, array_keys($this->getProperties()), true)) {
            if (is_array($value)) {
                $uuids = [];
                foreach ($value as $v) {
                    if (str_starts_with($v, '/api/')) {
                        $v = explode('/', $v);
                        $v = end($v);
                    }

                    if (Uuid::isValid($v)) {
                        $uuids[] = Uuid::fromRfc4122($v)->toBinary();
                    }
                }

                if (empty($uuids)) {
                    $queryBuilder->andWhere('1 = 0');

                    return;
                }

                $root = $queryBuilder->getRootAliases()[0];

                if (str_contains($property, '.')) {
                    [$associationKey, $associationProperty] = explode('.', $property, 2);

                    $associationKeyAlias = $queryNameGenerator->generateJoinAlias($associationKey);
                    $parameterName = $queryNameGenerator->generateParameterName($associationProperty);

                    $queryBuilder
                        ->leftJoin("{$root}.{$associationKey}", $associationKeyAlias)
                        ->andWhere($queryBuilder->expr()->in("{$associationKeyAlias}.{$associationProperty}", ":{$parameterName}"))
                        ->setParameter($parameterName, $uuids);
                } else {
                    $parameterName = $queryNameGenerator->generateParameterName($property);

                    $queryBuilder
                        ->andWhere($queryBuilder->expr()->in("{$root}.{$property}", ":{$parameterName}"))
                        ->setParameter($parameterName, $uuids);
                }

                return;
            }

            if (str_starts_with($value, '/api/')) {
                $value = explode('/', $value);
                $value = end($value);
            }

            if (Uuid::isValid($value)) {
                $uuid = Uuid::fromRfc4122($value);

                $root = $queryBuilder->getRootAliases()[0];

                if (str_contains($property, '.')) {
                    [$associationKey, $associationProperty] = explode('.', $property, 2);

                    $associationKeyAlias = $queryNameGenerator->generateJoinAlias($associationKey);
                    $parameterName = $queryNameGenerator->generateParameterName($associationProperty);

                    $queryBuilder
                        ->leftJoin("{$root}.{$associationKey}", $associationKeyAlias)
                        ->andWhere($queryBuilder->expr()->eq("{$associationKeyAlias}.{$associationProperty}", ":{$parameterName}"))
                        ->setParameter($parameterName, $uuid->toBinary());
                } else {
                    $parameterName = $queryNameGenerator->generateParameterName($property);

                    $queryBuilder
                        ->andWhere($queryBuilder->expr()->eq("{$root}.{$property}", ":{$parameterName}"))
                        ->setParameter($parameterName, $uuid->toBinary());
                }
            } else {
                $queryBuilder->andWhere('0 = 1');
            }
        }
    }
}

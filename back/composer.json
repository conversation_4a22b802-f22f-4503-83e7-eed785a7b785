{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.3", "ext-ctype": "*", "ext-curl": "*", "ext-iconv": "*", "ext-mbstring": "*", "api-platform/doctrine-orm": "^4.0", "api-platform/symfony": "^4.0", "doctrine/dbal": "^3", "doctrine/doctrine-bundle": "^2.13", "doctrine/doctrine-migrations-bundle": "^3.3", "doctrine/orm": "^2.16", "dunglas/doctrine-json-odm": "^1.4", "nelmio/cors-bundle": "^2.5", "phpdocumentor/reflection-docblock": "^5.4", "phpoffice/phpspreadsheet": "^3.7", "phpstan/phpdoc-parser": "^1.32", "sentry/sentry-symfony": "^5.0", "symfony/asset": "6.4.*", "symfony/console": "6.4.*", "symfony/dotenv": "6.4.*", "symfony/expression-language": "6.4.*", "symfony/flex": "^2", "symfony/framework-bundle": "6.4.*", "symfony/http-client": "6.4.*", "symfony/mailchimp-mailer": "6.4.*", "symfony/mailer": "6.4.*", "symfony/mime": "6.4.*", "symfony/monolog-bundle": "^3.10", "symfony/property-access": "6.4.*", "symfony/property-info": "6.4.*", "symfony/rate-limiter": "6.4.*", "symfony/runtime": "6.4.*", "symfony/security-bundle": "6.4.*", "symfony/serializer": "6.4.*", "symfony/translation": "6.4.*", "symfony/twig-bundle": "6.4.*", "symfony/validator": "6.4.*", "symfony/yaml": "6.4.*", "twig/cssinliner-extra": "^3.16", "twig/extra-bundle": "^3.16", "twig/intl-extra": "^3.17"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true, "phpstan/extension-installer": true}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "6.4.*"}}, "require-dev": {"phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^1.12", "phpstan/phpstan-doctrine": "^1.5", "phpstan/phpstan-symfony": "^1.4", "symfony/debug-bundle": "6.4.*", "symfony/stopwatch": "6.4.*", "symfony/web-profiler-bundle": "6.4.*"}}
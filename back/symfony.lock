{"api-platform/symfony": {"version": "4.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.0", "ref": "e9952e9f393c2d048f10a78f272cd35e807d972b"}, "files": ["config/packages/api_platform.yaml", "config/routes/api_platform.yaml", "src/ApiResource/.gitignore"]}, "doctrine/doctrine-bundle": {"version": "2.13", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.12", "ref": "7266981c201efbbe02ae53c87f8bb378e3f825ae"}, "files": ["config/packages/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "1d01ec03c6ecbd67c3375c5478c9a423ae5d6a33"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "dunglas/doctrine-json-odm": {"version": "1.4", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "0.1", "ref": "c2ab78f625df0c89af5908d50a28602ff8c4919f"}}, "nelmio/cors-bundle": {"version": "2.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.5", "ref": "6bea22e6c564fba3a1391615cada1437d0bde39c"}, "files": ["config/packages/nelmio_cors.yaml"]}, "phpstan/phpstan": {"version": "1.12", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "5e490cc197fb6bb1ae22e5abbc531ddc633b6767"}, "files": ["phpstan.dist.neon"]}, "sentry/sentry-symfony": {"version": "5.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "5.0", "ref": "76afa07d23e76f678942f00af5a6a417ba0816d0"}, "files": ["config/packages/sentry.yaml"]}, "symfony/console": {"version": "7.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "1781ff40d8a17d87cf53f8d4cf0c8346ed2bb461"}, "files": ["bin/console"]}, "symfony/debug-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "5aa8aa48234c8eb6dbdd7b3cd5d791485d2cec4b"}, "files": ["config/packages/debug.yaml"]}, "symfony/flex": {"version": "2.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "146251ae39e06a95be0fe3d13c807bcf3938b172"}, "files": [".env"]}, "symfony/framework-bundle": {"version": "7.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "6356c19b9ae08e7763e4ba2d9ae63043efc75db5"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/mailchimp-mailer": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.4", "ref": "1be490e068472fc87e226cf54bc95fb6f2e49117"}}, "symfony/mailer": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.3", "ref": "df66ee1f226c46f01e85c29c2f7acce0596ba35a"}, "files": ["config/packages/mailer.yaml"]}, "symfony/monolog-bundle": {"version": "3.10", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.7", "ref": "aff23899c4440dd995907613c1dd709b6f59503f"}, "files": ["config/packages/monolog.yaml"]}, "symfony/routing": {"version": "7.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "21b72649d5622d8f7da329ffb5afb232a023619d"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/security-bundle": {"version": "7.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "2ae08430db28c8eb4476605894296c82a642028f"}, "files": ["config/packages/security.yaml", "config/routes/security.yaml"]}, "symfony/translation": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "e28e27f53663cc34f0be2837aba18e3a1bef8e7b"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/twig-bundle": {"version": "7.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "cab5fd2a13a45c266d45a7d9337e28dee6272877"}, "files": ["config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/uid": {"version": "7.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "0df5844274d871b37fc3816c57a768ffc60a43a5"}}, "symfony/validator": {"version": "7.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "8c1c4e28d26a124b0bb273f537ca8ce443472bfd"}, "files": ["config/packages/validator.yaml"]}, "symfony/web-profiler-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.1", "ref": "e42b3f0177df239add25373083a564e5ead4e13a"}, "files": ["config/packages/web_profiler.yaml", "config/routes/web_profiler.yaml"]}, "twig/extra-bundle": {"version": "v3.16.0"}}
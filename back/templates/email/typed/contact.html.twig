{%  extends 'email/layout.html.twig' %}

{% block body %}
    <div class="header">
        {% if TYPE is same as 'contact' %}
            <h1>Contact</h1>
        {% endif %}
        {% if TYPE is same as 'info' %}
            <h1>Demande d'informations</h1>
        {% endif %}
        {% if TYPE is same as 'newsletter' %}
            <h1>Inscriptions newsletter</h1>
        {% endif %}
        {% if TYPE is same as 'big-account' %}
            <h1>Grand comptes</h1>
        {% endif %}
        {% if TYPE is same as 'diag-mag' %}
            <h1>Demande de guides DiagMag</h1>
            <p>Un internaute a fait une demande d’outils AGENDA, nous vous proposons de prendre rapidement contact avec lui.</p>
        {% endif %}
        {% if TYPE is same as 'diag-assist' %}
            <h1>Demande de DiagAssist</h1>
            <p>Un internaute a fait une demande d’outils AGENDA, nous vous proposons de prendre rapidement contact avec lui.</p>
        {% endif %}
    </div>

    <h3>Informations générales</h3>
    <ul>
        <li><strong>Nom: </strong>{{ lastname }}</li>
        <li><strong>Prénom: </strong>{{ firstname }}</li>
        {% if civility is defined and civility is not null  %}
            <li><strong>Civilité: </strong>{{ civility }}</li>
        {% endif %}
        <li><strong>email: </strong>{{ contactEmail }}</li>
        <li><strong>Téléphone: </strong>{{ phone }}</li>
        {% if fax is defined and fax is not null  %}
            <li><strong>Fax: </strong>{{ fax }}</li>
        {% endif %}
        {% if address is defined and address is not null  %}
            <li><strong>Adresse: </strong>{{ address }}</li>
        {% endif %}
        {% if zip is defined and zip is not null  %}
            <li><strong>Code postal: </strong>{{ zip }}</li>
        {% endif %}
        {% if city is defined and city is not null  %}
            <li><strong>Ville: </strong>{{ city }}</li>
        {% endif %}
        {% if company is defined and company is not null  %}
            <li><strong>Entreprise: </strong>{{ company }}</li>
        {% endif %}
        {% if activity is defined and activity is not null  %}
            <li><strong>Activité: </strong>{{ activity }}</li>
        {% endif %}
    </ul>

    {% if TYPE is same as 'diag-mag' %}
        <h4>DiagMag demandés:</h4>
        <ul>
            <li><strong>Nombre de DiagMag Vente Location: </strong>{{ nbrSellRent ?? 'Non renseigné' }}</li>
            <li><strong>Nombre de DiagMag Copropriété: </strong>{{ nbrCopro ?? 'Non renseigné' }}</li>
            <li><strong>Nombre de DiagMag Travaux: </strong>{{ nbrTravaux ?? 'Non renseigné' }}</li>
        </ul>
    {% endif %}

    {% if agency is defined and agency is not null  %}
        <p><strong>Vous trouverez ci-dessous les informations de votre cabinet :</strong></p>
        <p>
            <strong>Agence: </strong>{{ agency.name }} <br>
            <strong>Téléphone: </strong>{{ agency.contact.phone ?? '' }} <br>
            <strong>Email: </strong>{{ agency.contact.email ?? '' }} <br>
            <strong>Adresse: </strong> {{ agency.location.address1  ?? ''}} {{ agency.location.city ?? ''}} {{ agency.location.postcode  ?? ''}}
        </p>

    {% endif %}

    {% if message is defined and message is not null  %}
        <h3>Message:</h3>
        <p>{{ message }}</p>
    {% endif %}


    <div class="footer">
        <p style="background-color: #0d5aa7; padding: 10px; text-align: center">
            <a href="https://agendadiagnostics.fr" style="color: white">https://agendadiagnostics.fr</a>
        </p>
    </div>

{% endblock %}

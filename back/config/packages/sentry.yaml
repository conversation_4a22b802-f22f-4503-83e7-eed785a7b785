when@prod:
  sentry:
    dsn: '%env(SENTRY_DSN)%'
    register_error_listener: false
    register_error_handler: false
    options:
      server_name: '%env(SERVER_NAME)%'
      ignore_exceptions:
        - 'Symfony\Component\ErrorHandler\Error\FatalError'
        - 'Symfony\Component\Debug\Exception\FatalErrorException'
        - 'Symfony\Component\HttpKernel\Exception\NotFoundHttpException'

  monolog:
    handlers:
      sentry:
        type: sentry
        level: !php/const Monolog\Logger::ERROR
        hub_id: Sentry\State\HubInterface
        fill_extra_context: true

security:
  password_hashers:
    Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: auto
  providers:
    user_provider:
      id: App\Security\UserLoader
  firewalls:
    dev:
      pattern: ^/(_(profiler|wdt)|css|images|js)/
      security: false
    api_token:
      pattern: ^/api/tokens(?:\.json|\.jsonld)?$
      methods: [ POST ]
      provider: user_provider
      stateless: true
      json_login:
        check_path: !php/const App\Security\Api\Resource\Token::TOKEN_OPERATION
        username_path: email
        password_path: password
      login_throttling:
        max_attempts: 5 # max attempts by minute
    api:
      pattern: ^/api/
      provider: user_provider
      stateless: true
      custom_authenticators:
        - App\Security\Authenticator\TokenAuthenticator
      user_checker: App\Security\UserLoader
    main:
      lazy: true
      provider: user_provider
  role_hierarchy:
    !php/const App\Security\Enum\RoleEnum::SUPER_ADMIN: !php/const App\Security\Enum\RoleEnum::ADMIN
    !php/const App\Security\Enum\RoleEnum::ADMIN: !php/const App\Security\Enum\RoleEnum::USER
  access_control:
    - { path: ^/, roles: !php/const App\Security\Enum\RoleEnum::PUBLIC_ACCESS }

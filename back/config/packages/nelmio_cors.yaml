nelmio_cors:
  defaults:
    allow_credentials: false
    allow_origin: [ ]
    allow_headers: [ 'Content-Type', 'Authorization' ]
    allow_methods: [ ]
    expose_headers: [ 'Content-Disposition', 'Content-Length', 'Link', 'X-Sinfin-Token' ]
    max_age: 0
    hosts: [ ]
    origin_regex: false
    forced_allow_origin_value: ~
  paths:
    '^/api':
      allow_origin: [ '*' ]
      allow_headers: [ '*' ]
      allow_methods: [ 'OPTIONS', 'GET', 'POST', 'PUT', 'PATCH', 'DELETE' ]
      expose_headers: [ 'Content-Disposition', 'Content-Length', 'Link', 'X-Sinfin-Token' ]
      max_age: 3600
    '^/': ~
api_platform:
  title: AgendaDiagnostics
  version: 1.0.0
  name_converter: ~
  path_segment_name_generator: api_platform.metadata.path_segment_name_generator.dash
  defaults:
    stateless: true
    cache_headers:
      vary: [ 'Content-Type', 'Authorization', 'Origin' ]
    normalization_context:
      !php/const Symfony\Component\Serializer\Normalizer\AbstractObjectNormalizer::SKIP_NULL_VALUES: false
      !php/const Symfony\Component\Serializer\Normalizer\AbstractObjectNormalizer::PRESERVE_EMPTY_OBJECTS: true
  show_webby: false
  use_symfony_listeners: true
  mapping:
    paths:
      - '%kernel.project_dir%/src/Entity'
      - '%kernel.project_dir%/src/ApiResource'
      - '%kernel.project_dir%/src/Bridge/Lyra/Entity'
      - '%kernel.project_dir%/src/Business/Estimate/Entity'
      - '%kernel.project_dir%/src/Business/Estimate/Api/Resource'
      - '%kernel.project_dir%/src/Business/Stats/Api/Resource'
      - '%kernel.project_dir%/src/Security/Api/Resource'
      - '%kernel.project_dir%/src/Bridge/Mailer/Entity'
      - '%kernel.project_dir%/src/Bridge/Mailer/Api/Resource'
      - '%kernel.project_dir%/src/Bridge/Excel/Api/Resource'

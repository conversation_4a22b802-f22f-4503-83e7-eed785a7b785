parameters:
  upload_dir: '%kernel.project_dir%/private/Upload/'
  legacy_data_dir: '%kernel.project_dir%/legacy/'
  excel_dir: '%kernel.project_dir%/private/excel/'

services:
  _defaults:
    autowire: true
    autoconfigure: true

  App\:
    resource: '../src/'
    exclude:
      - '../src/DependencyInjection/'
      - '../src/Entity/'
      - '../src/Kernel.php'

  App\Api\PropertyAccessor\CollectionPropertyAccessor:
    decorates: 'property_accessor'
    arguments:
      $decorated: '@App\Api\PropertyAccessor\CollectionPropertyAccessor.inner'
    autoconfigure: false
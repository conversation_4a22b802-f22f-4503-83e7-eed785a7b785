body {
    --gutter: 2rem;
}
.container {
    max-width: 600px;
    margin: 1rem auto;
    background: #ffffff;
    padding: var(--gutter);
    border: 1px solid #c9cbcc7f;
}
.d-flex {
    display: flex;
}
.justify-content-center {
    justify-content: center;
}
.my-3 {
    margin-top: var(--gutter);
    margin-bottom: var(--gutter);
}
.mb-3 {
    margin-bottom: var(--gutter);
}
.text-center {
    text-align: center;
}
.fw-bold {
    font-weight: bold;
}
h1 {
    font-family: Helvetica, serif;
    margin: 0 0 var(--gutter);
}
hr {
    margin-top: var(--gutter);
    margin-bottom: var(--gutter);
    border: 1px solid #c9cbcc7f;
}
.font-helvetica {
    font-family: Helvetica, serif;
}

body {
    font-family: Arial, sans-serif;
}
.header {
    text-align: center;
    padding-bottom: 4px;
    border-bottom: 2px solid #005886;
}
.header img {
    max-width: 100px;
}
.header h1 {
    color: #005886;
    font-size: 20px;
    text-transform: uppercase;
}
.content p {
    font-size: 16px;
    color: #333;
}
.list {
    padding-left: 20px;
    margin: 10px 0;
}
.list li {
    margin-bottom: 5px;
}
.footer {
    text-align: center;
    margin-top: 20px;
    padding-top: 10px;
    border-top: 1px solid #ddd;
}
.footer p {
    font-size: 14px;
    color: #666;
}
.footer a {
    color: #005886;
    text-decoration: none;
}

parameters:
    # @todo goto 8?
    level: 7
    paths:
        - ../../../src
    doctrine:
        objectManagerLoader: object-manager-loader.php
        queryBuilderFastAlgorithm: true
    symfony:
        container_xml_path: ../../../var/cache/dev/App_KernelDevDebugContainer.xml
    ignoreErrors:
        - '#but database expects#'
        - identifier: missingType.generics
        - identifier: missingType.iterableValue
    parallel:
        maximumNumberOfProcesses: 4

#!/usr/bin/env bash

DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

if ! [ -d "${DIR}/vendor" ]; then
    echo "it looks like you did not install php-cs-fixer."
    echo "run \"composer install --working-dir=${DIR}\" to fix this error"
    exit
fi

cd "${DIR}/../../../" || exit

./bin/tools/php-cs-fixer/vendor/bin/php-cs-fixer fix \
    --allow-risky=yes \
    --using-cache=no \
    --diff \
    --config=./bin/tools/php-cs-fixer/.php-cs-fixer.php \
    bin/ config/ public/ src/

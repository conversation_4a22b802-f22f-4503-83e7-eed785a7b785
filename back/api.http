POST http://127.0.0.1:8000/api/password-reset-starts
Content-Type: application/ld+json

{
    "email": "<EMAIL>"
}

###

GET http://127.0.0.1:8000/api/password-reset-tokens/****************************************************************************************************************

###

POST http://127.0.0.1:8000/api/password-resets
Content-Type: application/ld+json

{
    "token": "****************************************************************************************************************",
    "password": "&1Aaaaaaa"
}

###

POST http://127.0.0.1:8000/api/tokens
Content-Type: application/ld+json

{
    "email": "<EMAIL>",
    "password": "&1Aaaaaaa"
}

###

GET http://127.0.0.1:8000/api/users/0
X-Sinfin-Token: WyJbMTcyOTY5MTM2MywzNjAwLFwiN2M1ZTc5N2QtNmMxYi00MjQ2LThiMjgtZjcxMjY0M2YzZWZlXCJdIiwiOGVhMWFiNjgxMDgxZmE2OWRkMDFkNjc1ZWQwYWZiMzciXQ==

###

GET http://127.0.0.1:8000/api/header
X-Sinfin-Token: WyJbMTcyOTY5MTM2MywzNjAwLFwiN2M1ZTc5N2QtNmMxYi00MjQ2LThiMjgtZjcxMjY0M2YzZWZlXCJdIiwiOGVhMWFiNjgxMDgxZmE2OWRkMDFkNjc1ZWQwYWZiMzciXQ==

###

POST http://127.0.0.1:8000/api/uploads
X-Sinfin-Token: WyJbMTcyOTY5MTM2MywzNjAwLFwiN2M1ZTc5N2QtNmMxYi00MjQ2LThiMjgtZjcxMjY0M2YzZWZlXCJdIiwiOGVhMWFiNjgxMDgxZmE2OWRkMDFkNjc1ZWQwYWZiMzciXQ==
Content-Type: multipart/form-data; boundary=WebAppBoundary

--WebAppBoundary
Content-Disposition: form-data; name="file"; filename="api.http"
Content-Type: text/plain

< ./api.http
--WebAppBoundary--
